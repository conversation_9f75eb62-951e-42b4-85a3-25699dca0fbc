import React from "react";
import { Google<PERSON><PERSON>, Marker, Autocomplete } from "@react-google-maps/api";
import Select from "react-select";

function LaunchTemplate({
  formData,
  setFormData,
  responders,
  locations,
  previousLocations,
  theme,
  mapsLoaded,
  launchEvent,
  onLoadEventAutocomplete,
  onLoadReportAutocomplete,
  onPlaceChanged,
  setShowMapPicker,
  showMapPicker,
  mapCenter,
  selectedPosition,
  handleMapClick,
  selectedModule,
  mode,
  setMode,
  formConfig,
  error,
  success,
}) {
  const roles = ["staff", "lead", "commander", "viewer"];
  const [role, setRole] = React.useState("");
  const [filteredResponders, setFilteredResponders] = React.useState(responders);
  const [showHelpModal, setShowHelpModal] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  React.useEffect(() => {
    // Filter responders based on the selected role
    if (role) {
      const filtered = responders.filter(
        (responder) => responder.role === role
      );
      setFilteredResponders(filtered);
  
      // Automatically select responders with the selected role
      const selectedIds = filtered.map((responder) => responder.id);
      setFormData((prev) => ({ ...prev, assignedIds: selectedIds }));
    } else {
      setFilteredResponders(responders); // Show all responders if no role selected
      // Don't clear selection when no role is selected - allow manual selection
    }
  }, [role, responders, setFormData]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    if (name.startsWith("location.")) {
      const locationField = name.split(".")[1];
      setFormData((prev) => ({
        ...prev,
        location: { ...prev.location, [locationField]: value },
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? checked : value,
      }));
    }
  };

  const handleResponderChange = (selectedOptions) => {
    const selectedIds = selectedOptions ? selectedOptions.map(option => option.value) : [];
    setFormData((prev) => ({ ...prev, assignedIds: selectedIds }));
  };

  const handleLocationChange = (index, field, value) => {
    const updatedLocations = [...formData.included_report_to_locations];
    updatedLocations[index] = { ...updatedLocations[index], [field]: value };
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: updatedLocations,
    }));
  };

  const addLocation = () => {
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: [
        ...prev.included_report_to_locations,
        {
          commonName: "",
          address: "",
          city: "",
          state: "",
          zip: "",
          primary: false,
          staffNeeded: 2,
          resources: [{ name: "", responderCount: 2, requiredRoles: [] }],
        },
      ],
    }));
  };

  const deleteLocation = (index) => {
    const updatedLocations = formData.included_report_to_locations.filter(
      (_, i) => i !== index
    );
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: updatedLocations,
    }));
  };

  const clearLocation = (index) => {
    const updatedLocations = [...formData.included_report_to_locations];
    updatedLocations[index] = {
      commonName: "",
      address: "",
      city: "",
      state: "",
      zip: "",
      primary: false,
      staffNeeded: 2,
      resources: [{ name: "", responderCount: 2, requiredRoles: [] }],
    };
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: updatedLocations,
    }));
  };

  const handleLocationSelect = (index, value) => {
    if (!value || value === " - " || value === "") {
      return;
    }

    const selectedLocation = locations.find(
      (loc) => `${loc.commonName} - ${loc.address}` === value
    );

    if (selectedLocation) {
      handleLocationChange(index, "commonName", selectedLocation.commonName);
      handleLocationChange(index, "address", selectedLocation.address);
      handleLocationChange(index, "city", selectedLocation.city || "");
      handleLocationChange(index, "state", selectedLocation.state || "");
      handleLocationChange(index, "zip", selectedLocation.zip || "");
    }
  };

  const addResource = (locationIndex) => {
    const updatedLocations = [...formData.included_report_to_locations];
    updatedLocations[locationIndex].resources.push({
      name: "",
      responderCount: 2,
      requiredRoles: [],
    });
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: updatedLocations,
    }));
  };

  const removeResource = (locationIndex, resourceIndex) => {
    const updatedLocations = [...formData.included_report_to_locations];
    updatedLocations[locationIndex].resources = updatedLocations[
      locationIndex
    ].resources.filter((_, i) => i !== resourceIndex);
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: updatedLocations,
    }));
  };

  const handleResourceChange = (locationIndex, resourceIndex, field, value) => {
    const updatedLocations = [...formData.included_report_to_locations];
    updatedLocations[locationIndex].resources[resourceIndex][field] = value;
    setFormData((prev) => ({
      ...prev,
      included_report_to_locations: updatedLocations,
    }));
  };

  const validateForm = (eventData) => {
    // Check if either "Notify All" is selected OR at least 1 user is selected
    const hasNotifyAll = eventData.notifyAllIfUnassigned;
    const hasSelectedUsers = eventData.assignedIds && eventData.assignedIds.length > 0;

    if (!hasNotifyAll && !hasSelectedUsers) {
      alert("Please either select 'Notify All Users' or choose at least one responder to notify.");
      return false;
    }

    return true;
  };

  const handleLaunch = async (eventData, saveAsTemplate = false) => {
    // Validate form before submission
    if (!validateForm(eventData)) {
      return;
    }

    setIsSubmitting(true);
    try {
      await launchEvent(eventData, saveAsTemplate);
    } catch (err) {
      console.error("Failed to launch event:", err);
      alert("Failed to launch event. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const defaultFormConfig = [
    { name: "title", label: "Title", type: "text", required: true },
    { name: "info", label: "Info", type: "textarea", required: false },
    { name: "description", label: "Description", type: "textarea", required: false },
    { name: "scale", label: "Scale", type: "select", required: true },
    { name: "urgency", label: "Urgency", type: "select", required: true },
  ];

  const activeFormConfig = formConfig && formConfig.length > 0 ? formConfig : defaultFormConfig;

  return (
    <>
      <style>
        {`
          /* Mobile Responsive Styles for Launch Event */
          @media (max-width: 768px) {
            .launch-container {
              padding: 10px !important;
            }
            .launch-buttons-container {
              flex-direction: column !important;
              gap: 10px !important;
              margin-bottom: 20px !important;
            }
            .launch-button, .launch-mode-indicator {
              padding: 12px 20px !important;
              font-size: 14px !important;
              width: 100% !important;
              text-align: center !important;
            }
            .launch-form {
              padding: 15px !important;
              margin: 0 !important;
              max-width: 100% !important;
            }
            .launch-form h1 {
              font-size: 20px !important;
              text-align: center !important;
              margin-bottom: 15px !important;
            }
            .form-section {
              padding: 15px !important;
              margin-bottom: 20px !important;
            }
            .form-section h3 {
              font-size: 16px !important;
              margin-bottom: 15px !important;
            }
            .form-grid {
              grid-template-columns: 1fr !important;
              gap: 15px !important;
            }
            .form-input, .form-textarea, .form-select {
              padding: 10px !important;
              font-size: 14px !important;
            }
            .location-grid {
              grid-template-columns: 1fr !important;
              gap: 10px !important;
            }
            .location-actions {
              flex-direction: column !important;
              gap: 8px !important;
            }
            .location-actions button {
              width: 100% !important;
              padding: 8px !important;
              font-size: 12px !important;
            }
            .responder-section {
              padding: 15px !important;
            }
            .submit-buttons {
              flex-direction: column !important;
              gap: 10px !important;
            }
            .submit-buttons button {
              width: 100% !important;
              padding: 12px !important;
              font-size: 14px !important;
            }
          }

          /* Tablet Styles */
          @media (min-width: 769px) and (max-width: 1024px) {
            .launch-container {
              padding: 15px !important;
            }
            .launch-buttons-container {
              gap: 12px !important;
            }
            .launch-button, .launch-mode-indicator {
              padding: 12px 25px !important;
              font-size: 15px !important;
            }
            .launch-form {
              padding: 18px !important;
              max-width: 100% !important;
            }
            .form-grid {
              grid-template-columns: 1fr 1fr !important;
              gap: 15px !important;
            }
            .location-grid {
              grid-template-columns: 1fr 1fr !important;
              gap: 12px !important;
            }
          }

          /* Desktop Styles */
          @media (min-width: 1025px) {
            .launch-container {
              padding: 20px !important;
            }
            .form-grid {
              grid-template-columns: 1fr 1fr 1fr !important;
            }
            .location-grid {
              grid-template-columns: 1fr 1fr 1fr !important;
            }
          }
        `}
      </style>
      <div
        className="launch-container"
        style={{
          padding: "20px",
          fontFamily: "Arial, sans-serif",
          backgroundColor: theme.backgroundColor,
          position: "relative",
        }}
      >
      {/* Top-right status bar */}
      {/* Help Me Launch and Event Launch Mode Buttons */}
      <div className="launch-buttons-container" style={{
        display: "flex",
        justifyContent: "center",
        gap: "15px",
        marginBottom: "30px",
        flexWrap: "wrap"
      }}>
        <button
          className="launch-button"
          onClick={() => setShowHelpModal(true)}
          style={{
            background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
            color: "white",
            border: "none",
            padding: "15px 30px",
            borderRadius: "12px",
            fontSize: "16px",
            fontWeight: "600",
            cursor: "pointer",
            boxShadow: "0 4px 15px rgba(16, 185, 129, 0.4)",
            transition: "all 0.3s ease",
            display: "flex",
            alignItems: "center",
            gap: "10px"
          }}
          onMouseOver={(e) => {
            e.target.style.transform = "translateY(-2px)";
            e.target.style.boxShadow = "0 6px 20px rgba(16, 185, 129, 0.6)";
          }}
          onMouseOut={(e) => {
            e.target.style.transform = "translateY(0)";
            e.target.style.boxShadow = "0 4px 15px rgba(16, 185, 129, 0.4)";
          }}
        >
          🆘 Help Me Launch
        </button>

        <div className="launch-mode-indicator" style={{
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          color: "white",
          padding: "15px 30px",
          borderRadius: "12px",
          fontSize: "16px",
          fontWeight: "600",
          display: "flex",
          alignItems: "center",
          gap: "10px",
          boxShadow: "0 4px 15px rgba(102, 126, 234, 0.4)",
          animation: "pulse 2s infinite"
        }}>
          <span style={{ fontSize: "18px" }}>🚀</span>
          Event Launch Mode
        </div>
      </div>

      <h1
        style={{
          color: mode === "template" ? theme.primaryColor : theme.secondaryColor,
          marginBottom: "20px",
        }}
      >
        {mode === "template" ? "Launch from Template" : "Full Form Launch"}
      </h1>
      <form
        className="launch-form"
        onSubmit={(e) => {
          e.preventDefault();
          handleLaunch({
            ...formData,
            assignedIds: formData.assignedIds.filter((id) =>
              responders.some((r) => r.id === id)
            ),
            module: selectedModule,
          });
        }}
        style={{
          backgroundColor: "#fff",
          padding: "20px",
          borderRadius: "8px",
          boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          maxWidth: "1200px",
          margin: "0 auto",
        }}
      >
        {error && <p style={{ color: "#d32f2f" }}>{error}</p>}
        {success && <p style={{ color: "#388e3c" }}>{success}</p>}



        {/* Event Details Section */}
        <div className="form-section" style={{
          marginBottom: "30px",
          padding: "20px",
          border: "2px solid #e0e0e0",
          borderRadius: "8px",
          backgroundColor: "#fafafa"
        }}>
          <h3 style={{ 
            margin: "0 0 20px 0", 
            color: theme.primaryColor, 
            borderBottom: "2px solid #e0e0e0", 
            paddingBottom: "10px" 
          }}>
            Event Details
          </h3>
          
          {activeFormConfig.filter(field =>
            ["title", "info", "description"].includes(field.name)
          ).map((field) => (
            <div key={field.name} style={{ marginBottom: "15px" }}>
              <label
                style={{ display: "block", marginBottom: "5px", color: "#333", fontWeight: "bold" }}
              >
                {field.label}
                {field.required && <span style={{ color: "red" }}>*</span>}
              </label>
              {field.type === "text" && (
                <input
                  className="form-input"
                  type="text"
                  name={field.name}
                  value={formData[field.name] || ""}
                  onChange={handleInputChange}
                  required={field.required}
                  style={{
                    width: "100%",
                    padding: "12px",
                    borderRadius: "6px",
                    border: "1px solid #ccc",
                    fontSize: "14px",
                    backgroundColor: "#fff",
                  }}
                />
              )}
              {field.type === "textarea" && (
                <textarea
                  className="form-textarea"
                  name={field.name}
                  value={formData[field.name] || ""}
                  onChange={handleInputChange}
                  required={field.required}
                  style={{
                    width: "100%",
                    padding: "12px",
                    borderRadius: "6px",
                    border: "1px solid #ccc",
                    minHeight: "100px",
                    fontSize: "14px",
                    backgroundColor: "#fff",
                  }}
                />
              )}
            </div>
          ))}
        </div>

        {/* Document Upload Section */}
        <div className="form-section" style={{
          marginBottom: "30px",
          padding: "20px",
          border: "2px solid #2196f3",
          borderRadius: "12px",
          background: "linear-gradient(135deg, #e3f2fd 0%, #f1f8ff 100%)",
          boxShadow: "0 4px 12px rgba(33, 150, 243, 0.2)"
        }}>
          <h3 style={{
            margin: "0 0 20px 0",
            color: "#1976d2",
            display: "flex",
            alignItems: "center",
            gap: "10px",
            fontSize: "20px",
            fontWeight: "700"
          }}>
            <span style={{ fontSize: "24px" }}>📁</span>
            Event Documents
          </h3>

          <div style={{
            background: "white",
            padding: "20px",
            borderRadius: "8px",
            border: "2px dashed #90caf9",
            textAlign: "center",
            marginBottom: "15px"
          }}>
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              onChange={(e) => {
                // Handle file upload logic here
                const files = Array.from(e.target.files);
                console.log('Files selected for upload:', files);
                // You can add the files to formData if needed
                setFormData(prev => ({ ...prev, documents: files }));
              }}
              style={{
                width: "100%",
                padding: "15px",
                border: "none",
                borderRadius: "6px",
                fontSize: "14px",
                fontWeight: "500"
              }}
            />
            <p style={{
              fontSize: "14px",
              color: "#1976d2",
              margin: "10px 0 0 0",
              fontWeight: "500"
            }}>
              📎 Upload documents (PDF, Word, Images) - Max 10 files
            </p>
          </div>

          <div style={{
            background: "rgba(33, 150, 243, 0.1)",
            padding: "8px 12px",
            borderRadius: "6px",
            fontSize: "12px",
            color: "#1565c0",
            marginTop: "8px"
          }}>
            <strong>💡 Tips:</strong> Upload procedures, maps, protocols • AI generates action checklists
          </div>
        </div>

        {/* Scale and Urgency Section */}
        <div style={{ 
          marginBottom: "30px", 
          padding: "20px", 
          border: "2px solid #e0e0e0", 
          borderRadius: "8px",
          backgroundColor: "#fafafa"
        }}>
          <h3 style={{ 
            margin: "0 0 20px 0", 
            color: theme.primaryColor, 
            borderBottom: "2px solid #e0e0e0", 
            paddingBottom: "10px" 
          }}>
            Scale and Urgency
          </h3>
          
          <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "20px" }}>
            {activeFormConfig.filter(field => 
              ["scale", "urgency"].includes(field.name)
            ).map((field) => (
              <div key={field.name}>
                <label
                  style={{ display: "block", marginBottom: "8px", color: "#333", fontWeight: "bold" }}
                >
                  {field.label}
                  {field.required && <span style={{ color: "red" }}>*</span>}
                </label>
                <select
                  name={field.name}
                  value={formData[field.name] || ""}
                  onChange={handleInputChange}
                  required={field.required}
                  style={{
                    width: "100%",
                    padding: "12px",
                    borderRadius: "6px",
                    border: "1px solid #ccc",
                    fontSize: "14px",
                    backgroundColor: "#fff",
                  }}
                >
                  <option value="">Select...</option>
                  {(field.name === "scale"
                    ? ["Small", "Medium", "Large"]
                    : field.name === "urgency"
                    ? ["Low", "Medium", "High", "Immediate"]
                    : field.options || []
                  ).map((opt) => (
                    <option key={opt} value={opt}>
                      {opt}
                    </option>
                  ))}
                </select>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Form Fields Section */}
        {activeFormConfig.filter(field =>
          !["title", "info", "description", "scale", "urgency"].includes(field.name)
        ).length > 0 && (
          <div style={{
            marginBottom: "30px",
            padding: "20px",
            border: "2px solid #e0e0e0",
            borderRadius: "8px",
            backgroundColor: "#fafafa"
          }}>
            <h3 style={{
              margin: "0 0 20px 0",
              color: theme.primaryColor,
              borderBottom: "2px solid #e0e0e0",
              paddingBottom: "10px"
            }}>
              Additional Information
            </h3>

            {activeFormConfig.filter(field =>
              !["title", "info", "description", "scale", "urgency"].includes(field.name)
            ).map((field) => (
              <div key={field.name} style={{ marginBottom: "15px" }}>
                <label
                  style={{ display: "block", marginBottom: "5px", color: "#333", fontWeight: "bold" }}
                >
                  {field.label}
                  {field.required && <span style={{ color: "red" }}>*</span>}
                </label>
                {field.type === "text" && !field.nested && (
                  <input
                    type="text"
                    name={field.name}
                    value={formData[field.name] || ""}
                    onChange={handleInputChange}
                    required={field.required}
                    style={{
                      width: "100%",
                      padding: "12px",
                      borderRadius: "6px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                      backgroundColor: "#fff",
                    }}
                  />
                )}
                {field.type === "text" &&
                  field.nested &&
                  field.name.startsWith("location.") && (
                    <input
                      type="text"
                      name={field.name}
                      value={formData.location[field.name.split(".")[1]] || ""}
                      onChange={handleInputChange}
                      required={field.required}
                      style={{
                        width: "100%",
                        padding: "12px",
                        borderRadius: "6px",
                        border: "1px solid #ccc",
                        fontSize: "14px",
                        backgroundColor: "#fff",
                      }}
                    />
                  )}
                {field.type === "textarea" && (
                  <textarea
                    name={field.name}
                    value={formData[field.name] || ""}
                    onChange={handleInputChange}
                    required={field.required}
                    style={{
                      width: "100%",
                      padding: "12px",
                      borderRadius: "6px",
                      border: "1px solid #ccc",
                      minHeight: "100px",
                      fontSize: "14px",
                      backgroundColor: "#fff",
                    }}
                  />
                )}
                {field.type === "select" && (
                  <select
                    name={field.name}
                    value={formData[field.name] || ""}
                    onChange={handleInputChange}
                    required={field.required}
                    style={{
                      width: "100%",
                      padding: "12px",
                      borderRadius: "6px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                      backgroundColor: "#fff",
                    }}
                  >
                    <option value="">Select...</option>
                    {(field.options || []).map((opt) => (
                      <option key={opt} value={opt}>
                        {opt}
                      </option>
                    ))}
                  </select>
                )}
                {field.type === "autocomplete" && mapsLoaded && (
                  <>
                    <Autocomplete
                      onLoad={onLoadEventAutocomplete}
                      onPlaceChanged={() => onPlaceChanged()}
                    >
                      <input
                        type="text"
                        name="location.address"
                        value={formData.location.address || ""}
                        onChange={handleInputChange}
                        required={field.required}
                        placeholder="Search for location..."
                        style={{
                          width: "100%",
                          padding: "12px",
                          borderRadius: "6px",
                          border: "1px solid #ccc",
                          fontSize: "14px",
                          backgroundColor: "#fff",
                        }}
                      />
                    </Autocomplete>
                  </>
                )}
                {field.type === "checkbox" && (
                  <input
                    type="checkbox"
                    name={field.name}
                    checked={formData[field.name]}
                    onChange={handleInputChange}
                    style={{ transform: "scale(1.2)" }}
                  />
                )}
              </div>
            ))}
          </div>
        )}

        {/* Event Location Section */}
        <div className="form-section" style={{
          marginBottom: "30px",
          padding: "20px",
          border: "2px solid #e0e0e0",
          borderRadius: "8px",
          backgroundColor: "#fafafa"
        }}>
          <h3 style={{
            margin: "0 0 20px 0",
            color: theme.primaryColor,
            borderBottom: "2px solid #e0e0e0",
            paddingBottom: "10px"
          }}>
            Event Location
          </h3>

          <div style={{ marginBottom: "20px" }}>
            {/* Location Dropdown */}
            <select
              name="location"
              value={`${formData.location.commonName || ""} - ${formData.location.address || ""}`}
              onChange={(e) => {
                if (e.target.value === " - " || e.target.value === "") {
                  setFormData((prev) => ({
                    ...prev,
                    location: {
                      ...prev.location,
                      commonName: "",
                      address: "",
                      city: "",
                      state: "",
                      zip: "",
                      lat: null,
                      lng: null,
                    },
                  }));
                  return;
                }

                const selectedLocation = locations.find(
                  (loc) => `${loc.commonName} - ${loc.address}` === e.target.value
                );

                if (selectedLocation) {
                  setFormData((prev) => ({
                    ...prev,
                    location: {
                      ...prev.location,
                      commonName: selectedLocation.commonName || "",
                      address: selectedLocation.address || "",
                      city: selectedLocation.city || "",
                      state: selectedLocation.state || "",
                      zip: selectedLocation.zip || "",
                      lat: selectedLocation.lat || null,
                      lng: selectedLocation.lng || null,
                    },
                  }));
                }
              }}
              style={{
                width: "100%",
                padding: "12px 10px",
                borderRadius: "6px",
                border: "1px solid #ccc",
                fontSize: "14px",
                color: "#555",
                backgroundColor: "#fff",
                marginBottom: "10px",
              }}
            >
              <option value=" - ">Select from saved locations...</option>
              {locations && locations.length > 0 ? (
                locations.map((loc, i) => (
                  <option key={i} value={`${loc.commonName} - ${loc.address}`}>
                    {loc.commonName} - {loc.address}
                  </option>
                ))
              ) : (
                <option disabled>No saved locations available</option>
              )}
            </select>

            {/* Google Maps Autocomplete */}
            <div style={{ marginBottom: "10px" }}>
              <label style={{ fontSize: "14px", color: "#666", marginBottom: "5px", display: "block" }}>
                Or search for a new location:
              </label>
              <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                {mapsLoaded ? (
                  <Autocomplete
                    onLoad={onLoadEventAutocomplete}
                    onPlaceChanged={() => onPlaceChanged()}
                  >
                    <input
                      type="text"
                      name="location.address"
                      value={formData.location.address || ""}
                      onChange={handleInputChange}
                      placeholder="Search for location (e.g., 123 Main St, City, State)"
                      style={{
                        flex: "1",
                        padding: "12px",
                        borderRadius: "6px",
                        border: "1px solid #ccc",
                        fontSize: "14px",
                        backgroundColor: "#fff"
                      }}
                    />
                  </Autocomplete>
                ) : (
                  <input
                    type="text"
                    name="location.address"
                    value={formData.location.address || ""}
                    onChange={handleInputChange}
                    placeholder="Enter address manually (Google Maps not loaded)"
                    style={{
                      flex: "1",
                      padding: "12px",
                      borderRadius: "6px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                    }}
                  />
                )}

                {/* Map Picker Button */}
                <button
                  type="button"
                  onClick={() => setShowMapPicker("location")}
                  style={{
                    padding: "12px 16px",
                    backgroundColor: "#28a745",
                    color: "white",
                    border: "none",
                    borderRadius: "6px",
                    fontSize: "14px",
                    fontWeight: "600",
                    cursor: "pointer",
                    whiteSpace: "nowrap",
                    display: "flex",
                    alignItems: "center",
                    gap: "6px"
                  }}
                >
                  🗺️ Pick on Map
                </button>
              </div>
            </div>

            {/* Manual Address Fields */}
            <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "10px", marginBottom: "10px" }}>
              <input
                type="text"
                name="location.commonName"
                value={formData.location.commonName || ""}
                onChange={handleInputChange}
                placeholder="Location Name (e.g., Fire Station 1)"
                style={{
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  fontSize: "14px",
                }}
              />
              <input
                type="text"
                name="location.address"
                value={formData.location.address || ""}
                onChange={handleInputChange}
                placeholder="Street Address"
                style={{
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  fontSize: "14px",
                }}
              />
            </div>

            <div style={{ display: "grid", gridTemplateColumns: "2fr 1fr 1fr", gap: "10px" }}>
              <input
                type="text"
                name="location.city"
                value={formData.location.city || ""}
                onChange={handleInputChange}
                placeholder="City"
                style={{
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  fontSize: "14px",
                }}
              />
              <input
                type="text"
                name="location.state"
                value={formData.location.state || ""}
                onChange={handleInputChange}
                placeholder="State"
                style={{
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  fontSize: "14px",
                }}
              />
              <input
                type="text"
                name="location.zip"
                value={formData.location.zip || ""}
                onChange={handleInputChange}
                placeholder="ZIP"
                style={{
                  padding: "8px",
                  borderRadius: "4px",
                  border: "1px solid #ccc",
                  fontSize: "14px",
                }}
              />
            </div>
          </div>
        </div>

        {/* Report-to Locations Section */}
        <div className="form-section" style={{
          marginBottom: "30px",
          padding: "20px",
          border: "2px solid #e0e0e0",
          borderRadius: "8px",
          backgroundColor: "#fafafa"
        }}>
          <h3 style={{
            margin: "0 0 20px 0",
            color: theme.primaryColor,
            borderBottom: "2px solid #e0e0e0",
            paddingBottom: "10px"
          }}>
            {formData.event_type === "notification_only" ? "Reference Locations (Optional)" : "Report-to Locations"}
          </h3>

          {/* Report-to Locations - Conditional based on event type */}
          {formData.event_type === "notification_only" && (
            <div style={{ marginBottom: "20px", padding: "15px", backgroundColor: "#e8f4f8", borderRadius: "8px", border: "1px solid #bee5eb" }}>
              <h4 style={{ color: "#0c5460", marginBottom: "10px" }}>Notification Only Event</h4>
              <p style={{ color: "#0c5460", margin: 0, fontSize: "14px" }}>
                This is a notification-only event. Responders will be informed but won't need to report to any locations.
                You can still add locations for reference/information purposes.
              </p>
            </div>
          )}

          <div style={{ marginBottom: "20px" }}>
            {formData.included_report_to_locations.map((loc, index) => (
              <div
                key={index}
                style={{
                  border: "1px solid #ddd",
                  padding: "15px",
                  marginBottom: "15px",
                  borderRadius: "8px",
                  boxShadow: "0 2px 5px rgba(0, 0, 0, 0.1)",
                  backgroundColor: "#fafafa",
                }}
              >
                <div
                  className="location-actions"
                  style={{
                    display: "flex",
                    justifyContent: "space-between",
                    marginBottom: "15px",
                  }}
                >
                  <button
                    type="button"
                    onClick={() => deleteLocation(index)}
                    style={{
                      backgroundColor: "#ff4d4f",
                      color: "#fff",
                      padding: "6px 12px",
                      borderRadius: "6px",
                      border: "none",
                      cursor: "pointer",
                      fontSize: "14px",
                    }}
                  >
                    Delete
                  </button>
                  <button
                    type="button"
                    onClick={() => clearLocation(index)}
                    style={{
                      backgroundColor: "#757575",
                      color: "#fff",
                      padding: "6px 12px",
                      borderRadius: "6px",
                      border: "none",
                      cursor: "pointer",
                      fontSize: "14px",
                    }}
                  >
                    Clear
                  </button>
                </div>
                <input
                  type="text"
                  value={loc.commonName}
                  onChange={(e) =>
                    handleLocationChange(index, "commonName", e.target.value)
                  }
                  placeholder="Common Name"
                  style={{
                    width: "100%",
                    padding: "10px",
                    borderRadius: "6px",
                    border: "1px solid #ddd",
                    marginBottom: "10px",
                    fontSize: "14px",
                  }}
                />
                {/* Location Dropdown */}
                <select
                  value={`${loc.commonName} - ${loc.address}`}
                  onChange={(e) => handleLocationSelect(index, e.target.value)}
                  style={{
                    width: "100%",
                    padding: "10px",
                    borderRadius: "6px",
                    border: "1px solid #ddd",
                    marginBottom: "10px",
                    fontSize: "14px",
                    backgroundColor: "#fff",
                  }}
                >
                  <option value="">Select from saved locations...</option>
                  {locations && locations.length > 0 ? (
                    locations.map((savedLoc, i) => (
                      <option key={i} value={`${savedLoc.commonName} - ${savedLoc.address}`}>
                        {savedLoc.commonName} - {savedLoc.address}
                      </option>
                    ))
                  ) : (
                    <option disabled>No saved locations available</option>
                  )}
                </select>

                {/* Google Maps Autocomplete */}
                <div style={{ marginBottom: "10px" }}>
                  <label style={{ fontSize: "14px", color: "#666", marginBottom: "5px", display: "block" }}>
                    Or search for a new location:
                  </label>
                  <div style={{ display: "flex", gap: "10px", alignItems: "center" }}>
                    {mapsLoaded ? (
                      <Autocomplete
                        onLoad={(autoC) => onLoadReportAutocomplete(autoC, index)}
                        onPlaceChanged={() => onPlaceChanged(index)}
                      >
                        <input
                          type="text"
                          value={loc.address || ""}
                          onChange={(e) => handleLocationChange(index, "address", e.target.value)}
                          placeholder="Search for location (e.g., 123 Main St, City, State)"
                          style={{
                            flex: "1",
                            padding: "12px",
                            borderRadius: "6px",
                            border: "1px solid #ccc",
                            fontSize: "14px",
                            backgroundColor: "#fff"
                          }}
                        />
                      </Autocomplete>
                    ) : (
                      <input
                        type="text"
                        value={loc.address || ""}
                        onChange={(e) => handleLocationChange(index, "address", e.target.value)}
                        placeholder="Enter address manually (Google Maps not loaded)"
                        style={{
                          flex: "1",
                          padding: "12px",
                          borderRadius: "6px",
                          border: "1px solid #ccc",
                          fontSize: "14px",
                        }}
                      />
                    )}

                    {/* Map Picker Button */}
                    <button
                      type="button"
                      onClick={() => setShowMapPicker(`report-${index}`)}
                      style={{
                        padding: "12px 16px",
                        backgroundColor: "#28a745",
                        color: "white",
                        border: "none",
                        borderRadius: "6px",
                        fontSize: "14px",
                        fontWeight: "600",
                        cursor: "pointer",
                        whiteSpace: "nowrap",
                        display: "flex",
                        alignItems: "center",
                        gap: "6px"
                      }}
                    >
                      🗺️ Pick on Map
                    </button>
                  </div>
                </div>
                {/* Manual Address Fields */}
                <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "10px", marginBottom: "10px" }}>
                  <input
                    type="text"
                    value={loc.commonName}
                    onChange={(e) =>
                      handleLocationChange(index, "commonName", e.target.value)
                    }
                    placeholder="Location Name (e.g., Fire Station 1)"
                    style={{
                      padding: "8px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                    }}
                  />
                  <input
                    type="text"
                    value={loc.address}
                    onChange={(e) =>
                      handleLocationChange(index, "address", e.target.value)
                    }
                    placeholder="Street Address"
                    style={{
                      padding: "8px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                    }}
                  />
                </div>

                <div style={{ display: "grid", gridTemplateColumns: "2fr 1fr 1fr", gap: "10px", marginBottom: "10px" }}>
                  <input
                    type="text"
                    value={loc.city}
                    onChange={(e) =>
                      handleLocationChange(index, "city", e.target.value)
                    }
                    placeholder="City"
                    style={{
                      padding: "8px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                    }}
                  />
                  <input
                    type="text"
                    value={loc.state}
                    onChange={(e) =>
                      handleLocationChange(index, "state", e.target.value)
                    }
                    placeholder="State"
                    style={{
                      padding: "8px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                    }}
                  />
                  <input
                    type="text"
                    value={loc.zip}
                    onChange={(e) =>
                      handleLocationChange(index, "zip", e.target.value)
                    }
                    placeholder="ZIP"
                    style={{
                      padding: "8px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      fontSize: "14px",
                    }}
                  />
                </div>
                <input
                  type="number"
                  value={loc.staffNeeded}
                  onChange={(e) =>
                    handleLocationChange(index, "staffNeeded", parseInt(e.target.value))
                  }
                  placeholder="Staff Needed"
                  min="1"
                  style={{
                    width: "100%",
                    padding: "10px",
                    borderRadius: "6px",
                    border: "1px solid #ddd",
                    marginBottom: "10px",
                    fontSize: "14px",
                  }}
                />
                <div style={{ marginTop: "15px" }}>
                  <h4 style={{ color: "#333", marginBottom: "10px" }}>Resources:</h4>
                  {loc.resources.map((res, resIndex) => (
                    <div
                      key={resIndex}
                      style={{
                        display: "flex",
                        gap: "10px",
                        marginBottom: "10px",
                        alignItems: "center",
                      }}
                    >
                      <select
                        value={res.name}
                        onChange={(e) =>
                          handleResourceChange(
                            index,
                            resIndex,
                            "name",
                            e.target.value
                          )
                        }
                        style={{
                          flex: "1",
                          padding: "10px",
                          borderRadius: "6px",
                          border: "1px solid #ddd",
                          fontSize: "14px",
                        }}
                      >
                        <option value="">Select Resource</option>
                        {(theme.resources || []).map((resource) => (
                          <option key={resource} value={resource}>
                            {resource}
                          </option>
                        ))}
                      </select>
                      <input
                        type="number"
                        value={res.responderCount}
                        onChange={(e) =>
                          handleResourceChange(
                            index,
                            resIndex,
                            "responderCount",
                            e.target.value
                          )
                        }
                        min="1"
                        placeholder="Count"
                        style={{
                          width: "80px",
                          padding: "10px",
                          borderRadius: "6px",
                          border: "1px solid #ddd",
                          fontSize: "14px",
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => removeResource(index, resIndex)}
                        style={{
                          backgroundColor: "#ff4d4f",
                          color: "#fff",
                          padding: "6px 12px",
                          borderRadius: "6px",
                          border: "none",
                          cursor: "pointer",
                          fontSize: "14px",
                        }}
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => addResource(index)}
                    style={{
                      padding: "10px 20px",
                      backgroundColor: "#6c757d",
                      color: "#fff",
                      borderRadius: "6px",
                      border: "none",
                      cursor: "pointer",
                      fontSize: "14px",
                      marginTop: "10px",
                    }}
                  >
                    Add Resource
                  </button>
                </div>
              </div>
            ))}
            <button
              type="button"
              onClick={addLocation}
              style={{
                padding: "10px 20px",
                backgroundColor: "#28a745",
                color: "#fff",
                borderRadius: "6px",
                border: "none",
                cursor: "pointer",
                fontSize: "14px",
                marginTop: "20px",
              }}
            >
              Add Location
            </button>
          </div>
        </div>

        {/* User Assignment Section */}
        <div className="responder-section" style={{
          marginBottom: "30px",
          padding: "20px",
          border: "2px solid #e0e0e0",
          borderRadius: "8px",
          backgroundColor: "#fafafa"
        }}>
          <h3 style={{
            margin: "0 0 20px 0",
            color: theme.primaryColor,
            borderBottom: "2px solid #e0e0e0",
            paddingBottom: "10px"
          }}>
            User Assignment
          </h3>

          {!formData.notifyAllIfUnassigned && (
            <div style={{ marginBottom: "20px" }}>
              {/* Role Selection */}
              <label style={{
                display: "block",
                marginBottom: "8px",
                fontWeight: "bold",
                color: "#333"
              }}>
                Select Role (Auto-assigns all users of this role):
              </label>
              <select
                value={role}
                onChange={(e) => setRole(e.target.value)}
                style={{
                  width: "100%",
                  padding: "12px",
                  borderRadius: "6px",
                  border: "1px solid #ccc",
                  fontSize: "14px",
                  backgroundColor: "#fff",
                  marginBottom: "15px",
                }}
              >
                <option value="">No Role Selected - Choose Users Manually</option>
                {roles.map((roleOption) => (
                  <option key={roleOption} value={roleOption}>
                    {roleOption.charAt(0).toUpperCase() + roleOption.slice(1)}
                  </option>
                ))}
              </select>

              {/* Assigned Responders */}
              <label style={{
                display: "block",
                marginBottom: "8px",
                fontWeight: "bold",
                color: "#333"
              }}>
                Assigned Responders:
              </label>
              <Select
                options={filteredResponders.map((responder) => ({
                  value: responder.id,
                  label: `${responder.username} (${responder.role})`,
                }))}
                isMulti
                value={filteredResponders
                  .filter((responder) =>
                    formData.assignedIds.includes(responder.id)
                  )
                  .map((responder) => ({
                    value: responder.id,
                    label: `${responder.username} (${responder.role})`,
                  }))}
                onChange={handleResponderChange}
                closeMenuOnSelect={false}
                classNamePrefix="select"
                placeholder="Select responders"
                styles={{
                  control: (provided) => ({
                    ...provided,
                    minHeight: '45px',
                    fontSize: '14px',
                  }),
                  multiValue: (provided) => ({
                    ...provided,
                    backgroundColor: theme.primaryColor + '20',
                  }),
                  multiValueLabel: (provided) => ({
                    ...provided,
                    color: theme.primaryColor,
                  }),
                }}
              />
              <small style={{ color: "#666", fontSize: "12px", display: "block", marginTop: "8px" }}>
                {role ? `All ${role} users are auto-selected. You can remove individuals if needed.` :
                 "Choose users manually or select a role above to auto-assign all users of that role."}
              </small>
            </div>
          )}

          <div style={{ marginBottom: "15px" }}>
            <label style={{
              display: "flex",
              alignItems: "center",
              gap: "8px",
              fontWeight: "bold",
              color: "#333"
            }}>
              <input
                type="checkbox"
                name="notifyAllIfUnassigned"
                checked={formData.notifyAllIfUnassigned || false}
                onChange={handleInputChange}
                style={{ transform: "scale(1.2)" }}
              />
              Notify All Users
            </label>
            <small style={{ color: "#666", fontSize: "12px", display: "block", marginTop: "4px" }}>
              If checked, all users will be notified regardless of individual assignments above.
            </small>
          </div>
        </div>

        {/* Event Type and Configuration Section */}
        <div style={{
          marginBottom: "30px",
          padding: "20px",
          border: "2px solid #e0e0e0",
          borderRadius: "8px",
          backgroundColor: "#fafafa"
        }}>
          <h3 style={{
            margin: "0 0 20px 0",
            color: theme.primaryColor,
            borderBottom: "2px solid #e0e0e0",
            paddingBottom: "10px"
          }}>
            Event Type & Configuration
          </h3>

          {/* Event Type Selector */}
          <div style={{ marginBottom: "20px" }}>
            <label
              style={{
                display: "block",
                fontSize: "16px",
                fontWeight: "bold",
                marginBottom: "8px",
                color: "#333",
              }}
            >
              Event Type:
            </label>
            <select
              name="event_type"
              value={formData.event_type}
              onChange={handleInputChange}
              style={{
                width: "100%",
                padding: "12px 10px",
                borderRadius: "6px",
                border: "1px solid #ccc",
                fontSize: "14px",
                color: "#555",
                backgroundColor: "#fff",
                marginTop: "5px",
              }}
            >
              <option value="response">Response Event</option>
              <option value="notification_only">Notification Only Event</option>
            </select>
            <small style={{ color: "#666", fontSize: "12px", display: "block", marginTop: "4px" }}>
              Response events require responders to report to locations. Notification-only events are for awareness only.
            </small>
          </div>

          {/* Notification Channels Selector */}
          <div style={{ marginBottom: "20px" }}>
            <label
              style={{
                display: "block",
                fontSize: "16px",
                fontWeight: "bold",
                marginBottom: "8px",
                color: "#333",
              }}
            >
              Notification Methods:
            </label>
            <div style={{ display: "flex", flexWrap: "wrap", gap: "10px", marginTop: "8px" }}>
              {["voice_call", "sms", "web_app", "email"].map((channel) => (
                <label key={channel} style={{ display: "flex", alignItems: "center", gap: "6px" }}>
                  <input
                    type="checkbox"
                    checked={formData.notification_channels.includes(channel)}
                    onChange={(e) => {
                      const isChecked = e.target.checked;
                      setFormData((prev) => ({
                        ...prev,
                        notification_channels: isChecked
                          ? [...prev.notification_channels, channel]
                          : prev.notification_channels.filter((c) => c !== channel),
                      }));
                    }}
                  />
                  <span style={{ fontSize: "14px", color: "#555" }}>
                    {channel === "voice_call" ? "Voice Call" :
                     channel === "sms" ? "SMS" :
                     channel === "web_app" ? "Web App" : "Email"}
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Location Update Frequency - Only show for response events */}
          {formData.event_type === "response" && (
            <div style={{ marginBottom: "20px" }}>
              <label
                style={{
                  display: "block",
                  fontSize: "16px",
                  fontWeight: "bold",
                  marginBottom: "8px",
                  color: "#333",
                }}
              >
                Location Update Frequency:
              </label>
              <select
                name="location_update_interval"
                value={formData.location_update_interval}
                onChange={handleInputChange}
                style={{
                  width: "100%",
                  padding: "12px 10px",
                  borderRadius: "6px",
                  border: "1px solid #ccc",
                  fontSize: "14px",
                  color: "#555",
                  backgroundColor: "#fff",
                  marginTop: "5px",
                }}
              >
                <option value={5}>Every 5 seconds</option>
                <option value={10}>Every 10 seconds</option>
                <option value={15}>Every 15 seconds</option>
                <option value={30}>Every 30 seconds</option>
                <option value={60}>Every 1 minute</option>
                <option value={300}>Every 5 minutes</option>
                <option value={600}>Every 10 minutes</option>
              </select>
              <small style={{ color: "#666", fontSize: "12px", display: "block", marginTop: "4px" }}>
                How often responders' locations will be updated during the event.
              </small>
            </div>
          )}
        </div>

        <div className="submit-buttons" style={{ display: "flex", gap: "10px" }}>
          <button
            type="submit"
            disabled={isSubmitting}
            style={{
              padding: "10px 20px",
              backgroundColor: isSubmitting ? "#ccc" : theme.primaryColor,
              color: "#fff",
              borderRadius: "4px",
              border: "none",
              cursor: isSubmitting ? "not-allowed" : "pointer",
              display: "flex",
              alignItems: "center",
              gap: "8px",
            }}
          >
            {isSubmitting && (
              <div style={{
                width: "16px",
                height: "16px",
                border: "2px solid #fff",
                borderTop: "2px solid transparent",
                borderRadius: "50%",
                animation: "spin 1s linear infinite"
              }}></div>
            )}
            {isSubmitting ? "Launching..." : "Launch Event"}
          </button>
          <button
            type="button"
            disabled={isSubmitting}
            onClick={() =>
              handleLaunch(
                {
                  ...formData,
                  assignedIds: formData.assignedIds.filter((id) =>
                    responders.some((r) => r.id === id)
                  ),
                  module: selectedModule,
                },
                true
              )
            }
            style={{
              padding: "10px 20px",
              backgroundColor: isSubmitting ? "#ccc" : theme.secondaryColor,
              color: "#fff",
              borderRadius: "4px",
              border: "none",
              cursor: isSubmitting ? "not-allowed" : "pointer",
              display: "flex",
              alignItems: "center",
              gap: "8px",
            }}
          >
            {isSubmitting && (
              <div style={{
                width: "16px",
                height: "16px",
                border: "2px solid #fff",
                borderTop: "2px solid transparent",
                borderRadius: "50%",
                animation: "spin 1s linear infinite"
              }}></div>
            )}
            {isSubmitting ? "Saving & Launching..." : "Save as Template & Launch"}
          </button>
          <button
            type="button"
            onClick={() => setMode(null)}
            style={{
              padding: "10px 20px",
              backgroundColor: "#666",
              color: "#fff",
              borderRadius: "4px",
              border: "none",
            }}
          >
            Cancel
          </button>
        </div>
      </form>
      
      {showMapPicker !== null && mapsLoaded && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(0,0,0,0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <div
            style={{
              backgroundColor: "#fff",
              padding: "20px",
              borderRadius: "8px",
              width: "80%",
              maxWidth: "800px",
            }}
          >
            <h3>Pick Location on Map</h3>
            <GoogleMap
              mapContainerStyle={{ width: "100%", height: "400px" }}
              center={mapCenter}
              zoom={10}
              onClick={handleMapClick}
            >
              {selectedPosition && <Marker position={selectedPosition} />}
            </GoogleMap>
            <div
              style={{
                display: "flex",
                gap: "10px",
                marginTop: "10px",
                justifyContent: "center",
              }}
            >
              <button
                onClick={() => setShowMapPicker(null)}
                style={{
                  padding: "10px 20px",
                  backgroundColor: theme.primaryColor,
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Confirm
              </button>
              <button
                onClick={() => setShowMapPicker(null)}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#666",
                  color: "#fff",
                  borderRadius: "4px",
                  border: "none",
                }}
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Map Picker Modal */}
      {showMapPicker && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1000,
          }}
        >
          <div
            style={{
              backgroundColor: "white",
              padding: "20px",
              borderRadius: "8px",
              width: "80%",
              maxWidth: "800px",
              height: "70%",
              display: "flex",
              flexDirection: "column",
            }}
          >
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                marginBottom: "15px",
              }}
            >
              <h3 style={{ margin: 0, color: "#333" }}>📍 Pick Location on Map</h3>
              <button
                onClick={() => setShowMapPicker(null)}
                style={{
                  background: "#dc3545",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  padding: "8px 12px",
                  cursor: "pointer",
                  fontSize: "14px",
                }}
              >
                ✕ Close
              </button>
            </div>

            <div style={{ flex: 1, border: "1px solid #ddd", borderRadius: "4px" }}>
              {mapsLoaded && (
                <GoogleMap
                  mapContainerStyle={{ width: "100%", height: "100%" }}
                  center={mapCenter}
                  zoom={13}
                  onClick={handleMapClick}
                >
                  {selectedPosition && <Marker position={selectedPosition} />}
                </GoogleMap>
              )}
            </div>

            <div
              style={{
                marginTop: "15px",
                padding: "10px",
                backgroundColor: "#f8f9fa",
                borderRadius: "4px",
                fontSize: "14px",
                color: "#666",
              }}
            >
              💡 Click anywhere on the map to select a location. The address will be automatically filled.
            </div>
          </div>
        </div>
      )}

      {/* Help Me Launch Modal */}
      {showHelpModal && (
        <div style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          zIndex: 1000
        }}>
          <div style={{
            backgroundColor: "#fff",
            borderRadius: "12px",
            padding: "30px",
            maxWidth: "600px",
            width: "90%",
            maxHeight: "80vh",
            overflowY: "auto",
            boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1)"
          }}>
            <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", marginBottom: "20px" }}>
              <h2 style={{ margin: 0, color: "#1f2937", fontSize: "24px", fontWeight: "700" }}>
                🆘 Help Me Launch
              </h2>
              <button
                onClick={() => setShowHelpModal(false)}
                style={{
                  background: "none",
                  border: "none",
                  fontSize: "24px",
                  cursor: "pointer",
                  color: "#6b7280"
                }}
              >
                ×
              </button>
            </div>

            <div style={{ marginBottom: "20px" }}>
              <h3 style={{ color: "#374151", fontSize: "18px", fontWeight: "600", marginBottom: "10px" }}>
                📋 Quick Start Checklist
              </h3>
              <ul style={{ color: "#6b7280", fontSize: "14px", lineHeight: "1.6", paddingLeft: "20px" }}>
                <li>Set event title and description</li>
                <li>Choose event location using map or address search</li>
                <li>Select responders to notify</li>
                <li>Configure notification methods (SMS, email, app)</li>
                <li>Upload relevant documents for AI analysis</li>
                <li>Set urgency level and scale</li>
                <li>Review and launch your event</li>
              </ul>
            </div>

            <div style={{ marginBottom: "20px" }}>
              <h3 style={{ color: "#374151", fontSize: "18px", fontWeight: "600", marginBottom: "10px" }}>
                ⚡ Pro Tips
              </h3>
              <ul style={{ color: "#6b7280", fontSize: "14px", lineHeight: "1.6", paddingLeft: "20px" }}>
                <li>Use templates for faster launches</li>
                <li>Test notification methods first</li>
                <li>Include clear, actionable instructions</li>
                <li>Set realistic response times</li>
                <li>Upload documents during launch for automatic AI summarization</li>
              </ul>
            </div>

            <div style={{ marginBottom: "20px" }}>
              <h3 style={{ color: "#374151", fontSize: "18px", fontWeight: "600", marginBottom: "10px" }}>
                📄 Document Upload Tips
              </h3>
              <ul style={{ color: "#6b7280", fontSize: "14px", lineHeight: "1.6", paddingLeft: "20px" }}>
                <li>Include emergency procedures and contact lists</li>
                <li>Add maps, floor plans, or site diagrams</li>
                <li>Upload safety protocols and equipment manuals</li>
                <li>AI will analyze documents and generate action checklists</li>
                <li>Documents will appear in dashboard with summaries</li>
              </ul>
            </div>

            <div style={{ display: "flex", gap: "10px", justifyContent: "flex-end" }}>
              <button
                onClick={() => setShowHelpModal(false)}
                style={{
                  padding: "10px 20px",
                  backgroundColor: "#6b7280",
                  color: "white",
                  border: "none",
                  borderRadius: "6px",
                  fontSize: "14px",
                  fontWeight: "500",
                  cursor: "pointer"
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* CSS for animations */}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.7;
          }
        }
        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
    </>
  );
}

export default LaunchTemplate;
