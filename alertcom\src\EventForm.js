import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import Footer from "./Footer";
import { useUseType } from "./context/UseTypeContext";
import { themes } from "./themes";
import LaunchTemplate from "./LaunchTemplate";
import HelpMeLaunch from "./HelpMeLaunch";
import "./styles.css";

const baseUrl = process.env.REACT_APP_BASE_URL;

function EventForm({ token, mapsLoaded }) {
  const { selectedModule, formConfig, locations: contextLocations } = useUseType();
  const theme = themes[selectedModule] || themes.EMS;
  const [mode, setMode] = useState(null);
  const [templates, setTemplates] = useState([]);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [responders, setResponders] = useState([]);
  const [previousLocations, setPreviousLocations] = useState([]);
  const [locations, setLocations] = useState([]);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Consolidated form state
  const [formData, setFormData] = useState({
    title: "",
    info: "",
    description: "",
    scale: "",
    urgency: "",
    location: { commonName: "", address: "", city: "", state: "", zip: "" },
    event_type: "response", // New field: 'response' or 'notification_only'
    location_update_interval: 60, // New field: geolocation frequency in seconds
    notification_channels: ["web_app"], // New field: notification methods
    event_documents: [], // New field: uploaded documents for AI actions
    included_report_to_locations: [
      {
        commonName: "",
        address: "",
        city: "",
        state: "",
        zip: "",
        primary: false,
        staffNeeded: 2,
        resources: [{ name: "", responderCount: 2, requiredRoles: [] }],
      },
    ],
    assignedIds: [],
    notifyAllIfUnassigned: false,
    customFields: {},
  });

  const eventAutocompleteRef = useRef(null);
  const reportAutocompletesRef = useRef([]);
  const [showMapPicker, setShowMapPicker] = useState(null);
  const [mapCenter, setMapCenter] = useState({ lat: 43.8349, lng: -91.3188 });
  const [selectedPosition, setSelectedPosition] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [respondersRes, templatesRes, commonLocationsRes] = await Promise.all([
          fetch(`${baseUrl}/responders`, {
            headers: { Authorization: `Bearer ${token}` },
          }),
          fetch(`${baseUrl}/templates`, {
            headers: { Authorization: `Bearer ${token}` },
          }),
          fetch(`${baseUrl}/common-locations`, {
            headers: { Authorization: `Bearer ${token}` },
          }),
        ]);
        if (!respondersRes.ok || !templatesRes.ok)
          throw new Error("Failed to fetch data");
        const respondersData = await respondersRes.json();
        const templatesData = await templatesRes.json();

        setResponders(respondersData);
        setTemplates(templatesData);

        // Extract locations from templates for previousLocations (for autocomplete)
        const locs = templatesData
          .flatMap((t) => t.config?.included_report_to_locations || [])
          .map((loc) => ({
            address: loc.address || loc.location || "",
            city: loc.city || "",
            state: loc.state || "",
            zip: loc.zip || "",
          }));
        setPreviousLocations([
          ...new Set(
            locs.map((l) =>
              `${l.address}, ${l.city}, ${l.state} ${l.zip}`.trim()
            )
          ),
        ]);

        // Extract unique locations from templates for dropdown
        const templateLocations = templatesData
          .flatMap((t) => t.config?.included_report_to_locations || [])
          .filter((loc) => loc.commonName && loc.address) // Only include locations with both name and address
          .map((loc) => ({
            commonName: loc.commonName,
            address: loc.address,
            city: loc.city || "",
            state: loc.state || "",
            zip: loc.zip || "",
          }));

        // Get common locations if available
        let commonLocations = [];
        if (commonLocationsRes.ok) {
          const commonLocationsData = await commonLocationsRes.json();
          commonLocations = commonLocationsData.map(loc => ({
            commonName: loc.name,
            address: loc.address,
            city: loc.city || "",
            state: loc.state || "",
            zip: loc.zip || ""
          }));
        }

        // Combine and deduplicate locations (include context locations from Settings)
        const allLocations = [...templateLocations, ...commonLocations, ...contextLocations];
        const uniqueLocations = allLocations.filter((loc, index, self) =>
          index === self.findIndex(l => l.commonName === loc.commonName && l.address === loc.address)
        );



        setLocations(uniqueLocations);
      } catch (err) {
        console.error("Error fetching data:", err);
        setError(err.message);
      }
    };
    if (token) fetchData();
  }, [token]);

  // Update locations when context locations change
  useEffect(() => {
    if (contextLocations && contextLocations.length > 0) {
      setLocations(prevLocations => {
        const allLocations = [...prevLocations, ...contextLocations];
        const uniqueLocations = allLocations.filter((loc, index, self) =>
          index === self.findIndex(l => l.commonName === loc.commonName && l.address === loc.address)
        );

        return uniqueLocations;
      });
    }
  }, [contextLocations]);

  useEffect(() => {
    reportAutocompletesRef.current = Array(
      formData.included_report_to_locations.length
    ).fill(null);
  }, [formData.included_report_to_locations.length]);

  const launchEvent = async (eventData, saveAsTemplate = false) => {
    // const toastId = toast.loading("Creating event...");
    try {
      // Extract documents from eventData before sending
      const { documents, ...eventDataWithoutDocs } = eventData;

      const response = await fetch(`${baseUrl}/events`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(eventDataWithoutDocs),
      });
      if (!response.ok)
        throw new Error(`Failed to launch event: ${response.status}`);
      const event = await response.json();
      console.log("Event launched:", event);

      // Upload documents if any exist
      if (documents && documents.length > 0) {
        console.log("Uploading documents:", documents);
        const formData = new FormData();

        for (let i = 0; i < documents.length; i++) {
          formData.append('documents', documents[i]);
        }

        const uploadResponse = await fetch(`${baseUrl}/events/${event.id}/documents`, {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` },
          body: formData
        });

        if (uploadResponse.ok) {
          console.log('Documents uploaded successfully!');
        } else {
          const error = await uploadResponse.json();
          console.error('Document upload failed:', error);
          // Don't throw error here, event was created successfully
        }
      }

      if (saveAsTemplate) {
        const templateResponse = await fetch(`${baseUrl}/templates`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name: `${eventData.title} Template`,
            description: "Auto-saved from event launch",
            config: eventDataWithoutDocs, // Don't include documents in template
          }),
        });
        if (!templateResponse.ok) throw new Error("Failed to save template");
        console.log("Template saved:", await templateResponse.json());
      }

      // toast.update(toastId, {
      //   render: "Event created successfully!",
      //   type: "success",
      //   isLoading: false,
      //   autoClose: 3000,
      // });
      setSuccess("Event launched successfully!");
      setTimeout(() => {
        const currentToken = localStorage.getItem("token");
        if (!currentToken) {
          console.log("Token missing at navigation time, redirecting to login");
          navigate("/login");
        } else {
          console.log("Navigating to dashboard with eventId:", event.id);
          navigate(`/dashboard/${event.id}`);
        }
      }, 1000);
    } catch (err) {
      console.error("Error launching event:", err);
      // toast.update(toastId, {
      //   render: `Failed to create event: ${err.message}`,
      //   type: "error",
      //   isLoading: false,
      //   autoClose: 3000,
      // });
      setError(err.message);
      setTimeout(() => setError(null), 3000);
    }
  };

  const onLoadEventAutocomplete = useCallback((autoC) => {
    eventAutocompleteRef.current = autoC;
  }, []);

  const onLoadReportAutocomplete = useCallback((autoC, index) => {
    reportAutocompletesRef.current[index] = autoC;
  }, []);

  const onPlaceChanged = useCallback((index = -1) => {
    const autoC =
      index === -1
        ? eventAutocompleteRef.current
        : reportAutocompletesRef.current[index];
    if (!autoC) return;
    const place = autoC.getPlace();
    if (!place || !place.geometry) return;
    const addressComponents = place.address_components || [];
    const getComponent = (type) =>
      addressComponents.find((c) => c.types.includes(type))?.short_name || "";
    const streetNumber = getComponent("street_number");
    const route = getComponent("route");
    const address =
      streetNumber && route
        ? `${streetNumber} ${route}`
        : streetNumber || route || "";
    const city = getComponent("locality");
    const state = getComponent("administrative_area_level_1");
    const zip = getComponent("postal_code");
    if (place.geometry && place.geometry.location) {
      setMapCenter({
        lat: place.geometry.location.lat(),
        lng: place.geometry.location.lng(),
      });
    }
    if (index === -1) {
      setFormData((prev) => ({
        ...prev,
        location: { ...prev.location, address, city, state, zip },
      }));
    } else {
      setFormData((prev) => {
        const newLocs = [...prev.included_report_to_locations];
        newLocs[index] = { ...newLocs[index], address, city, state, zip };
        return { ...prev, included_report_to_locations: newLocs };
      });
    }
  }, []);

  const handleMapClick = (event) => {
    const lat = event.latLng.lat();
    const lng = event.latLng.lng();

    setSelectedPosition({ lat, lng });
    setMapCenter({ lat, lng });
    const geocoder = new window.google.maps.Geocoder();

    geocoder.geocode({ location: { lat, lng } }, (results, status) => {
      if (status === "OK" && results[0]) {
        const place = results[0];
        const addressComponents = place.address_components || [];
        const getComponent = (type) =>
          addressComponents.find((c) => c.types.includes(type))?.short_name ||
          "";

        const streetNumber = getComponent("street_number");
        const route = getComponent("route");
        const address =
          streetNumber && route
            ? `${streetNumber} ${route}`
            : streetNumber || route || "";
        const city = getComponent("locality");
        const state = getComponent("administrative_area_level_1");
        const zip = getComponent("postal_code");
        if (showMapPicker === null) {
          setFormData((prev) => ({
            ...prev,
            location: { ...prev.location, address, city, state, zip },
          }));
        } else {
          setFormData((prev) => {
            const newLocs = [...prev.included_report_to_locations];
            newLocs[showMapPicker] = {
              ...newLocs[showMapPicker],
              address,
              city,
              state,
              zip,
            };
            return { ...prev, included_report_to_locations: newLocs };
          });
        }
        setShowMapPicker(null);
      } else {
        setError("Failed to get address from map selection");
        setTimeout(() => setError(null), 3000);
      }
    });
  };

  if (!mode) {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          backgroundColor: theme.backgroundColor,
        }}
      >
        <div style={{ flex: "1" }}>
          <div
            className="container"
            style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}
          >
            <h1 style={{ color: theme.primaryColor, marginBottom: "30px" }}>
              Launch Event
            </h1>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
                gap: "20px",
              }}
            >
              <OptionCard
                title="Launch from Template"
                description="Load event criteria from a saved template. Modify as needed before launching."
                color={theme.primaryColor}
                onClick={() => setMode("template")}
              />
              <OptionCard
                title="Help Me Launch"
                description="Step-by-step guide to launch an event. Save as a template at the end."
                color={theme.secondaryColor}
                onClick={() => setMode("guided")}
              />
              <OptionCard
                title="Full Form Launch"
                description="Fill out the complete event form in one go."
                color={theme.primaryColor}
                onClick={() => setMode("full")}
              />
            </div>
          </div>
        </div>
        <Footer token={token} />
      </div>
    );
  }

  if (mode === "template" && !selectedTemplate) {
    const groupedTemplates = {
      MCI: templates.filter((t) => t.name.toLowerCase().includes("mci")),
      PAIP: templates.filter((t) => t.name.toLowerCase().includes("paip")),
      Routine: templates.filter((t) =>
        t.name.toLowerCase().includes("routine")
      ),
      Test: templates.filter((t) => t.name.toLowerCase().includes("test")),
      Other: templates.filter(
        (t) =>
          !["mci", "paip", "routine", "test"].some((g) =>
            t.name.toLowerCase().includes(g)
          )
      ),
    };

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          minHeight: "100vh",
          backgroundColor: theme.backgroundColor,
        }}
      >
        <div style={{ flex: "1" }}>
          <div
            className="container"
            style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}
          >
            <h1 style={{ color: theme.primaryColor, marginBottom: "20px" }}>
              Select a Template
            </h1>
            {Object.entries(groupedTemplates).map(
              ([group, groupTemplates]) =>
                groupTemplates.length > 0 && (
                  <div key={group} style={{ marginBottom: "20px" }}>
                    <h2 style={{ color: "#333", marginBottom: "10px" }}>
                      {group}
                    </h2>
                    <div
                      style={{
                        display: "grid",
                        gridTemplateColumns:
                          "repeat(auto-fill, minmax(250px, 1fr))",
                        gap: "15px",
                      }}
                    >
                      {groupTemplates.map((template) => (
                        <div
                          key={template.id}
                          style={{
                            padding: "15px",
                            backgroundColor: "#fff",
                            borderRadius: "8px",
                            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                            cursor: "pointer",
                            transition: "transform 0.2s",
                          }}
                          onClick={() => {
                            setFormData({
                              title: template.config.title || "",
                              info: template.config.info || "",
                              description: template.config.description || "",
                              scale: template.config.scale || "",
                              urgency: template.config.urgency || "",
                              location: template.config.location || {
                                commonName: "",
                                address: "",
                                city: "",
                                state: "",
                                zip: "",
                              },
                              event_type: template.config.event_type || "response",
                              location_update_interval: template.config.location_update_interval || 60,
                              notification_channels: template.config.notification_channels || ["web_app"],
                              event_documents: template.config.event_documents || [],
                              included_report_to_locations: template.config
                                .included_report_to_locations || [
                                {
                                  commonName: "",
                                  address: "",
                                  city: "",
                                  state: "",
                                  zip: "",
                                  primary: false,
                                  staffNeeded: 2,
                                  resources: [
                                    {
                                      name: "",
                                      responderCount: 2,
                                      requiredRoles: [],
                                    },
                                  ],
                                },
                              ],
                              assignedIds: template.config.assignedIds || [],
                              notifyAllIfUnassigned:
                                template.config.notifyAllIfUnassigned || false,
                              customFields: template.config.customFields || {},
                            });
                            setSelectedTemplate(template.config);
                          }}
                          onMouseEnter={(e) =>
                            (e.currentTarget.style.transform = "scale(1.02)")
                          }
                          onMouseLeave={(e) =>
                            (e.currentTarget.style.transform = "scale(1)")
                          }
                        >
                          <h3
                            style={{
                              color: theme.primaryColor,
                              margin: "0 0 10px",
                            }}
                          >
                            {template.name}
                          </h3>
                          <p style={{ color: "#666", margin: 0 }}>
                            {template.description || "No description"}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )
            )}
            <button
              onClick={() => setMode(null)}
              className="btn-secondary"
              style={{
                marginTop: "20px",
                backgroundColor: theme.secondaryColor,
                color: "#fff",
              }}
            >
              Back
            </button>
          </div>
        </div>
        <Footer token={token} />
      </div>
    );
  }

  if (mode === "guided") {
    return (
      <HelpMeLaunch
        formData={formData}
        setFormData={setFormData}
        formConfig={formConfig}
        responders={responders}
        locations={locations}
        previousLocations={previousLocations}
        theme={theme}
        mapsLoaded={mapsLoaded}
        launchEvent={launchEvent}
        onLoadEventAutocomplete={onLoadEventAutocomplete}
        onLoadReportAutocomplete={onLoadReportAutocomplete}
        onPlaceChanged={onPlaceChanged}
        setShowMapPicker={setShowMapPicker}
        showMapPicker={showMapPicker}
        mapCenter={mapCenter}
        selectedPosition={selectedPosition}
        handleMapClick={handleMapClick}
        selectedModule={selectedModule}
        setMode={setMode}
        token={token}
      />
    );
  }

  return (
    <div
      style={{
        display: "flex",
        flexDirection: "column",
        minHeight: "100vh",
        backgroundColor: theme.backgroundColor,
      }}
    >
      <div style={{ flex: "1" }}>
        <LaunchTemplate
          formData={formData}
          setFormData={setFormData}
          responders={responders}
          locations={locations}
          previousLocations={previousLocations}
          theme={theme}
          mapsLoaded={mapsLoaded}
          launchEvent={launchEvent}
          onLoadEventAutocomplete={onLoadEventAutocomplete}
          onLoadReportAutocomplete={onLoadReportAutocomplete}
          onPlaceChanged={onPlaceChanged}
          setShowMapPicker={setShowMapPicker}
          showMapPicker={showMapPicker}
          mapCenter={mapCenter}
          selectedPosition={selectedPosition}
          handleMapClick={handleMapClick}
          selectedModule={selectedModule}
          mode={mode}
          setMode={setMode}
          formConfig={formConfig}
          error={error}
          success={success}
        />
      </div>
      <Footer token={token} />
    </div>
  );
}

function OptionCard({ title, description, color, onClick }) {
  return (
    <div
      style={{
        padding: "20px",
        backgroundColor: "#fff",
        borderRadius: "8px",
        boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
        textAlign: "center",
        cursor: "pointer",
        transition: "transform 0.2s, box-shadow 0.2s",
      }}
      onClick={onClick}
      onMouseEnter={(e) => {
        e.currentTarget.style.transform = "scale(1.05)";
        e.currentTarget.style.boxShadow = "0 6px 12px rgba(0,0,0,0.2)";
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.transform = "scale(1)";
        e.currentTarget.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
      }}
    >
      <h3 style={{ color, margin: "0 0 10px", fontSize: "1.5em" }}>{title}</h3>
      <p style={{ color: "#666", margin: 0 }}>{description}</p>
    </div>
  );
}

export default EventForm;
