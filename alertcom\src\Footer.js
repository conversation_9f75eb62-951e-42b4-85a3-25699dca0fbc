// src/Footer.js
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { jwtDecode } from 'jwt-decode';
import './styles.css';

function Footer({ token }) {
    const [role, setRole] = useState(localStorage.getItem('role') || '');

    useEffect(() => {
        if (token) {
            try {
                const decoded = jwtDecode(token);
                setRole(decoded.role);
                localStorage.setItem('role', decoded.role);
            } catch (error) {
                console.error('Invalid token:', error);
                setRole('');
                localStorage.removeItem('role');
            }
        } else {
            setRole('');
            localStorage.removeItem('role');
        }
    }, [token]);

    return (
        <footer style={{
            backgroundColor: '#1a73e8',
            color: '#fff',
            padding: '30px 20px',
            textAlign: 'center',
            marginTop: 'auto'
        }}>
            <div className="footer-content" style={{ maxWidth: '1200px', margin: '0 auto' }}>
                <div className="footer-logo" style={{ marginBottom: '20px' }}>
                    <a
                        href="http://www.emsmg.com"
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ textDecoration: 'none' }}
                        onMouseEnter={e => {
                            e.currentTarget.style.transform = 'scale(1.05)';
                            e.currentTarget.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
                        }}
                        onMouseLeave={e => {
                            e.currentTarget.style.transform = 'scale(1)';
                            e.currentTarget.style.boxShadow = 'none';
                        }}
                    >
                        <div style={{
                            display: 'inline-block',
                            backgroundColor: '#fff',
                            padding: '5px 10px',
                            borderRadius: '5px',
                            transition: 'transform 0.2s, box-shadow 0.2s',
                        }}>
                            <img
                                src="/emsmg.png"
                                alt="EMS Management Group Logo"
                                style={{
                                    height: '50px',
                                    maxWidth: '200px',
                                    objectFit: 'contain'
                                }}
                            />
                        </div>
                    </a>
                </div>
                <div className="footer-links" style={{ display: 'flex', justifyContent: 'center', gap: '20px', marginBottom: '20px' }}>
                    <Link to="/" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-base)', transition: 'color 0.2s' }}
                        onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                        onMouseLeave={e => e.target.style.color = '#fff'}
                    >
                        Home
                    </Link>
                    {(role === 'commander' || role === 'lead' || role === 'staff') && (
                        <>
                            <Link to="/events" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-base)', transition: 'color 0.2s' }}
                                onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                                onMouseLeave={e => e.target.style.color = '#fff'}
                            >
                                Events
                            </Link>
                            <Link to="/dashboard" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-base)', transition: 'color 0.2s' }}
                                onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                                onMouseLeave={e => e.target.style.color = '#fff'}
                            >
                                Dashboard
                            </Link>
                        </>
                    )}
                    {role === 'commander' && (
                        <>
                            <Link to="/launch-event" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-base)', transition: 'color 0.2s' }}
                                onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                                onMouseLeave={e => e.target.style.color = '#fff'}
                            >
                                Launch Event
                            </Link>
                            <Link to="/templates" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-base)', transition: 'color 0.2s' }}
                                onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                                onMouseLeave={e => e.target.style.color = '#fff'}
                            >
                                Templates
                            </Link>
                            <Link to="/user-management" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-base)', transition: 'color 0.2s' }}
                                onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                                onMouseLeave={e => e.target.style.color = '#fff'}
                            >
                                User Management
                            </Link>
                            <Link to="/reporting" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-base)', transition: 'color 0.2s' }}
                                onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                                onMouseLeave={e => e.target.style.color = '#fff'}
                            >
                                Reporting
                            </Link>
                        </>
                    )}
                </div>
                <div className="footer-info" style={{ marginBottom: '20px' }}>
                    <p className="tagline" style={{ fontSize: 'var(--font-size-base)', fontStyle: 'italic', marginBottom: '10px' }}>
                        Empowering Emergency Response with AlertComm1
                    </p>
                    <p className="contact" style={{ fontSize: 'var(--font-size-small)', marginBottom: '10px' }}>
                        Contact Us: <a href="mailto:<EMAIL>" style={{ color: '#e6f0fa', textDecoration: 'none' }}
                            onMouseEnter={e => e.target.style.textDecoration = 'underline'}
                            onMouseLeave={e => e.target.style.textDecoration = 'none'}
                        >
                            <EMAIL>
                        </a> | (555) 123-4567
                    </p>
                    <div className="social-links" style={{ display: 'flex', justifyContent: 'center', gap: '15px' }}>
                        <a href="https://facebook.com" target="_blank" rel="noopener noreferrer" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-small)' }}
                            onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                            onMouseLeave={e => e.target.style.color = '#fff'}
                        >
                            Facebook
                        </a>
                        <a href="https://twitter.com" target="_blank" rel="noopener noreferrer" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-small)' }}
                            onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                            onMouseLeave={e => e.target.style.color = '#fff'}
                        >
                            Twitter
                        </a>
                        <a href="https://linkedin.com" target="_blank" rel="noopener noreferrer" style={{ color: '#fff', textDecoration: 'none', fontSize: 'var(--font-size-small)' }}
                            onMouseEnter={e => e.target.style.color = '#e6f0fa'}
                            onMouseLeave={e => e.target.style.color = '#fff'}
                        >
                            LinkedIn
                        </a>
                    </div>
                </div>
                <div className="copyright" style={{ fontSize: 'var(--font-size-small)', marginTop: '10px' }}>
                    © 2025 EMS Management Group, LLC. All rights reserved.
                </div>
            </div>
        </footer>
    );
}

export default Footer;