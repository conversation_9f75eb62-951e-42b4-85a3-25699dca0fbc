-- Test script to verify the new database schema
-- Run this to check if all new tables and columns exist

-- Check if ai_prompts and ai_preconditions columns exist in company_settings
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'company_settings' 
AND column_name IN ('ai_prompts', 'ai_preconditions');

-- Check if checklist_templates table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_name = 'checklist_templates';

-- Check if document_questions table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_name = 'document_questions';

-- Check if document_summaries table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_name = 'document_summaries';

-- Show structure of new tables
\d checklist_templates;
\d document_questions;
\d document_summaries;

-- Test inserting sample data
INSERT INTO checklist_templates (name, description, template_data, created_by) 
VALUES ('Test Template', 'Test Description', '["Item 1", "Item 2"]', 1);

-- Test AI settings
UPDATE company_settings 
SET ai_prompts = '{"document_summary": "Test prompt"}', 
    ai_preconditions = '{"document_qa_prompt": "Test QA prompt"}' 
WHERE company_id = 1;

-- Verify the data
SELECT * FROM checklist_templates;
SELECT ai_prompts, ai_preconditions FROM company_settings WHERE company_id = 1;
