// src/socket.js
import io from "socket.io-client";
import config from './config'; 
const { baseUrl } = config;

const socket = io(`${baseUrl}`, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
  reconnectionDelay: 1000,
});

socket.on("connect", () => {
  console.log("Connected to Socket.IO server");
});

socket.on("connect_error", (error) => {
  console.error("Socket.IO connection error:", error);
});

socket.on("new-event", (event) => {
  console.log("New event received:", event);
  // Handle the new event (e.g., update UI, show notification)
});

export default socket;
