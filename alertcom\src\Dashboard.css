.dashboard-container {
    display: grid;
    grid-template-areas: 
        "event-overview all-responders"
        "map-section responder-groups"
        "chat-section tbd-section";
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
    gap: 20px;
    height: 100vh;
    padding: 20px;
    background: #1a1a1a; /* Dark theme */
    color: #ffffff;
    font-family: 'Arial', sans-serif;
}

.event-overview {
    grid-area: event-overview;
    background: #2a2a2a;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px #00ffcc; /* Neon accent */
}

.all-responders-overview {
    grid-area: all-responders;
    background: #2a2a2a;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px #00ffcc;
}

.map-section {
    grid-area: map-section;
    background: #2a2a2a;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px #00ffcc;
}

.responder-groups {
    grid-area: responder-groups;
    background: #2a2a2a;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px #00ffcc;
}

.chat-section {
    grid-area: chat-section;
    background: #2a2a2a;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px #00ffcc;
}

.tbd-section {
    grid-area: tbd-section;
    background: #2a2a2a;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 0 10px #00ffcc;
}

h2 {
    color: #00ffcc; /* Neon cyan */
    margin-bottom: 15px;
}

p, li {
    color: #ffffff;
    line-height: 1.5;
}

.responder-table {
    width: 100%;
    border-collapse: collapse;
    background: #333333;
}

.responder-table th, .responder-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #00ffcc;
}

.responder-table th {
    background: #404040;
    color: #00ffcc;
}

.alert {
    background: #ff3366; /* Neon pink for alerts */
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.map-placeholder, .chat-placeholder, .tbd-placeholder {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #404040;
    color: #00ffcc;
    font-size: 18px;
    border-radius: 5px;
}

ul {
    list-style: none;
    padding: 0;
}

li {
    margin-bottom: 10px;
}