import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';
import config from './config';

const { baseUrl } = config;

function ChecklistStatus({ token }) {
  const { eventId } = useParams();
  const [actionItems, setActionItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [users, setUsers] = useState({});
  const [filterRole, setFilterRole] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');

  useEffect(() => {
    if (eventId && token) {
      loadActionItems();
      loadUsers();
    }
  }, [eventId, token]);

  const loadActionItems = async () => {
    try {
      const response = await fetch(`${baseUrl}/events/${eventId}/action-items`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        setActionItems(data);
      } else {
        throw new Error('Failed to load action items');
      }
    } catch (error) {
      console.error('Error loading action items:', error);
      toast.error('Failed to load checklist status');
    } finally {
      setLoading(false);
    }
  };

  const loadUsers = async () => {
    try {
      const response = await fetch(`${baseUrl}/users`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      
      if (response.ok) {
        const data = await response.json();
        const userMap = {};
        data.forEach(user => {
          userMap[user.id] = user;
        });
        setUsers(userMap);
      }
    } catch (error) {
      console.error('Error loading users:', error);
    }
  };

  const getFilteredItems = () => {
    return actionItems.filter(item => {
      const roleMatch = filterRole === 'all' || item.user_role === filterRole;
      const statusMatch = filterStatus === 'all' || 
        (filterStatus === 'completed' && item.is_completed) ||
        (filterStatus === 'pending' && !item.is_completed);
      return roleMatch && statusMatch;
    });
  };

  const getCompletionStats = () => {
    const total = actionItems.length;
    const completed = actionItems.filter(item => item.is_completed).length;
    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
    return { total, completed, percentage };
  };

  const getRoleStats = () => {
    const roleStats = {};
    actionItems.forEach(item => {
      const role = item.user_role || 'Unknown';
      if (!roleStats[role]) {
        roleStats[role] = { total: 0, completed: 0 };
      }
      roleStats[role].total++;
      if (item.is_completed) {
        roleStats[role].completed++;
      }
    });
    return roleStats;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <div style={{ fontSize: '18px', color: '#666' }}>Loading checklist status...</div>
      </div>
    );
  }

  const filteredItems = getFilteredItems();
  const stats = getCompletionStats();
  const roleStats = getRoleStats();

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '30px' }}>
        <h1 style={{ fontSize: '28px', fontWeight: '600', color: '#1f2937', marginBottom: '10px' }}>
          Checklist Status
        </h1>
        <p style={{ color: '#6b7280', fontSize: '16px' }}>
          Real-time completion status for all checklists in this event
        </p>
      </div>

      {/* Overall Stats */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '30px'
      }}>
        <div style={{
          padding: '20px',
          backgroundColor: '#f8fafc',
          borderRadius: '12px',
          border: '1px solid #e2e8f0',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', fontWeight: '700', color: '#3b82f6' }}>
            {stats.percentage}%
          </div>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>Overall Completion</div>
        </div>
        
        <div style={{
          padding: '20px',
          backgroundColor: '#f0fdf4',
          borderRadius: '12px',
          border: '1px solid #bbf7d0',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', fontWeight: '700', color: '#22c55e' }}>
            {stats.completed}
          </div>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>Completed Tasks</div>
        </div>
        
        <div style={{
          padding: '20px',
          backgroundColor: '#fefce8',
          borderRadius: '12px',
          border: '1px solid #fde047',
          textAlign: 'center'
        }}>
          <div style={{ fontSize: '32px', fontWeight: '700', color: '#eab308' }}>
            {stats.total - stats.completed}
          </div>
          <div style={{ fontSize: '14px', color: '#6b7280' }}>Pending Tasks</div>
        </div>
      </div>

      {/* Role Stats */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ fontSize: '20px', fontWeight: '600', color: '#1f2937', marginBottom: '15px' }}>
          Progress by Role
        </h2>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '15px'
        }}>
          {Object.entries(roleStats).map(([role, stats]) => (
            <div key={role} style={{
              padding: '15px',
              backgroundColor: '#fff',
              borderRadius: '8px',
              border: '1px solid #e5e7eb',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div>
                <div style={{ fontWeight: '500', color: '#1f2937' }}>{role}</div>
                <div style={{ fontSize: '14px', color: '#6b7280' }}>
                  {stats.completed} of {stats.total} completed
                </div>
              </div>
              <div style={{
                width: '50px',
                height: '50px',
                borderRadius: '50%',
                backgroundColor: '#f3f4f6',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '14px',
                fontWeight: '600',
                color: stats.completed === stats.total ? '#22c55e' : '#6b7280'
              }}>
                {Math.round((stats.completed / stats.total) * 100)}%
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Filters */}
      <div style={{
        display: 'flex',
        gap: '15px',
        marginBottom: '20px',
        flexWrap: 'wrap'
      }}>
        <select
          value={filterRole}
          onChange={(e) => setFilterRole(e.target.value)}
          style={{
            padding: '8px 12px',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            fontSize: '14px'
          }}
        >
          <option value="all">All Roles</option>
          {Object.keys(roleStats).map(role => (
            <option key={role} value={role}>{role}</option>
          ))}
        </select>
        
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          style={{
            padding: '8px 12px',
            border: '1px solid #d1d5db',
            borderRadius: '6px',
            fontSize: '14px'
          }}
        >
          <option value="all">All Status</option>
          <option value="completed">Completed</option>
          <option value="pending">Pending</option>
        </select>
      </div>

      {/* Action Items List */}
      <div style={{
        backgroundColor: '#fff',
        borderRadius: '12px',
        border: '1px solid #e5e7eb',
        overflow: 'hidden'
      }}>
        <div style={{
          padding: '20px',
          borderBottom: '1px solid #e5e7eb',
          backgroundColor: '#f9fafb'
        }}>
          <h3 style={{ margin: 0, fontSize: '18px', fontWeight: '600', color: '#1f2937' }}>
            Action Items ({filteredItems.length})
          </h3>
        </div>
        
        <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
          {filteredItems.length === 0 ? (
            <div style={{
              padding: '40px',
              textAlign: 'center',
              color: '#6b7280'
            }}>
              No action items found for the selected filters.
            </div>
          ) : (
            filteredItems.map((item, index) => (
              <div key={item.id || index} style={{
                padding: '15px 20px',
                borderBottom: index < filteredItems.length - 1 ? '1px solid #f3f4f6' : 'none',
                display: 'flex',
                alignItems: 'flex-start',
                gap: '15px'
              }}>
                <div style={{
                  width: '20px',
                  height: '20px',
                  borderRadius: '4px',
                  backgroundColor: item.is_completed ? '#22c55e' : '#e5e7eb',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  marginTop: '2px'
                }}>
                  {item.is_completed && (
                    <span style={{ color: '#fff', fontSize: '12px' }}>✓</span>
                  )}
                </div>
                
                <div style={{ flex: 1 }}>
                  <div style={{
                    fontSize: '16px',
                    color: '#1f2937',
                    marginBottom: '5px',
                    textDecoration: item.is_completed ? 'line-through' : 'none'
                  }}>
                    {item.action_text}
                  </div>
                  
                  <div style={{
                    display: 'flex',
                    gap: '15px',
                    fontSize: '14px',
                    color: '#6b7280'
                  }}>
                    <span>
                      <strong>User:</strong> {users[item.user_id]?.username || 'Unknown'}
                    </span>
                    <span>
                      <strong>Role:</strong> {item.user_role || 'Unknown'}
                    </span>
                    {item.completed_at && (
                      <span>
                        <strong>Completed:</strong> {new Date(item.completed_at).toLocaleString()}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default ChecklistStatus;
