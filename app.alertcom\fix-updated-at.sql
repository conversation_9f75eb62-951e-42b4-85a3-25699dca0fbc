-- Quick fix for the updated_at column issue
-- Run this immediately to fix the AI settings save error

BEGIN;

-- Add updated_at column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'company_settings' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE company_settings ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added updated_at column to company_settings';
        
        -- Update existing rows to have the current timestamp
        UPDATE company_settings SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL;
        RAISE NOTICE 'Updated existing rows with current timestamp';
    ELSE
        RAISE NOTICE 'updated_at column already exists in company_settings';
    END IF;
END $$;

-- Verify the column was added
SELECT 
    CASE 
        WHEN COUNT(*) = 1 THEN 'SUCCESS: updated_at column exists'
        ELSE 'ERROR: updated_at column missing'
    END AS status
FROM information_schema.columns 
WHERE table_name = 'company_settings' AND column_name = 'updated_at';

-- Show current company_settings structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'company_settings'
ORDER BY ordinal_position;

COMMIT;

SELECT 'Database fix completed successfully!' AS result;
