--
-- PostgreSQL database dump
--

-- Dumped from database version 17.4
-- Dumped by pg_dump version 17.4

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: common_locations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.common_locations (
    id integer NOT NULL,
    name text NOT NULL,
    address text NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.common_locations OWNER TO postgres;

--
-- Name: common_locations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.common_locations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.common_locations_id_seq OWNER TO postgres;

--
-- Name: common_locations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.common_locations_id_seq OWNED BY public.common_locations.id;


--
-- Name: companies; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.companies (
    id integer NOT NULL
);


ALTER TABLE public.companies OWNER TO postgres;

--
-- Name: companies_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.companies_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.companies_id_seq OWNER TO postgres;

--
-- Name: companies_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.companies_id_seq OWNED BY public.companies.id;


--
-- Name: company_settings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.company_settings (
    company_id integer NOT NULL,
    active_modules text[] DEFAULT ARRAY['EMS'::text],
    form_configs jsonb DEFAULT '{}'::jsonb,
    locations jsonb DEFAULT '[]'::jsonb
);


ALTER TABLE public.company_settings OWNER TO postgres;

--
-- Name: event_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.event_logs (
    id integer NOT NULL,
    event_id integer,
    action character varying(50) NOT NULL,
    "timestamp" timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    details jsonb
);


ALTER TABLE public.event_logs OWNER TO postgres;

--
-- Name: event_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.event_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.event_logs_id_seq OWNER TO postgres;

--
-- Name: event_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.event_logs_id_seq OWNED BY public.event_logs.id;


--
-- Name: events; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.events (
    id integer NOT NULL,
    title character varying(255) NOT NULL,
    info text,
    scale character varying(50) NOT NULL,
    urgency character varying(50) NOT NULL,
    location jsonb,
    status character varying(50) DEFAULT 'open'::character varying,
    escalation_level integer DEFAULT 0,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    included_report_to_locations jsonb,
    module character varying(50) DEFAULT 'mci'::character varying,
    description text,
    custom_fields jsonb DEFAULT '{}'::jsonb,
    company_id integer NOT NULL,
    assigned_ids integer[],
    notify_all_if_unassigned boolean
);


ALTER TABLE public.events OWNER TO postgres;

--
-- Name: events_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.events_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.events_id_seq OWNER TO postgres;

--
-- Name: events_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.events_id_seq OWNED BY public.events.id;


--
-- Name: modules; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.modules (
    customer_id integer NOT NULL,
    module_name character varying(50) NOT NULL,
    enabled boolean DEFAULT false
);


ALTER TABLE public.modules OWNER TO postgres;

--
-- Name: notifications; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.notifications (
    id integer NOT NULL,
    responder_id integer NOT NULL,
    event_id integer NOT NULL,
    message text NOT NULL,
    type character varying(50) NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.notifications OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.notifications_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.notifications_id_seq OWNER TO postgres;

--
-- Name: notifications_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.notifications_id_seq OWNED BY public.notifications.id;


--
-- Name: responder_logs; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.responder_logs (
    id integer NOT NULL,
    responder_id integer,
    latitude double precision,
    longitude double precision,
    status character varying(50),
    "timestamp" timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.responder_logs OWNER TO postgres;

--
-- Name: responder_logs_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.responder_logs_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.responder_logs_id_seq OWNER TO postgres;

--
-- Name: responder_logs_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.responder_logs_id_seq OWNED BY public.responder_logs.id;


--
-- Name: responders; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.responders (
    id integer NOT NULL,
    latitude double precision NOT NULL,
    longitude double precision NOT NULL,
    status character varying(50) DEFAULT 'available'::character varying,
    last_updated timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.responders OWNER TO postgres;

--
-- Name: roles_config; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.roles_config (
    role text NOT NULL,
    report_to_locations jsonb DEFAULT '[]'::jsonb
);


ALTER TABLE public.roles_config OWNER TO postgres;

--
-- Name: shift_assignments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.shift_assignments (
    id integer NOT NULL,
    shift_id integer,
    user_id integer,
    assigned_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.shift_assignments OWNER TO postgres;

--
-- Name: shift_assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.shift_assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.shift_assignments_id_seq OWNER TO postgres;

--
-- Name: shift_assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.shift_assignments_id_seq OWNED BY public.shift_assignments.id;


--
-- Name: shifts; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.shifts (
    id integer NOT NULL,
    start_time timestamp without time zone NOT NULL,
    end_time timestamp without time zone NOT NULL,
    location character varying(255) NOT NULL,
    staff_needed integer NOT NULL,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.shifts OWNER TO postgres;

--
-- Name: shifts_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.shifts_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.shifts_id_seq OWNER TO postgres;

--
-- Name: shifts_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.shifts_id_seq OWNED BY public.shifts.id;


--
-- Name: tasks; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.tasks (
    id integer NOT NULL,
    event_id integer,
    assigned_to integer,
    status character varying(50) DEFAULT 'pending'::character varying,
    message text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    report_to_location text
);


ALTER TABLE public.tasks OWNER TO postgres;

--
-- Name: tasks_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.tasks_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.tasks_id_seq OWNER TO postgres;

--
-- Name: tasks_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.tasks_id_seq OWNED BY public.tasks.id;


--
-- Name: templates; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.templates (
    id integer NOT NULL,
    name text NOT NULL,
    config jsonb NOT NULL,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    description text
);


ALTER TABLE public.templates OWNER TO postgres;

--
-- Name: templates_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.templates_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.templates_id_seq OWNER TO postgres;

--
-- Name: templates_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.templates_id_seq OWNED BY public.templates.id;


--
-- Name: transport_assignments; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transport_assignments (
    id integer NOT NULL,
    request_id integer,
    driver_id integer,
    assigned_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.transport_assignments OWNER TO postgres;

--
-- Name: transport_assignments_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transport_assignments_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.transport_assignments_id_seq OWNER TO postgres;

--
-- Name: transport_assignments_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.transport_assignments_id_seq OWNED BY public.transport_assignments.id;


--
-- Name: transport_requests; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.transport_requests (
    id integer NOT NULL,
    pickup_location character varying(255) NOT NULL,
    destination character varying(255) NOT NULL,
    vehicle_type character varying(50) NOT NULL,
    urgency character varying(50) NOT NULL,
    event_id integer,
    patient_name character varying(100),
    patient_condition character varying(255),
    special_needs character varying(255),
    vendor_id integer,
    status character varying(50) DEFAULT 'requested'::character varying,
    created_by integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.transport_requests OWNER TO postgres;

--
-- Name: transport_requests_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.transport_requests_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.transport_requests_id_seq OWNER TO postgres;

--
-- Name: transport_requests_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.transport_requests_id_seq OWNED BY public.transport_requests.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    email character varying(255) NOT NULL,
    username character varying(50) NOT NULL,
    password character varying(255) NOT NULL,
    role character varying(50) NOT NULL,
    phone character varying(20),
    main_location text,
    roles jsonb DEFAULT '[]'::jsonb,
    first_name text,
    last_name text,
    job_role text,
    home_address text,
    city text,
    state text,
    zip text
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: vendors; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.vendors (
    id integer NOT NULL,
    name character varying(100) NOT NULL,
    email character varying(255),
    phone character varying(20),
    transport_modes text[] NOT NULL,
    capabilities text[] NOT NULL,
    base_location character varying(255) NOT NULL,
    availability_status character varying(50) DEFAULT 'available'::character varying,
    contact_methods text[] NOT NULL,
    api_endpoint character varying(255),
    api_type character varying(50),
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);


ALTER TABLE public.vendors OWNER TO postgres;

--
-- Name: vendors_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.vendors_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.vendors_id_seq OWNER TO postgres;

--
-- Name: vendors_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.vendors_id_seq OWNED BY public.vendors.id;


--
-- Name: common_locations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.common_locations ALTER COLUMN id SET DEFAULT nextval('public.common_locations_id_seq'::regclass);


--
-- Name: companies id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.companies ALTER COLUMN id SET DEFAULT nextval('public.companies_id_seq'::regclass);


--
-- Name: event_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.event_logs ALTER COLUMN id SET DEFAULT nextval('public.event_logs_id_seq'::regclass);


--
-- Name: events id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events ALTER COLUMN id SET DEFAULT nextval('public.events_id_seq'::regclass);


--
-- Name: notifications id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications ALTER COLUMN id SET DEFAULT nextval('public.notifications_id_seq'::regclass);


--
-- Name: responder_logs id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.responder_logs ALTER COLUMN id SET DEFAULT nextval('public.responder_logs_id_seq'::regclass);


--
-- Name: shift_assignments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.shift_assignments ALTER COLUMN id SET DEFAULT nextval('public.shift_assignments_id_seq'::regclass);


--
-- Name: shifts id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.shifts ALTER COLUMN id SET DEFAULT nextval('public.shifts_id_seq'::regclass);


--
-- Name: tasks id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks ALTER COLUMN id SET DEFAULT nextval('public.tasks_id_seq'::regclass);


--
-- Name: templates id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.templates ALTER COLUMN id SET DEFAULT nextval('public.templates_id_seq'::regclass);


--
-- Name: transport_assignments id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_assignments ALTER COLUMN id SET DEFAULT nextval('public.transport_assignments_id_seq'::regclass);


--
-- Name: transport_requests id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_requests ALTER COLUMN id SET DEFAULT nextval('public.transport_requests_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Name: vendors id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vendors ALTER COLUMN id SET DEFAULT nextval('public.vendors_id_seq'::regclass);


--
-- Data for Name: common_locations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.common_locations (id, name, address, created_at) FROM stdin;
1	Station 1	123 Fire Lane, Springfield, IL 62704	2025-03-26 18:27:11.833934
2	Station 2	456 Rescue Rd, Springfield, IL 62705	2025-03-26 18:27:11.833934
3	Headquarters	789 Command St, Springfield, IL 62701	2025-03-26 18:27:11.833934
\.


--
-- Data for Name: companies; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.companies (id) FROM stdin;
1
\.


--
-- Data for Name: company_settings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.company_settings (company_id, active_modules, form_configs, locations) FROM stdin;
1	{EMS,Fire,Hospital}	{"EMS": [{"name": "title", "type": "text", "label": "Title", "required": true}, {"name": "info", "type": "textarea", "label": "Info", "required": false}, {"name": "description", "type": "textarea", "label": "Description", "required": false}, {"name": "scale", "type": "select", "label": "Scale", "options": ["Small", "Medium", "Large"], "required": true}, {"name": "urgency", "type": "select", "label": "Urgency", "options": ["Low", "Medium", "High", "Immediate"], "required": true}, {"name": "location.commonName", "type": "text", "label": "Location Common Name", "nested": true, "required": false}, {"name": "location.address", "type": "autocomplete", "label": "Location Address", "nested": true, "required": false}, {"name": "location.city", "type": "text", "label": "Location City", "nested": true, "required": false}, {"name": "location.state", "type": "text", "label": "Location State", "nested": true, "required": false}, {"name": "location.zip", "type": "text", "label": "Location Zip", "nested": true, "required": false}], "Fire": [{"name": "title", "type": "text", "label": "Incident Title", "required": true}, {"name": "info", "type": "textarea", "label": "Incident Info", "required": false}, {"name": "description", "type": "textarea", "label": "Incident Description", "required": false}, {"name": "scale", "type": "select", "label": "Fire Scale", "options": ["Small", "Medium", "Large"], "required": true}, {"name": "urgency", "type": "select", "label": "Fire Urgency", "options": ["Low", "Medium", "High", "Immediate"], "required": true}, {"name": "location.commonName", "type": "text", "label": "Fire Location Name", "nested": true, "required": false}, {"name": "location.address", "type": "autocomplete", "label": "Fire Location Address", "nested": true, "required": false}, {"name": "location.city", "type": "text", "label": "Fire Location City", "nested": true, "required": false}, {"name": "location.state", "type": "text", "label": "Fire Location State", "nested": true, "required": false}, {"name": "location.zip", "type": "text", "label": "Fire Location Zip", "nested": true, "required": false}], "Hospital": [{"name": "title", "type": "text", "label": "Event Title", "required": true}, {"name": "info", "type": "textarea", "label": "Event Info", "required": false}, {"name": "description", "type": "textarea", "label": "Event Description", "required": false}, {"name": "scale", "type": "select", "label": "Event Scale", "options": ["Small", "Medium", "Large"], "required": true}, {"name": "urgency", "type": "select", "label": "Event Urgency", "options": ["Low", "Medium", "High", "Immediate"], "required": true}, {"name": "location.commonName", "type": "text", "label": "Hospital Name", "nested": true, "required": false}, {"name": "location.address", "type": "autocomplete", "label": "Hospital Address", "nested": true, "required": false}, {"name": "location.city", "type": "text", "label": "Hospital City", "nested": true, "required": false}, {"name": "location.state", "type": "text", "label": "Hospital State", "nested": true, "required": false}, {"name": "location.zip", "type": "text", "label": "Hospital Zip", "nested": true, "required": false}]}	[{"address": "123 Main St, La Crosse, WI 54601", "commonName": "Main Hospital"}, {"address": "456 Oak Ave, La Crosse, WI 54601", "commonName": "Fire Station 1"}]
\.


--
-- Data for Name: event_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.event_logs (id, event_id, action, "timestamp", details) FROM stdin;
\.


--
-- Data for Name: events; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.events (id, title, info, scale, urgency, location, status, escalation_level, created_by, created_at, included_report_to_locations, module, description, custom_fields, company_id, assigned_ids, notify_all_if_unassigned) FROM stdin;
244	MCI - I-90 Larger Crash	Will be sending staff to stations and staging areas. 	Medium	High	{"zip": "54669", "city": "West Salem", "state": "WI", "address": "", "commonName": "I-90 Mile Marker 12"}	resolved	0	2	2025-03-30 12:43:04.865883	[{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance ", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "SUV", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2615 George St", "primary": false, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 3", "staffNeeded": 2}]	mci	I-90 mile-marker 12, multi-vehicle crash.	{}	1	{}	t
245	MCI - I-90 Larger Crash	Will be sending staff to stations and staging areas. 	Medium	High	{"zip": "54669", "city": "West Salem", "state": "WI", "address": "", "commonName": "I-90 Mile Marker 12"}	resolved	0	2	2025-03-30 12:51:12.126647	[{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance ", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "SUV", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2615 George St", "primary": false, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 3", "staffNeeded": 2}]	mci	I-90 mile-marker 12, multi-vehicle crash.	{}	1	{}	t
246	Test Popout Map	This is an MCI test for Tri-State Ambulance. Check your text messages and click on URL. 	Medium	Medium	{"zip": "54650", "city": "Onalaska", "state": "WI", "address": "1211 Crossing Meadows Dr", "commonName": "Sam's Club "}	resolved	0	2	2025-03-30 12:52:57.125619	[{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "SUV 1", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}, {"name": "SUV", "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}]	mci		{}	1	{}	t
242	Test Popout Map	This is an MCI test for Tri-State Ambulance. Check your text messages and click on URL. 	Medium	Medium	{"zip": "54650", "city": "Onalaska", "state": "WI", "address": "1211 Crossing Meadows Dr", "commonName": "Sam's Club "}	resolved	0	2	2025-03-30 10:35:55.850939	[{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "SUV 1", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}, {"name": "SUV", "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}]	mci	Multi locational with testing. 	{}	1	{}	t
248	MCI	No additional information was available. 	Large	High	{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2850 Airport Rd", "commonName": "La Crosse Regional Airport"}	resolved	0	2	2025-03-30 13:04:43.606266	[{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "SUV", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance ", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2615 George St", "primary": false, "resources": [{"name": "", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 3", "staffNeeded": 2}]	mci	MCI Test Walk through.	{}	1	{}	t
253	11	1	Small	Low	{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "111 Causeway Blvd", "commonName": ""}	open	0	\N	2025-04-02 19:59:56.891	{}	\N	1	{}	1	{6}	t
250	Test Popout Map	This is an MCI test for Tri-State Ambulance. Check your text messages and click on URL. 	Medium	Medium	{"zip": "54650", "city": "Onalaska", "state": "WI", "address": "1211 Crossing Meadows Dr", "commonName": "Sam's Club "}	resolved	0	2	2025-03-30 14:47:39.142026	[{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "SUV 1", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}, {"name": "SUV", "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}]	mci		{}	1	{}	t
252	MCI La Crosse Airport - MCI - Please Respond 		Medium	High	{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2850 Airport Rd", "commonName": "La Crosse Regional Airport"}	open	0	2	2025-04-01 18:12:19.479431	[{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance 1", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance 2", "requiredRoles": [], "responderCount": 2}, {"name": "SUV ", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5 ", "staffNeeded": 2}, {"zip": "54601", "city": "La Crosse", "state": "WI", "address": "1900 South Ave", "primary": false, "resources": [], "commonName": "Gundersen ER", "staffNeeded": 2}]	mci	Check your text messages for a link to open your MDT!	{}	1	{}	t
251	MCI La Crosse Airport - MCI - Please Respond 		Medium	High	{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2850 Airport Rd", "commonName": "La Crosse Regional Airport"}	resolved	0	2	2025-03-30 19:28:36.481044	[{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance 1", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance 2", "requiredRoles": [], "responderCount": 2}, {"name": "SUV ", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5 ", "staffNeeded": 2}, {"zip": "54601", "city": "La Crosse", "state": "WI", "address": "1900 South Ave", "primary": false, "resources": [], "commonName": "Gundersen ER", "staffNeeded": 2}]	mci	Check your text messages for a link to open your MDT!	{}	1	{}	t
254	T	w	Small	High	{"zip": "54601", "city": "La Crosse", "state": "WI", "address": "1106 3rd St S", "commonName": "w"}	open	0	\N	2025-04-02 20:33:38.046	{}	\N	w	{}	1	{11}	t
\.


--
-- Data for Name: modules; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.modules (customer_id, module_name, enabled) FROM stdin;
2	mci	t
2	dispatch	t
2	scheduling	t
2	reporting	t
2	transportation	t
\.


--
-- Data for Name: notifications; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.notifications (id, responder_id, event_id, message, type, created_at) FROM stdin;
\.


--
-- Data for Name: responder_logs; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.responder_logs (id, responder_id, latitude, longitude, status, "timestamp") FROM stdin;
\.


--
-- Data for Name: responders; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.responders (id, latitude, longitude, status, last_updated) FROM stdin;
11	43.83485380798298	-91.31880298118396	available	2025-03-30 11:03:30.456116
6	43.834853447211636	-91.31880039423406	available	2025-04-01 20:38:14.169325
5	43.8342373	-91.3185703	available	2025-03-30 13:42:45.126115
2	43.8342333	-91.3185684	available	2025-03-30 13:52:57.780387
\.


--
-- Data for Name: roles_config; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.roles_config (role, report_to_locations) FROM stdin;
lead	["1919 Oak St, La Crosse, WI", "2020 Pine St, La Crosse, WI"]
\.


--
-- Data for Name: shift_assignments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.shift_assignments (id, shift_id, user_id, assigned_at) FROM stdin;
\.


--
-- Data for Name: shifts; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.shifts (id, start_time, end_time, location, staff_needed, created_by, created_at) FROM stdin;
\.


--
-- Data for Name: tasks; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.tasks (id, event_id, assigned_to, status, message, created_at, report_to_location) FROM stdin;
236	244	5	pending	\N	2025-03-30 12:43:04.882941	2615 George St, La Crosse, WI 54603
242	248	5	pending	\N	2025-03-30 13:04:43.630117	2615 George St, La Crosse, WI 54603
245	250	6	enroute	\N	2025-03-30 14:47:39.152227	235 Causeway Blvd, La Crosse, WI 54603
251	252	5	pending	\N	2025-04-01 18:12:19.582123	1900 South Ave, La Crosse, WI 54601
237	244	6	pending	\N	2025-03-30 12:43:04.950775	2615 George St, La Crosse, WI 54603
243	248	6	acknowledged	\N	2025-03-30 13:04:43.776464	2615 George St, La Crosse, WI 54603
246	251	2	pending	\N	2025-03-30 19:28:36.497045	1900 South Ave, La Crosse, WI 54601
252	252	6	enroute	\N	2025-04-01 18:12:19.582855	1900 South Ave, La Crosse, WI 54601
238	245	5	pending	\N	2025-03-30 12:51:12.139002	2615 George St, La Crosse, WI 54603
247	251	11	pending	\N	2025-03-30 19:28:36.608531	1900 South Ave, La Crosse, WI 54601
253	252	11	pending	\N	2025-04-01 18:12:19.583374	1900 South Ave, La Crosse, WI 54601
239	245	6	pending	\N	2025-03-30 12:51:12.139166	2615 George St, La Crosse, WI 54603
248	251	6	enroute	\N	2025-03-30 19:28:36.608712	1900 South Ave, La Crosse, WI 54601
240	246	6	pending	\N	2025-03-30 12:52:57.128853	235 Causeway Blvd, La Crosse, WI 54603
249	251	5	pending	\N	2025-03-30 19:28:36.648751	1900 South Ave, La Crosse, WI 54601
250	252	2	pending	\N	2025-04-01 18:12:19.522316	1900 South Ave, La Crosse, WI 54601
231	242	6	delayed	\N	2025-03-30 10:35:55.913989	235 Causeway Blvd, La Crosse, WI 54603
229	242	2	acknowledged	\N	2025-03-30 10:35:55.864351	235 Causeway Blvd, La Crosse, WI 54603
230	242	5	delayed	\N	2025-03-30 10:35:55.864471	235 Causeway Blvd, La Crosse, WI 54603
232	242	11	pending	\N	2025-03-30 10:35:55.91413	235 Causeway Blvd, La Crosse, WI 54603
\.


--
-- Data for Name: templates; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.templates (id, name, config, created_by, created_at, description) FROM stdin;
7	Resources and Locations	{"info": "This is an MCI test for Tri-State Ambulance. Check your text messages and click on URL. ", "scale": "Large", "title": "Test Multiple Locations and Resource Assignment", "status": "open", "urgency": "Immediate", "location": {"0": "v", "1": "a", "2": "l", "3": "l", "4": "e", "5": "y", "6": " ", "zip": "54601", "city": "La Crosse", "state": "WI", "address": "3800 WI-16", "commonName": "Valley View Mall"}, "assigned_to": ["6"], "included_report_to_locations": [{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": true, "resources": [{"name": "SUV 1", "responderCount": 1}, {"name": "SUV 2", "responderCount": 1}, {"name": "Ambulance 1", "responderCount": 2}, {"name": "Ambulance 2", "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 8}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance 3", "responderCount": 2}, {"name": "Ambulance 4", "responderCount": 2}], "commonName": "The ROCC", "staffNeeded": 4}, {"zip": "54601", "city": "La Crosse", "state": "WI", "address": "1900 South Ave", "primary": false, "resources": [{"name": "Ambulance 5", "responderCount": 2}], "commonName": "Gundersen Lutheran Main Campus", "staffNeeded": 2}, {"zip": "", "city": "", "state": "", "address": "", "primary": false, "resources": [], "commonName": "", "staffNeeded": 2}]}	2	2025-03-23 11:06:00.44361	\N
10	Test Map Popout	{"info": "This is an MCI test for Tri-State Ambulance. Check your text messages and click on URL. ", "scale": "Medium", "title": "Test Popout Map", "status": "open", "urgency": "Medium", "location": {"zip": "54650", "city": "Onalaska", "state": "WI", "address": "1211 Crossing Meadows Dr", "commonName": "Sam's Club "}, "assigned_to": [], "included_report_to_locations": [{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "SUV 1", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "responderCount": 2}, {"name": "Ambulance", "responderCount": 2}, {"name": "SUV", "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}]}	2	2025-03-30 10:17:29.30436	Multi locational with testing. 
11	MCI - I-90 Larger Crash Template	{"info": "Will be sending staff to stations and staging areas. ", "scale": "Medium", "title": "MCI - I-90 Larger Crash", "urgency": "High", "location": {"zip": "54669", "city": "West Salem", "state": "WI", "address": "", "commonName": "I-90 Mile Marker 12"}, "assignedIds": [5, 6], "description": "I-90 mile-marker 12, multi-vehicle crash.", "notifyAllIfUnassigned": true, "included_report_to_locations": [{"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance ", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "SUV", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2615 George St", "primary": false, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 3", "staffNeeded": 2}]}	2	2025-03-30 12:43:12.322431	Auto-saved from event launch
12	MCI Template	{"info": "No additional information was available. ", "scale": "Large", "title": "MCI", "urgency": "High", "location": {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2850 Airport Rd", "commonName": "La Crosse Regional Airport"}, "assignedIds": [5, 6], "description": "MCI Test Walk through.", "notifyAllIfUnassigned": true, "included_report_to_locations": [{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance", "requiredRoles": [], "responderCount": 2}, {"name": "SUV", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance ", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5", "staffNeeded": 2}, {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2615 George St", "primary": false, "resources": [{"name": "", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 3", "staffNeeded": 2}]}	2	2025-03-30 13:04:49.733708	Auto-saved from event launch
13	MCI La Crosse Airport - MCI - Please Respond  Template	{"info": "", "scale": "Medium", "title": "MCI La Crosse Airport - MCI - Please Respond ", "urgency": "High", "location": {"zip": "54603", "city": "La Crosse", "state": "WI", "address": "2850 Airport Rd", "commonName": "La Crosse Regional Airport"}, "assignedIds": [2, 5, 11, 6], "description": "Check your text messages for a link to open your MDT!", "notifyAllIfUnassigned": true, "included_report_to_locations": [{"zip": "54603", "city": "La Crosse", "state": "WI", "address": "235 Causeway Blvd", "primary": true, "resources": [{"name": "Ambulance 1", "requiredRoles": [], "responderCount": 2}, {"name": "Ambulance 2", "requiredRoles": [], "responderCount": 2}, {"name": "SUV ", "requiredRoles": [], "responderCount": 1}], "commonName": "ROCC", "staffNeeded": 2}, {"zip": "54636", "city": "Holmen", "state": "WI", "address": "3031 Circle Dr", "primary": false, "resources": [{"name": "Ambulance", "requiredRoles": [], "responderCount": 2}], "commonName": "Station 5 ", "staffNeeded": 2}, {"zip": "54601", "city": "La Crosse", "state": "WI", "address": "1900 South Ave", "primary": false, "resources": [], "commonName": "Gundersen ER", "staffNeeded": 2}]}	2	2025-03-30 19:28:51.014526	Auto-saved from event launch
\.


--
-- Data for Name: transport_assignments; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.transport_assignments (id, request_id, driver_id, assigned_at) FROM stdin;
\.


--
-- Data for Name: transport_requests; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.transport_requests (id, pickup_location, destination, vehicle_type, urgency, event_id, patient_name, patient_condition, special_needs, vendor_id, status, created_by, created_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, email, username, password, role, phone, main_location, roles, first_name, last_name, job_role, home_address, city, state, zip) FROM stdin;
2	<EMAIL>	ttornstrom	$2b$10$96uCFC3NKjlloOkRF4l74uNhEKV7GQrvDBkc4RmKjR/hKvYCmwTue	commander	+16084060390	Headquarters	["commander"]	Thomas	Tornstrom	Chief	1126 Sandy Cir	La Crescent	MN	55947
5	<EMAIL>	staff1	$2b$10$KGy530NjRMmUJSJArXJ4QeNwWWnNOlE27KgHtqfhPKxzCmqYMqgm6	staff		Station 1	["medic"]	John	Doe	EMT	713 E Grove St	Caledonia	MN	55921
11	<EMAIL>	etornstrom	$2b$10$G.D0O5kLBHb7FcZikPibxeLaGyF5IhVLSnhgKk4EztJoY05sRa7CC	staff	+1507-730-7003	Headquarters	[]	Evan	Tornstrom	Paramedic	1126 Sandy Cir	La Crescent	MN	55947
6	<EMAIL>	staff2	$2b$10$rb631XkcVTcI1IZQqf3loOYXaBBQmDVL9jFJ37hCq.invBxp6tiI.	staff	+1608-406-0390	Station 2	["medic", "driver"]	Lori	Johnson	Paramedic	3210 N Kinney Coulee Rd	Onalaska	WI	54650
\.


--
-- Data for Name: vendors; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.vendors (id, name, email, phone, transport_modes, capabilities, base_location, availability_status, contact_methods, api_endpoint, api_type, created_at) FROM stdin;
1	City Ambulance Service	<EMAIL>	+**********	{Ambulance}	{ALS,BLS,Oxygen,Stretcher}	123 Main St, La Crosse, WI	available	{email,sms,phone}	\N	\N	2025-03-24 17:04:47.991115
2	Wheelchair Transport Co	<EMAIL>	+**********	{"Wheelchair Van"}	{"Wheelchair Access"}	456 Oak St, La Crosse, WI	available	{email,sms}	\N	\N	2025-03-24 17:04:47.991115
3	Local Taxi Cabs	<EMAIL>	+**********	{"Taxi Cab"}	{Non-Emergency}	789 Pine St, La Crosse, WI	available	{email,phone}	\N	\N	2025-03-24 17:04:47.991115
4	Air Medical Services	<EMAIL>	+**********	{"Air Medical"}	{ALS,"Critical Care"}	101 Airport Rd, La Crosse, WI	available	{email,sms}	\N	\N	2025-03-24 17:04:47.991115
\.


--
-- Name: common_locations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.common_locations_id_seq', 3, true);


--
-- Name: companies_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.companies_id_seq', 1, false);


--
-- Name: event_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.event_logs_id_seq', 1, false);


--
-- Name: events_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.events_id_seq', 254, true);


--
-- Name: notifications_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.notifications_id_seq', 1, false);


--
-- Name: responder_logs_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.responder_logs_id_seq', 1, false);


--
-- Name: shift_assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.shift_assignments_id_seq', 1, false);


--
-- Name: shifts_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.shifts_id_seq', 1, false);


--
-- Name: tasks_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.tasks_id_seq', 253, true);


--
-- Name: templates_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.templates_id_seq', 13, true);


--
-- Name: transport_assignments_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.transport_assignments_id_seq', 1, false);


--
-- Name: transport_requests_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.transport_requests_id_seq', 1, false);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 11, true);


--
-- Name: vendors_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.vendors_id_seq', 4, true);


--
-- Name: common_locations common_locations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.common_locations
    ADD CONSTRAINT common_locations_pkey PRIMARY KEY (id);


--
-- Name: companies companies_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.companies
    ADD CONSTRAINT companies_pkey PRIMARY KEY (id);


--
-- Name: company_settings company_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company_settings
    ADD CONSTRAINT company_settings_pkey PRIMARY KEY (company_id);


--
-- Name: event_logs event_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.event_logs
    ADD CONSTRAINT event_logs_pkey PRIMARY KEY (id);


--
-- Name: events events_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_pkey PRIMARY KEY (id);


--
-- Name: modules modules_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.modules
    ADD CONSTRAINT modules_pkey PRIMARY KEY (customer_id, module_name);


--
-- Name: notifications notifications_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT notifications_pkey PRIMARY KEY (id);


--
-- Name: responder_logs responder_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.responder_logs
    ADD CONSTRAINT responder_logs_pkey PRIMARY KEY (id);


--
-- Name: responders responders_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.responders
    ADD CONSTRAINT responders_pkey PRIMARY KEY (id);


--
-- Name: roles_config roles_config_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.roles_config
    ADD CONSTRAINT roles_config_pkey PRIMARY KEY (role);


--
-- Name: shift_assignments shift_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.shift_assignments
    ADD CONSTRAINT shift_assignments_pkey PRIMARY KEY (id);


--
-- Name: shifts shifts_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.shifts
    ADD CONSTRAINT shifts_pkey PRIMARY KEY (id);


--
-- Name: tasks tasks_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_pkey PRIMARY KEY (id);


--
-- Name: templates templates_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.templates
    ADD CONSTRAINT templates_pkey PRIMARY KEY (id);


--
-- Name: transport_assignments transport_assignments_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_assignments
    ADD CONSTRAINT transport_assignments_pkey PRIMARY KEY (id);


--
-- Name: transport_requests transport_requests_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_requests
    ADD CONSTRAINT transport_requests_pkey PRIMARY KEY (id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: vendors vendors_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.vendors
    ADD CONSTRAINT vendors_pkey PRIMARY KEY (id);


--
-- Name: company_settings company_settings_company_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.company_settings
    ADD CONSTRAINT company_settings_company_id_fkey FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: event_logs event_logs_event_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.event_logs
    ADD CONSTRAINT event_logs_event_id_fkey FOREIGN KEY (event_id) REFERENCES public.events(id);


--
-- Name: events events_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT events_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: events fk_company_id; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.events
    ADD CONSTRAINT fk_company_id FOREIGN KEY (company_id) REFERENCES public.companies(id);


--
-- Name: notifications fk_event_id; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT fk_event_id FOREIGN KEY (event_id) REFERENCES public.events(id);


--
-- Name: notifications fk_responder_id; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.notifications
    ADD CONSTRAINT fk_responder_id FOREIGN KEY (responder_id) REFERENCES public.responders(id);


--
-- Name: modules modules_customer_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.modules
    ADD CONSTRAINT modules_customer_id_fkey FOREIGN KEY (customer_id) REFERENCES public.users(id);


--
-- Name: responder_logs responder_logs_responder_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.responder_logs
    ADD CONSTRAINT responder_logs_responder_id_fkey FOREIGN KEY (responder_id) REFERENCES public.users(id);


--
-- Name: responders responders_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.responders
    ADD CONSTRAINT responders_id_fkey FOREIGN KEY (id) REFERENCES public.users(id);


--
-- Name: shift_assignments shift_assignments_shift_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.shift_assignments
    ADD CONSTRAINT shift_assignments_shift_id_fkey FOREIGN KEY (shift_id) REFERENCES public.shifts(id);


--
-- Name: shift_assignments shift_assignments_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.shift_assignments
    ADD CONSTRAINT shift_assignments_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: shifts shifts_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.shifts
    ADD CONSTRAINT shifts_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: tasks tasks_assigned_to_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_assigned_to_fkey FOREIGN KEY (assigned_to) REFERENCES public.users(id);


--
-- Name: tasks tasks_event_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.tasks
    ADD CONSTRAINT tasks_event_id_fkey FOREIGN KEY (event_id) REFERENCES public.events(id) ON DELETE CASCADE;


--
-- Name: templates templates_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.templates
    ADD CONSTRAINT templates_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: transport_assignments transport_assignments_driver_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_assignments
    ADD CONSTRAINT transport_assignments_driver_id_fkey FOREIGN KEY (driver_id) REFERENCES public.users(id);


--
-- Name: transport_assignments transport_assignments_request_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_assignments
    ADD CONSTRAINT transport_assignments_request_id_fkey FOREIGN KEY (request_id) REFERENCES public.transport_requests(id);


--
-- Name: transport_requests transport_requests_created_by_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_requests
    ADD CONSTRAINT transport_requests_created_by_fkey FOREIGN KEY (created_by) REFERENCES public.users(id);


--
-- Name: transport_requests transport_requests_event_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_requests
    ADD CONSTRAINT transport_requests_event_id_fkey FOREIGN KEY (event_id) REFERENCES public.events(id);


--
-- Name: transport_requests transport_requests_vendor_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.transport_requests
    ADD CONSTRAINT transport_requests_vendor_id_fkey FOREIGN KEY (vendor_id) REFERENCES public.vendors(id);


--
-- PostgreSQL database dump complete
--

