// src/Home.js
import React from 'react';
import { Link } from 'react-router-dom';
import Footer from './Footer';
import './styles.css';

function Home({ token }) {
    return (
        <div style={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
            <div style={{ flex: '1' }}>
                <div className="container">
                    <section className="hero">
                        <h1>Welcome to AlertComm1</h1>
                        <p className="hero-subtitle">
                            Your trusted platform for real-time emergency communication and coordination.
                        </p>
                        <div className="hero-buttons">
                            <Link to="/login" className="btn-primary">Get Started</Link>
                            <Link to="/events" className="btn-secondary">View Events</Link>
                        </div>
                    </section>

                    <section className="features">
                        <div className="card">
                            <h2>Real-Time Updates</h2>
                            <p>Stay informed with instant event notifications and live task tracking.</p>
                        </div>
                        <div className="card">
                            <h2>Secure Communication</h2>
                            <p>Encrypted messaging ensures your team’s safety and privacy.</p>
                        </div>
                        <div className="card">
                            <h2>Smart Coordination</h2>
                            <p>Assign tasks and manage responders with intelligent location-based tools.</p>
                        </div>
                    </section>
                    {!token && (
                        <section className="cta">
                            <h2>Ready to Enhance Your Response?</h2>
                            <p>Join AlertComm1 today and streamline your emergency operations.</p>
                            <Link to="/signup" className="btn-primary">Sign Up Now</Link>
                        </section>
                    )}

                </div>
            </div>
            <Footer token={token} />
        </div>
    );
}

export default Home;