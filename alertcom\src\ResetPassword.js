// src/ResetPassword.js
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import Footer from "./Footer";
import "./styles.css";
import config from './config';
const { baseUrl } = config;

function ResetPassword({ token }) {
  
  const [email, setEmail] = useState("");
  const [message, setMessage] = useState(null);
  const [error, setError] = useState(null);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch(`${baseUrl}/forgot-password`, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        credentials: 'include', // Include cookies if your API uses session-based auth
        body: JSON.stringify({ email }),
      });      
      
      console.log('Forgot password response:', response);
      const data = await response.json(); // Parse response JSON

      // Use message from response, or fall back to a default message
      setMessage(data?.message || "If your email is registered, you will receive a password reset link.");
      setTimeout(() => {
        navigate("/login");
      }, 3000);
    } catch (err) {
      setError("An error occurred. Please try again later.");
      setTimeout(() => setError(null), 3000);
    }
  };

  return (
    <div
      style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
    >
      <div style={{ flex: "1" }}>
        <div className="container">
          <div className="card">
            <h1>Reset Password</h1>
            {error && (
              <div
                style={{
                  backgroundColor: "var(--danger)",
                  color: "#fff",
                  padding: "10px",
                  borderRadius: "5px",
                  marginBottom: "10px",
                }}
              >
                {error}
              </div>
            )}
            {message && (
              <div
                style={{
                  backgroundColor: "var(--success, #28a745)",
                  color: "#fff",
                  padding: "10px",
                  borderRadius: "5px",
                  marginBottom: "10px",
                }}
              >
                {message}
              </div>
            )}
            <form onSubmit={handleSubmit}>
              <label>
                Email:
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="form-input"
                  required
                />
              </label>
              <button type="submit" className="btn-primary">
                Send Reset Code
              </button>
              <div style={{ textAlign: 'center', marginTop: '15px' }}>
                <a href="/login" style={{ color: '#4f46e5', fontWeight: 'bold', textDecoration: 'underline' }}>
                  Back to Login
                </a>
              </div>
            </form>
          </div>
        </div>
      </div>
      <Footer token={token} />
    </div>
  );
}

export default ResetPassword;
