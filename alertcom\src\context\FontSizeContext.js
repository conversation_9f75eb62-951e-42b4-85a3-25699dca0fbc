import React, { createContext, useState, useEffect, useContext } from 'react';

// Create the context
const FontSizeContext = createContext();

// Custom hook to use the font size context
export const useFontSize = () => {
  const context = useContext(FontSizeContext);
  if (!context) {
    throw new Error('useFontSize must be used within a FontSizeProvider');
  }
  return context;
};

// Provider component
export const FontSizeProvider = ({ children }) => {
  // Get the initial font size scale from localStorage or use default
  const [fontSizeScale, setFontSizeScale] = useState(() => {
    const savedScale = localStorage.getItem('fontSizeScale');
    return savedScale ? parseFloat(savedScale) : 1;
  });

  // Update the CSS variable whenever fontSizeScale changes
  useEffect(() => {
    document.documentElement.style.setProperty('--font-size-scale', fontSizeScale);
    localStorage.setItem('fontSizeScale', fontSizeScale);
  }, [fontSizeScale]);

  // Function to handle font size changes
  const handleFontSizeChange = (direction) => {
    setFontSizeScale(prevScale => {
      let newScale;
      if (direction === 'increase') {
        newScale = Math.min(prevScale + 0.1, 1.5); // Maximum scale: 1.5
      } else if (direction === 'decrease') {
        newScale = Math.max(prevScale - 0.1, 0.8); // Minimum scale: 0.8
      } else if (direction === 'reset') {
        newScale = 1; // Reset to default
      } else {
        newScale = prevScale; // No change
      }
      return newScale;
    });
  };

  // Value to be provided to consumers
  const value = {
    fontSizeScale,
    handleFontSizeChange,
  };

  return (
    <FontSizeContext.Provider value={value}>
      {children}
    </FontSizeContext.Provider>
  );
};
