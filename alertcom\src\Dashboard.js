/* eslint-disable react-hooks/rules-of-hooks */
// src/Dashboard.js
import React, { useState, useEffect, useCallback } from "react";
import { useParams, Navigate, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { jwtDecode } from "jwt-decode";

import io from "socket.io-client";
import { GoogleMap, Marker } from "@react-google-maps/api";
import "./styles.css";
import config from "./config";
import StatCard from "./components/StatCard";
const { baseUrl } = config;

const socket = io(`${baseUrl}`, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
});

// Document Summary Component (replacing Document & Role Management)
const DocumentSummary = ({ eventId, token, eventInfo }) => {
  const [documents, setDocuments] = useState([]);
  const [documentSummaries, setDocumentSummaries] = useState({});
  const [documentChecklists, setDocumentChecklists] = useState({});
  const [loadingSummary, setLoadingSummary] = useState({});
  const [checkedSummaries, setCheckedSummaries] = useState({});
  const [checkedChecklists, setCheckedChecklists] = useState({});
  const [uploading, setUploading] = useState(false);
  const [showUpload, setShowUpload] = useState(false);

  // AI Q&A state
  const [aiQuestion, setAiQuestion] = useState("");
  const [aiAnswer, setAiAnswer] = useState("");
  const [loadingAnswer, setLoadingAnswer] = useState(false);
  const [questionHistory, setQuestionHistory] = useState([]);

  // Cookie utility functions
  const setCookie = (name, value, days) => {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${JSON.stringify(value)};expires=${expires.toUTCString()};path=/`;
  };

  const getCookie = (name) => {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i];
      while (c.charAt(0) === ' ') c = c.substring(1, c.length);
      if (c.indexOf(nameEQ) === 0) {
        try {
          return JSON.parse(c.substring(nameEQ.length, c.length));
        } catch (e) {
          return null;
        }
      }
    }
    return null;
  };

  // Load documents when component mounts
  const loadDocuments = useCallback(async () => {
    try {
      console.log('Loading documents for event:', eventId);
      const response = await fetch(`${baseUrl}/events/${eventId}/documents`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });
      console.log('Documents response status:', response.status);
      if (response.ok) {
        const docs = await response.json();
        console.log('Loaded documents:', docs);
        // Filter out any invalid documents
        const validDocs = docs.filter(doc => doc && (doc.filename || doc.originalName));
        console.log('Valid documents after filtering:', validDocs);
        setDocuments(validDocs);
      } else {
        const error = await response.json();
        console.error('Error response:', error);
      }
    } catch (error) {
      console.error('Error loading documents:', error);
    }
  }, [eventId, token]);

  useEffect(() => {
    loadDocuments();

    // Listen for document updates via WebSocket
    const handleDocumentUpdate = (data) => {
      if (data.eventId === parseInt(eventId)) {
        console.log('Documents updated via WebSocket:', data);
        loadDocuments();
      }
    };

    socket.on('documents-updated', handleDocumentUpdate);

    return () => {
      socket.off('documents-updated', handleDocumentUpdate);
    };
  }, [loadDocuments, eventId]);

  // Generate AI summary
  const generateDocumentSummary = async (doc) => {
    setLoadingSummary(prev => ({ ...prev, [doc.filename]: true }));

    try {
      const response = await fetch(`${baseUrl}/ai/summarize-document`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          eventId: eventId,
          documentName: doc.originalName || doc.filename,
          documentUrl: `${baseUrl}/events/${eventId}/documents/${doc.filename}`
        }),
      });

      if (response.ok) {
        const data = await response.json();
        const summary = data.summary || "Summary generated successfully";
        const checklistItems = data.checklist_items || [];

        setDocumentSummaries(prev => {
          const updated = { ...prev, [doc.filename]: summary };
          setCookie(`doc_summaries_${eventId}`, updated, 7);
          return updated;
        });

        setDocumentChecklists(prev => {
          const updated = { ...prev, [doc.filename]: checklistItems };
          setCookie(`doc_checklists_${eventId}`, updated, 7);
          return updated;
        });

        toast.success('Document summary and checklist generated!');
      } else {
        throw new Error('Failed to generate summary');
      }
    } catch (error) {
      console.error('Error generating summary:', error);
      setDocumentSummaries(prev => {
        const updated = { ...prev, [doc.filename]: "Failed to generate summary. Please try again." };
        setCookie(`doc_summaries_${eventId}`, updated, 7);
        return updated;
      });
      setDocumentChecklists(prev => {
        const updated = { ...prev, [doc.filename]: [] };
        setCookie(`doc_checklists_${eventId}`, updated, 7);
        return updated;
      });
      toast.error('Failed to generate summary');
    } finally {
      setLoadingSummary(prev => ({ ...prev, [doc.filename]: false }));
    }
  };

  // Handle checkbox change
  const handleSummaryCheck = (docFilename, checked) => {
    setCheckedSummaries(prev => {
      const updated = { ...prev, [docFilename]: checked };
      setCookie(`checked_summaries_${eventId}`, updated, 7);
      return updated;
    });
  };

  // Handle checklist item checkbox change
  const handleChecklistCheck = (docFilename, itemIndex, checked) => {
    setCheckedChecklists(prev => {
      const docChecklist = prev[docFilename] || {};
      const updated = {
        ...prev,
        [docFilename]: {
          ...docChecklist,
          [itemIndex]: checked
        }
      };
      setCookie(`checked_checklists_${eventId}`, updated, 7);
      return updated;
    });
  };

  // AI Q&A function
  const handleAskQuestion = async () => {
    if (!aiQuestion.trim()) {
      toast.error('Please enter a question');
      return;
    }

    setLoadingAnswer(true);
    try {
      const response = await fetch(`${baseUrl}/ai/ask-document`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          eventId: eventId,
          question: aiQuestion,
          documents: documents
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setAiAnswer(data.answer || "No answer received");

        // Add to question history and save to localStorage
        const newHistory = [{
          question: aiQuestion,
          answer: data.answer,
          timestamp: new Date().toLocaleString()
        }, ...questionHistory.slice(0, 9)]; // Keep last 10 questions

        setQuestionHistory(newHistory);
        localStorage.setItem(`ai_questions_${eventId}`, JSON.stringify(newHistory));

        setAiQuestion(""); // Clear input
        toast.success('Answer generated!');
      } else {
        throw new Error('Failed to get answer');
      }
    } catch (error) {
      console.error('Error asking question:', error);
      setAiAnswer("Sorry, I couldn't process your question. Please try again.");
      toast.error('Failed to get answer');
    } finally {
      setLoadingAnswer(false);
    }
  };

  // Handle file upload
  const handleFileUpload = async (event) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    console.log('Starting file upload for', files.length, 'files');
    setUploading(true);
    const formData = new FormData();

    // Validate file types and sizes
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'image/jpeg', 'image/png', 'image/gif'];
    const maxSize = 50 * 1024 * 1024; // 50MB

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      console.log(`File ${i + 1}: ${file.name}, Type: ${file.type}, Size: ${file.size}`);

      if (file.size > maxSize) {
        toast.error(`File ${file.name} is too large. Maximum size is 50MB.`);
        setUploading(false);
        return;
      }

      formData.append('documents', file);
    }

    try {
      console.log('Sending upload request to:', `${baseUrl}/events/${eventId}/documents`);
      const response = await fetch(`${baseUrl}/events/${eventId}/documents`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` },
        body: formData
      });

      console.log('Upload response status:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('Documents uploaded successfully!', result);
        toast.success(`${files.length} document(s) uploaded successfully!`);
        loadDocuments(); // Reload documents
        setShowUpload(false);
        // Reset file input
        event.target.value = '';
      } else {
        const error = await response.json();
        console.error(`Upload failed: ${error.error}`);
        toast.error(`Upload failed: ${error.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error uploading documents:', error);
      toast.error(`Upload error: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  // Load saved states
  useEffect(() => {
    const savedSummaries = getCookie(`doc_summaries_${eventId}`);
    const savedChecked = getCookie(`checked_summaries_${eventId}`);
    const savedChecklists = getCookie(`doc_checklists_${eventId}`);
    const savedChecklistsChecked = getCookie(`checked_checklists_${eventId}`);

    // Load AI question history from localStorage
    const savedQuestionHistory = localStorage.getItem(`ai_questions_${eventId}`);
    if (savedQuestionHistory) {
      try {
        setQuestionHistory(JSON.parse(savedQuestionHistory));
      } catch (error) {
        console.error('Error parsing saved question history:', error);
      }
    }

    if (savedSummaries) {
      setDocumentSummaries(savedSummaries);
    }
    if (savedChecked) {
      setCheckedSummaries(savedChecked);
    }
    if (savedChecklists) {
      setDocumentChecklists(savedChecklists);
    }
    if (savedChecklistsChecked) {
      setCheckedChecklists(savedChecklistsChecked);
    }
  }, [eventId]);

  return (
    <section
      className="modern-card document-summary-container"
      style={{
        padding: "25px",
        marginBottom: "20px",
        borderRadius: "16px",
        background: "rgba(255, 255, 255, 0.95)",
        backdropFilter: "blur(20px)",
        boxShadow: "0 15px 30px rgba(0,0,0,0.1)",
        border: "1px solid rgba(255, 255, 255, 0.2)",
        position: "relative",
        overflow: "hidden"
      }}
    >
      {/* Decorative background elements */}
      <div style={{
        position: "absolute",
        top: "-30%",
        right: "-15%",
        width: "200px",
        height: "200px",
        background: "linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1))",
        borderRadius: "50%",
        zIndex: 0
      }}></div>

      <div style={{ position: "relative", zIndex: 1 }}>
        <div style={{ textAlign: "center", marginBottom: "20px" }}>
          <h2 style={{
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
            backgroundClip: "text",
            fontSize: "20px",
            fontWeight: "700",
            marginBottom: "8px"
          }}>
            📄 Event Documents & AI Summary
          </h2>
          <div style={{
            width: "50px",
            height: "2px",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            margin: "0 auto 15px auto",
            borderRadius: "2px"
          }}></div>

          {/* Upload Button */}
          <button
            onClick={() => setShowUpload(!showUpload)}
            style={{
              background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
              color: "white",
              border: "none",
              padding: "10px 20px",
              borderRadius: "8px",
              fontSize: "14px",
              fontWeight: "600",
              cursor: "pointer",
              boxShadow: "0 4px 12px rgba(16, 185, 129, 0.3)",
              transition: "all 0.3s ease",
              display: "flex",
              alignItems: "center",
              gap: "8px",
              margin: "0 auto"
            }}
          >
            📤 {showUpload ? "Cancel Upload" : "Upload Documents"}
          </button>
        </div>

        {/* Upload Form */}
        {showUpload && (
          <div style={{
            background: "rgba(16, 185, 129, 0.1)",
            border: "2px dashed #10b981",
            borderRadius: "8px",
            padding: "20px",
            marginBottom: "20px",
            textAlign: "center"
          }}>
            <div style={{ fontSize: "32px", marginBottom: "10px" }}>📁</div>
            <input
              type="file"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif"
              onChange={handleFileUpload}
              disabled={uploading}
              style={{
                width: "100%",
                padding: "12px",
                border: "2px solid #10b981",
                borderRadius: "6px",
                marginBottom: "10px",
                fontSize: "14px",
                fontWeight: "500"
              }}
            />
            <p style={{
              fontSize: "12px",
              color: "#059669",
              margin: "0",
              fontWeight: "500"
            }}>
              📎 Supported: PDF, Word documents, Images (JPG, PNG, GIF)
            </p>
            {uploading && (
              <div style={{
                color: "#059669",
                marginTop: "10px",
                fontWeight: "600",
                fontSize: "14px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: "8px"
              }}>
                <div style={{
                  width: "16px",
                  height: "16px",
                  border: "2px solid #10b981",
                  borderTop: "2px solid transparent",
                  borderRadius: "50%",
                  animation: "spin 1s linear infinite"
                }}></div>
                Uploading documents...
              </div>
            )}
          </div>
        )}

        {documents.length === 0 ? (
          <div style={{
            textAlign: "center",
            padding: "40px 20px",
            color: "#6b7280",
            fontSize: "16px"
          }}>
            <div style={{ fontSize: "48px", marginBottom: "15px" }}>📁</div>
            <p style={{ margin: "0 0 10px 0", fontWeight: "500" }}>No Documents Available</p>
            <p style={{ margin: 0, fontSize: "14px" }}>Documents uploaded during event launch will appear here with AI-generated summaries.</p>
          </div>
        ) : (
          <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fill, minmax(350px, 1fr))", gap: "20px" }}>
            {documents.map((doc, index) => (
              <div
                key={index}
                className="document-item"
                style={{
                  border: "2px solid #e5e7eb",
                  borderRadius: "12px",
                  padding: "20px",
                  background: "linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)",
                  transition: "transform 0.2s ease, box-shadow 0.2s ease"
                }}
              >
                <div style={{ display: "flex", alignItems: "center", marginBottom: "15px" }}>
                  <span style={{ fontSize: "24px", marginRight: "10px" }}>
                    {doc.filename && doc.filename.endsWith('.pdf') ? '📄' :
                     doc.filename && doc.filename.match(/\.(doc|docx)$/i) ? '📝' : '🖼️'}
                  </span>
                  <h4 style={{
                    margin: 0,
                    fontSize: "16px",
                    fontWeight: "600",
                    color: "#1f2937",
                    flex: 1,
                    wordBreak: "break-word"
                  }}>
                    {doc.originalName || doc.filename || 'Unknown Document'}
                  </h4>
                </div>

                <div style={{ marginBottom: "15px" }}>
                  {documentSummaries[doc.filename] ? (
                    <div style={{
                      background: "linear-gradient(135deg, #e8f4f8 0%, #f0f9ff 100%)",
                      border: "1px solid #0ea5e9",
                      borderRadius: "8px",
                      padding: "15px",
                      marginBottom: "10px"
                    }}>
                      <h5 style={{
                        margin: "0 0 12px 0",
                        color: "#0369a1",
                        fontSize: "14px",
                        fontWeight: "600",
                        display: "flex",
                        alignItems: "center",
                        gap: "6px"
                      }}>
                        📋 AI Summary Checklist
                      </h5>

                      {/* Convert summary bullet points to interactive checklist */}
                      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
                        {documentSummaries[doc.filename]
                          .split('\n')
                          .filter(line => {
                            const trimmed = line.trim();
                            return trimmed.startsWith('•') || trimmed.startsWith('-') || trimmed.startsWith('*') ||
                                   (trimmed.length > 10 && !trimmed.includes(':') && trimmed.endsWith('.'));
                          })
                          .map((item, index) => {
                            const cleanItem = item.replace(/^[•\-*]\s*/, '').trim();
                            if (!cleanItem || cleanItem.length < 5) return null;

                            const checkboxKey = `${doc.filename}_summary_${index}`;
                            return (
                              <label key={index} style={{
                                display: "flex",
                                alignItems: "flex-start",
                                gap: "8px",
                                fontSize: "13px",
                                color: "#0c4a6e",
                                cursor: "pointer",
                                lineHeight: "1.4",
                                padding: "6px 8px",
                                backgroundColor: "rgba(255, 255, 255, 0.6)",
                                borderRadius: "4px",
                                border: "1px solid rgba(14, 165, 233, 0.2)"
                              }}>
                                <input
                                  type="checkbox"
                                  checked={checkedSummaries[checkboxKey] || false}
                                  onChange={(e) => handleSummaryCheck(checkboxKey, e.target.checked)}
                                  style={{
                                    width: "14px",
                                    height: "14px",
                                    accentColor: "#0ea5e9",
                                    marginTop: "2px"
                                  }}
                                />
                                <span style={{
                                  textDecoration: checkedSummaries[checkboxKey] ? "line-through" : "none",
                                  color: checkedSummaries[checkboxKey] ? "#6b7280" : "#0c4a6e",
                                  opacity: checkedSummaries[checkboxKey] ? 0.7 : 1
                                }}>
                                  {cleanItem}
                                </span>
                              </label>
                            );
                          })
                          .filter(Boolean)
                        }

                        {/* Fallback if no bullet points found - show original summary */}
                        {documentSummaries[doc.filename]
                          .split('\n')
                          .filter(line => {
                            const trimmed = line.trim();
                            return trimmed.startsWith('•') || trimmed.startsWith('-') || trimmed.startsWith('*') ||
                                   (trimmed.length > 10 && !trimmed.includes(':') && trimmed.endsWith('.'));
                          }).length === 0 && (
                          <div style={{
                            fontSize: "13px",
                            lineHeight: "1.4",
                            color: "#0c4a6e",
                            padding: "8px",
                            backgroundColor: "rgba(255, 255, 255, 0.6)",
                            borderRadius: "4px",
                            border: "1px solid rgba(14, 165, 233, 0.2)"
                          }}>
                            {documentSummaries[doc.filename]}
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={() => generateDocumentSummary(doc)}
                      disabled={loadingSummary[doc.filename]}
                      style={{
                        width: "100%",
                        padding: "12px",
                        backgroundColor: loadingSummary[doc.filename] ? "#9ca3af" : "#3b82f6",
                        color: "white",
                        border: "none",
                        borderRadius: "8px",
                        fontSize: "14px",
                        fontWeight: "600",
                        cursor: loadingSummary[doc.filename] ? "not-allowed" : "pointer",
                        marginBottom: "10px",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        gap: "8px"
                      }}
                    >
                      {loadingSummary[doc.filename] ? (
                        <>⏳ Generating Summary...</>
                      ) : (
                        <>🤖 Generate AI Summary</>
                      )}
                    </button>
                  )}

                  {documentSummaries[doc.filename] && (
                    <label style={{
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                      fontSize: "14px",
                      fontWeight: "500",
                      color: "#374151",
                      cursor: "pointer"
                    }}>
                      <input
                        type="checkbox"
                        checked={checkedSummaries[doc.filename] || false}
                        onChange={(e) => handleSummaryCheck(doc.filename, e.target.checked)}
                        style={{
                          width: "16px",
                          height: "16px",
                          accentColor: "#3b82f6"
                        }}
                      />
                      ✅ Summary reviewed and understood
                    </label>
                  )}

                  {/* Checklist Section */}
                  {documentChecklists[doc.filename] && documentChecklists[doc.filename].length > 0 && (
                    <div style={{
                      marginTop: "15px",
                      padding: "15px",
                      backgroundColor: "#f0f9ff",
                      borderRadius: "8px",
                      border: "1px solid #bae6fd"
                    }}>
                      <h5 style={{
                        margin: "0 0 10px 0",
                        fontSize: "14px",
                        fontWeight: "600",
                        color: "#0c4a6e",
                        display: "flex",
                        alignItems: "center",
                        gap: "6px"
                      }}>
                        📋 Action Checklist
                      </h5>
                      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
                        {documentChecklists[doc.filename].map((item, index) => (
                          <label key={index} style={{
                            display: "flex",
                            alignItems: "flex-start",
                            gap: "8px",
                            fontSize: "13px",
                            color: "#374151",
                            cursor: "pointer",
                            lineHeight: "1.4"
                          }}>
                            <input
                              type="checkbox"
                              checked={checkedChecklists[doc.filename]?.[index] || false}
                              onChange={(e) => handleChecklistCheck(doc.filename, index, e.target.checked)}
                              style={{
                                width: "14px",
                                height: "14px",
                                accentColor: "#3b82f6",
                                marginTop: "2px"
                              }}
                            />
                            <span style={{
                              textDecoration: checkedChecklists[doc.filename]?.[index] ? "line-through" : "none",
                              color: checkedChecklists[doc.filename]?.[index] ? "#6b7280" : "#374151"
                            }}>
                              {item}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="document-actions">
                  <a
                    href={`${baseUrl}/events/${eventId}/documents/${doc.filename || 'unknown'}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      display: "inline-flex",
                      alignItems: "center",
                      gap: "6px",
                      padding: "8px 12px",
                      backgroundColor: "#10b981",
                      color: "white",
                      textDecoration: "none",
                      borderRadius: "6px",
                      fontSize: "13px",
                      fontWeight: "500",
                      transition: "background-color 0.2s ease"
                    }}
                  >
                    📎 View Document
                  </a>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* AI Question-Answering Interface - Only show when documents are available */}
        {documents.length > 0 && (
        <div style={{
          marginTop: "30px",
          padding: "20px",
          background: "linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)",
          borderRadius: "12px",
          border: "2px solid #e2e8f0"
        }}>
          <h3 style={{
            margin: "0 0 15px 0",
            fontSize: "18px",
            fontWeight: "600",
            color: "#1e293b",
            display: "flex",
            alignItems: "center",
            gap: "8px"
          }}>
            🤖 Ask AI About Documents
          </h3>

          <div style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "20px",
            marginBottom: "15px"
          }}>
            {/* Left side - Question Input */}
            <div>
              <label style={{
                display: "block",
                fontSize: "14px",
                fontWeight: "500",
                color: "#475569",
                marginBottom: "8px"
              }}>
                Ask a question about the uploaded documents:
              </label>
              <textarea
                value={aiQuestion}
                onChange={(e) => setAiQuestion(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (!loadingAnswer && aiQuestion.trim()) {
                      handleAskQuestion();
                    }
                  }
                }}
                placeholder="e.g., What is the telephone number for the local hospital? What should staff members do to help? (Press Enter to ask, Shift+Enter for new line)"
                rows={3}
                style={{
                  width: "100%",
                  padding: "12px",
                  fontSize: "14px",
                  border: "1px solid #cbd5e1",
                  borderRadius: "8px",
                  outline: "none",
                  resize: "vertical",
                  fontFamily: "inherit"
                }}
              />
              <button
                onClick={handleAskQuestion}
                disabled={loadingAnswer || !aiQuestion.trim()}
                style={{
                  marginTop: "10px",
                  padding: "10px 20px",
                  fontSize: "14px",
                  fontWeight: "600",
                  color: "#fff",
                  backgroundColor: loadingAnswer || !aiQuestion.trim() ? "#94a3b8" : "#3b82f6",
                  border: "none",
                  borderRadius: "6px",
                  cursor: loadingAnswer || !aiQuestion.trim() ? "not-allowed" : "pointer",
                  transition: "background-color 0.2s"
                }}
              >
                {loadingAnswer ? "Getting Answer..." : "Ask AI"}
              </button>
            </div>

            {/* Right side - Answer Display */}
            <div>
              <label style={{
                display: "block",
                fontSize: "14px",
                fontWeight: "500",
                color: "#475569",
                marginBottom: "8px"
              }}>
                AI Answer:
              </label>
              <div style={{
                minHeight: "120px",
                padding: "12px",
                backgroundColor: "#fff",
                border: "1px solid #cbd5e1",
                borderRadius: "8px",
                fontSize: "14px",
                lineHeight: "1.5",
                color: "#374151",
                whiteSpace: "pre-wrap"
              }}>
                {loadingAnswer ? (
                  <div style={{ textAlign: "center", color: "#6b7280" }}>
                    🤔 Thinking...
                  </div>
                ) : aiAnswer || (
                  <div style={{ color: "#9ca3af", fontStyle: "italic" }}>
                    Ask a question to get an AI-powered answer based on your uploaded documents.
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Question History */}
          {questionHistory.length > 0 && (
            <div style={{ marginTop: "20px" }}>
              <h4 style={{
                margin: "0 0 10px 0",
                fontSize: "16px",
                fontWeight: "500",
                color: "#374151"
              }}>
                Recent Questions:
              </h4>
              <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                {questionHistory.map((item, index) => (
                  <div key={index} style={{
                    padding: "10px",
                    marginBottom: "8px",
                    backgroundColor: "#fff",
                    borderRadius: "6px",
                    border: "1px solid #e5e7eb",
                    fontSize: "13px"
                  }}>
                    <div style={{ fontWeight: "500", color: "#1f2937", marginBottom: "4px" }}>
                      Q: {item.question}
                    </div>
                    <div style={{ color: "#6b7280", marginBottom: "4px" }}>
                      A: {item.answer.substring(0, 150)}{item.answer.length > 150 ? "..." : ""}
                    </div>
                    <div style={{ fontSize: "11px", color: "#9ca3af" }}>
                      {item.timestamp}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
        )}
      </div>
    </section>
  );
};


const mapContainerStyle = {
  width: "100%",
  height: "400px",
};

class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ color: "red" }}>
          Error: {this.state.error?.message || "Something went wrong"}
        </div>
      );
    }
    return this.props.children;
  }
}

function Dashboard({ token, mapsLoaded }) {
  const { eventId } = useParams();
  const [eventInfo, setEventInfo] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [responders, setResponders] = useState([]);
  const [notifiedResponders, setNotifiedResponders] = useState([]);
  const [alert, setAlert] = useState("");
  const [fetchError, setFetchError] = useState(null);
  const [chatMessages, setChatMessages] = useState([]);
  const [chatInput, setChatInput] = useState("");
  const [aiChatSummary, setAiChatSummary] = useState("");
  const [loading, setLoading] = useState(true);
  const [eventLatLng, setEventLatLng] = useState(null);
  const [taskLocations, setTaskLocations] = useState({});
  const [reportToLocations, setReportToLocations] = useState({});
  const [showMapModal, setShowMapModal] = useState("");
  const [showResponderModal, setShowResponderModal] = useState(false);
  const [activeCardType, setActiveCardType] = useState(null);
  const [responderData, setResponderData] = useState([]);
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [lastUpdated, setLastUpdated] = useState(Date.now()); // For forcing re-renders

  // Handle StatCard click to show responder data in modal
  const handleStatCardClick = (cardType) => {
    setActiveCardType(cardType);
    let filteredData = [];
    let locationName = '';
    
    // Helper function to get responder name from various possible sources
    const getResponderName = (item) => {
      // Log the item to debug
      // console.log('Responder data:', JSON.stringify(item));
      
      // Find the assigned user if available
      if (item.assigned_to) {
        // console.log('Found assigned_to:', item.assigned_to);
        // Try to find the user in responders array
        const assignedUser = responders.find(r => r.id === item.assigned_to);
        if (assignedUser) {
          return (
            assignedUser.first_name + " " + assignedUser.last_name ||
            assignedUser.name ||
            assignedUser.username ||
            `User ${item.assigned_to}`
          ) + (assignedUser.job_role ? ` (${assignedUser.job_role})` : "");

        }
      }
      
      // Try all possible paths to find a name
      return (item.first_name + " " + item.last_name || item.responderName || 
             item.name || 
             item.username || 
             (item.user ? (item.user.name || item.user.username) : null) || 
             (item.responder ? (item.responder.name || item.responder.username) : null) || 
             (item.userId ? `User ${item.userId}` : null) || 
             (item.assigned_to ? `User ${item.assigned_to}` : null) || 
             (item.responderId ? `Responder ${item.responderId}` : null) || 
             'Unknown') + (item.job_role ? ` (${item.job_role})` : "");
    };
    
    console.log('Card type:', cardType);
    
    const locationArray = Object.values(locationsData);
    switch(cardType) {
      case 'notified':
        filteredData = notifiedResponders.map(responder => ({
          name: getResponderName(responder),
          status: 'Notified',
          timestamp: responder.notifiedAt || responder.createdAt,
          rawData: responder // Store the original data for debugging
        }));
        break;
      case 'acknowledged':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'acknowledged')
          .map(task => {
            // Calculate individual ETA for this responder
            const responder = responders.find(r => r.id === task.assigned_to);
            let individualETA = 'N/A';
            if (responder) {
              const etaResult = calculateETA(responder, task.id);
              individualETA = etaResult.value;
            }

            return {
              name: getResponderName(task),
              status: 'acknowledged',
              timestamp: task.updatedAt || task.createdAt,
              eta: individualETA,
              rawData: task
            };
          });
        break;
      case 'enroute':
        // For the main enroute card, show all enroute tasks
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'enroute')
          .map(task => {
            // Find matching location data
            const taskLocation = task.report_to_location || formatLocation(eventInfo.location);
            const matchedLocationData = locationsData[taskLocation];

            // Calculate individual ETA for this responder
            const responder = responders.find(r => r.id === task.assigned_to);
            let individualETA = 'N/A';
            if (responder) {
              const etaResult = calculateETA(responder, task.id);
              individualETA = etaResult.value;
            }

            return {
              name: getResponderName(task),
              status: 'Enroute',
              timestamp: task.updatedAt || task.createdAt,
              eta: individualETA,
              rawData: task
            };
          });
        break;
      case 'delayed':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'delayed')
          .map(task => {
            // Find matching location by comparing fullAddress with task.report_to_location
            const matchedLocation = locationArray.find(loc =>
              loc.fullAddress === task.report_to_location
            );

            return {
              name: getResponderName(task),
              status: 'delayed',
              timestamp: task.updatedAt || task.createdAt,
              eta: matchedLocation?.etaToStaffed || 'N/A',
              rawData: task
            };
          });
        break;
      case 'unable':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'unable')
          .map(task => {
            // Find matching location by comparing fullAddress with task.report_to_location
            const matchedLocation = locationArray.find(loc =>
              loc.fullAddress === task.report_to_location
            );

            return {
              name: getResponderName(task),
              status: 'unable',
              timestamp: task.updatedAt || task.createdAt,
              eta: matchedLocation?.etaToStaffed || 'N/A',
              rawData: task
            };
          });
        break;
      case 'arrived':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'arrived')
          .map(task => {
            // Find matching location by comparing fullAddress with task.report_to_location
            const matchedLocation = locationArray.find(loc =>
              loc.fullAddress === task.report_to_location
            );

            return {
              name: getResponderName(task),
              status: 'Arrived',
              timestamp: task.updatedAt || task.createdAt,
              eta: matchedLocation?.etaToStaffed || 'N/A',
              rawData: task
            };
          });
        break;
      case 'completed':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'completed')
          .map(task => {
            // Find matching location by comparing fullAddress with task.report_to_location
            const matchedLocation = locationArray.find(loc =>
              loc.fullAddress === task.report_to_location
            );

            return {
              name: getResponderName(task),
              status: 'Completed',
              timestamp: task.updatedAt || task.createdAt,
              eta: matchedLocation?.etaToStaffed || 'N/A',
              rawData: task
            };
          });
        break;
      case 'primaryLocation':
        // console.log('Primary location:', eventInfo.location);
        // For primary location, show all tasks reporting to the primary location
        locationName = formatLocation(eventInfo.location);
        
        filteredData = tasks
          .filter(t => !t.report_to_location || isMatchingLocation(t.report_to_location, eventInfo.location))
          .map(task => ({
            name: getResponderName(task),
            status: task.status || 'Unknown',
            timestamp: task.updatedAt || task.createdAt,
            eta: task.eta || 'N/A',
            rawData: task // Store raw data for debugging
          }));
        break;
      default:
        // Handle additional locations or location-specific enroute/delayed
        if (cardType.startsWith('additionalLocation_')) {
          
          console.log('additionalLocation_enroute', cardType);
          const parts = cardType.split('_');
          const locationIndex = parseInt(parts[1] || '0');
          const additionalLocations = Object.keys(locationsData).filter(loc => loc !== formatLocation(eventInfo.location));
          
          if (additionalLocations.length > locationIndex) {
            locationName = additionalLocations[locationIndex];
            let locationFullAddress = locationsData[locationName]?.fullAddress;
            
            // Check if this is for a specific status (enroute/delayed)
            if (parts.length > 2 && parts[2] === 'enroute') {
              // Handle enroute for this location
              filteredData = tasks
                .filter(t => t.status?.toLowerCase() === 'enroute' && t.report_to_location === locationFullAddress)
                .map(task => ({
                  name: getResponderName(task),
                  status: 'Enroute',
                  timestamp: task.updatedAt || task.createdAt,
                  // eta: task.eta || 'N/A',
                  eta: locationsData[locationName].etaToStaffed || 'N/A',
                  rawData: task
                }));
              // Set a specific title
              setActiveCardType(`enroute_at_${locationName}`);
            } else if (parts.length > 2 && parts[2] === 'delayed') {
              // Handle delayed for this location
              filteredData = tasks
                .filter(t => t.status?.toLowerCase() === 'delayed' && t.report_to_location === locationFullAddress)
                .map(task => ({
                  name: getResponderName(task),
                  status: 'Delayed',
                  timestamp: task.updatedAt || task.createdAt,
                  eta: locationsData[locationName].etaToStaffed || 'N/A',
                  rawData: task
                }));
              // Set a specific title
              setActiveCardType(`delayed_at_${locationName}`);
            } else {
              // Show all tasks for this location
              filteredData = tasks
                .filter(t => t.report_to_location === locationFullAddress)
                .map(task => ({
                  name: getResponderName(task),
                  status: task.status || 'Unknown',
                  timestamp: task.updatedAt || task.createdAt,
                  eta: locationsData[locationName].etaToStaffed || 'N/A',
                  rawData: task
                }));
            }
          }
        } else if (cardType.startsWith('primaryLocation_')) {
          // Handle specific status for primary location
          locationName = formatLocation(eventInfo.location);
          // locationName = locationName.replace(/^[^,]+,\s*/, '');
          const status = cardType.split('_')[1];

          // console.log('status', tasks);
          // console.log('location data: ', locationsData);
          // console.log('location name: ', locationName);
          // console.log('location name data: ', locationsData[locationName]);
          
          
          
          if (status === 'enroute') {
            filteredData = tasks
              .filter(t => t.status?.toLowerCase() === 'enroute' && isMatchingLocation(t.report_to_location, locationName))
              .map(task => {
              // Find matching location by comparing fullAddress with task.report_to_location
              const matchedLocation = locationArray.find(loc =>
                loc.fullAddress === task.report_to_location
              );

              return {
                name: getResponderName(task),
                status: 'Enroute',
                timestamp: task.updatedAt || task.createdAt,
                eta: matchedLocation?.etaToStaffed || 'N/A',
                rawData: task
              };
            });
          } else if (status === 'delayed') {
            filteredData = tasks
              .filter(t => t.status?.toLowerCase() === 'delayed' && isMatchingLocation(t.report_to_location, locationName))
              .map(task => {
              // Find matching location by comparing fullAddress with task.report_to_location
              const matchedLocation = locationArray.find(loc =>
                loc.fullAddress === task.report_to_location
              );

              return {
                name: getResponderName(task),
                status: 'Delayed',
                timestamp: task.updatedAt || task.createdAt,
                eta: matchedLocation?.etaToStaffed || 'N/A',
                rawData: task
              };
            });
          }
        } else if (cardType.startsWith('additionalLocation_') && cardType.includes('_enroute')) {
          
          // Handle enroute for a specific additional location
          const locationParts = cardType.split('_');
          const locationIndex = parseInt(locationParts[1] || '0');
          const additionalLocations = Object.keys(locationsData).filter(loc => loc !== formatLocation(eventInfo.location));
          if (additionalLocations.length > locationIndex) {
            locationName = additionalLocations[locationIndex];
            filteredData = tasks
              .filter(t => t.status?.toLowerCase() === 'enroute' && t.report_to_location === locationName)
              .map(task => {
              // Find matching location by comparing fullAddress with task.report_to_location
              const matchedLocation = locationArray.find(loc =>
                loc.fullAddress === task.report_to_location
              );

              return {
                name: getResponderName(task),
                status: 'Enroute',
                timestamp: task.updatedAt || task.createdAt,
                eta: matchedLocation?.etaToStaffed || 'N/A',
                rawData: task
              };
            });
          }
        }
        break;
    }
    
    // Set a custom title for location-based views
    if (locationName && (cardType === 'primaryLocation' || cardType.startsWith('additionalLocation_'))) {
      setActiveCardType(`location_${locationName}`);
    }
    
    // For primary location enroute, we need special handling
    if (cardType === 'primaryLocation' && filteredData.length === 0) {
      // Try to get enroute tasks for this location specifically
      const primaryLocationName = formatLocation(eventInfo.location);
      const enrouteTasks = tasks.filter(t => 
        t.status?.toLowerCase() === 'enroute' && 
        (!t.report_to_location || t.report_to_location === primaryLocationName)
      );
      
      if (enrouteTasks.length > 0) {
        // console.log('Found enroute tasks for primary location:', enrouteTasks);
        filteredData = enrouteTasks.map(task => ({
          name: getResponderName(task),
          status: 'Enroute',
          timestamp: task.updatedAt || task.createdAt,
          eta: task.eta || 'N/A',
          rawData: task
        }));
      }
    }
    
    // Sort the data based on sortField and sortDirection
    const sortedData = [...filteredData].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];
      
      // Handle special cases for sorting
      if (sortField === 'eta') {
        // Convert ETA strings to minutes for sorting
        aValue = aValue === 'N/A' ? 9999 : parseInt(aValue);
        bValue = bValue === 'N/A' ? 9999 : parseInt(bValue);
      } else if (sortField === 'timestamp') {
        // Convert timestamps to Date objects for sorting
        aValue = aValue ? new Date(aValue) : new Date(0);
        bValue = bValue ? new Date(bValue) : new Date(0);
      }
      
      // Sort based on direction
      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    setResponderData(sortedData);
    setShowResponderModal(true);
  };

  // Chat message handler
  const handleSendMessage = async () => {
    if (chatInput.trim() && token) {
      try {
        // Send message via API (which will also broadcast via Socket.IO)
        const response = await fetch(`${baseUrl}/events/${eventId}/chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            text: chatInput.trim(),
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // Message will be received via Socket.IO broadcast, so just clear input
            setChatInput("");
            toast.success("Message sent successfully!");
          } else {
            throw new Error(data.error || 'Failed to send message');
          }
        } else {
          throw new Error('Failed to send message');
        }
      } catch (error) {
        console.error("Error sending message:", error);
        toast.error("Failed to send message. Please try again.");
      }
    }
  };

  // AI Chat Summary handler
  const handleSummarizeChat = async () => {
    if (chatMessages.length === 0) {
      toast.error("No chat messages to summarize");
      return;
    }

    try {
      const chatText = chatMessages
        .map(msg => `${msg.username}: ${msg.text}`)
        .join('\n');

      // Check if API endpoint exists
      try {
        const response = await fetch(`${baseUrl}/ai/summarize-chat`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({
            eventId: eventId,
            chatText: chatText,
          }),
        });

        if (!response.ok) {
          throw new Error('API not implemented');
        }

        const data = await response.json();
        setAiChatSummary(data.summary);
        toast.success("AI summary generated successfully!");
      } catch (apiError) {
        // Fallback: Generate a basic summary without AI
        console.log("AI API not available, using fallback summary");

        const messageCount = chatMessages.length;
        const participants = [...new Set(chatMessages.map(msg => msg.username))];
        const timeRange = chatMessages.length > 0
          ? `${new Date(chatMessages[0].timestamp).toLocaleTimeString()} - ${new Date(chatMessages[chatMessages.length - 1].timestamp).toLocaleTimeString()}`
          : 'N/A';

        const fallbackSummary = `Chat Summary (${messageCount} messages):

Participants: ${participants.join(', ')}
Time Range: ${timeRange}

Recent Activity:
${chatMessages.slice(-5).map(msg => '• ' + msg.username + ': ' + msg.text.substring(0, 100) + (msg.text.length > 100 ? '...' : '')).join('\n')}

Note: This is a basic summary. AI-powered summarization requires backend API setup.`;

        setAiChatSummary(fallbackSummary);
        toast.info("Generated basic summary. AI summarization requires API setup.");
      }
    } catch (error) {
      console.error("Error generating summary:", error);
      toast.error("Failed to generate summary. Please try again.");
    }
  };

  const navigate = useNavigate();

  if (!token) {
    // console.log("No token provided to Dashboard, redirecting to login");
    return <Navigate to="/login" />;
  }

  // Format location for display
  const formatLocation = (location) => {
    if (!location) return "Unknown";
    if (typeof location === "string") return location;
    if (typeof location === "object") {
      const { commonName, address, city, state, zip } = location;
      const namePart = commonName || "";
      const addressPart = address || "";
      const cityPart = city || "";
      const statePart = state || "";
      const zipPart = zip || "";
      return (
        `${namePart}${namePart && addressPart ? ", " : ""}${addressPart}${
          addressPart || namePart ? ", " : ""
        }${cityPart}${cityPart && statePart ? ", " : ""}${statePart} ${zipPart}`
          .trim()
          .replace(/,\s*,/g, ",")
          .replace(/,\s*$/, "") || "Unknown"
      );
    }
    return "Unknown";
  };

  // Check if a task's report_to_location matches the given location
  const isMatchingLocation = (taskLocation, targetLocation) => {
    // If the task has no report_to_location, it's considered to be at the primary location
    if (!taskLocation && targetLocation === eventInfo.location) {
      return true;
    }
    
    // If targetLocation is an object (like eventInfo.location)
    if (typeof targetLocation === 'object') {
      // If taskLocation is a string, it might be the formatted version or just the commonName
      if (typeof taskLocation === 'string') {
        const formattedTarget = formatLocation(targetLocation);
        return taskLocation === formattedTarget || 
               taskLocation === targetLocation.commonName;
      }
      // If both are objects, compare their properties
      if (typeof taskLocation === 'object') {
        return taskLocation.commonName === targetLocation.commonName && 
               taskLocation.address === targetLocation.address;
      }
    }
    
    // Simple string comparison
    return taskLocation === targetLocation;
  };


  const geocodeLocation = useCallback(
    (location, setter, id = null) => {
      if (!mapsLoaded || !window.google || !window.google.maps) {
        // console.log("Google Maps API not loaded yet for geocoding:", location);
        return;
      }
      console.time(`Geocode-${id || "event"}`);
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ address: location }, (results, status) => {
        if (status === "OK") {
          const { lat, lng } = results[0].geometry.location;
          const coords = { lat: lat(), lng: lng() };
          if (id) {
            setter((prev) => {
              const newLocations = { ...prev, [id]: coords };
              return newLocations;
            });
          } else {
            setter(coords);
          }
        } else {
          console.error(
            `Geocoding failed for ${location} (${id || "event"}):`,
            status
          );
          if (!id) setter(null);
        }
        console.timeEnd(`Geocode-${id || "event"}`);
      });
    },
    [mapsLoaded]
  );

  useEffect(() => {
    const fetchData = async (init = false) => {
      if (!token || !eventId) {
        toast.error("Missing required data for fetch: token or event ID!", {
          position: "top-right",
          autoClose: 3000,
        });
        setLoading(false);
        return;
      }

      if (init) {
        setLoading(true);
      }

      try {
        const eventResponse = await fetch(
          `${baseUrl}/active-events/${eventId}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );

        if (!eventResponse.ok) {
          const eventData = await eventResponse.json();
          toast.error(
            `Failed to fetch event: ${eventResponse.status} - ${
              eventData?.error || "Unknown error"
            }`,
            {
              position: "top-right",
              autoClose: 4000,
            }
          );
          setLoading(false);
          return; // Avoid further execution or reload
        }
        const eventData = await eventResponse.json();
        // Map over tasks and update report_to_location
        const updatedTasks = (eventData.tasks || []).map(task => {
          const loc = task.report_to_location || {};
          const report_to_location = `${loc}`
            .replace(/,\s*,/g, ",")
            .replace(/,\s*$/, "")
            .trim();
          return {
            ...task,
            report_to_location,
          };
        });

        setEventInfo({ ...eventData, tasks: undefined });
        setTasks(updatedTasks);

        const assignedIds = eventData.assignIds || [];
        const notifyAll = eventData.notifyAll && assignedIds.length === 0;

        // Build URL with query params only if needed
        let respondersUrl = `${baseUrl}/responders`;

        if (assignedIds.length > 0 && !notifyAll) {
          const params = new URLSearchParams();
          assignedIds.forEach((id) => params.append("ids[]", id));
          respondersUrl += `?${params.toString()}`;
        }

        const respondersResponse = await fetch(respondersUrl, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (!respondersResponse.ok) {
          const errorText = await respondersResponse.text();
          toast.error(
            `Failed to fetch responders: ${respondersResponse.status} - ${errorText}`,
            {
              position: "top-right",
              autoClose: 4000,
            }
          );
          setLoading(false);
          return; // Also avoid further execution
        }

        const allResponders = await respondersResponse.json();

        setResponders(allResponders || []);
        setNotifiedResponders(
          notifyAll
            ? allResponders
            : allResponders.filter((r) => assignedIds.includes(r.id))
        );

        const eventLocation = formatLocation(eventData.location);

        if (eventLocation && mapsLoaded)
          geocodeLocation(eventLocation, setEventLatLng);

        eventData.included_report_to_locations?.forEach((loc) => {
          const locString = `${loc.commonName || ""}, ${loc.address || ""}, ${
            loc.city || ""
          }, ${loc.state || ""} ${loc.zip || ""}`
            .trim()
            .replace(/,\s*,/g, ",")
            .replace(/,\s*$/, "");
          if (locString && mapsLoaded)
            geocodeLocation(locString, setReportToLocations, locString);
        });

        eventData.tasks?.forEach((task) => {
          if (task.report_to_location && mapsLoaded)
            geocodeLocation(task.report_to_location, setTaskLocations, task.id);
        });

        // Load initial chat messages
        try {
          const chatResponse = await fetch(`${baseUrl}/events/${eventId}/chat`, {
            headers: { Authorization: `Bearer ${token}` },
          });

          if (chatResponse.ok) {
            const chatData = await chatResponse.json();
            if (chatData.success && chatData.messages) {
              setChatMessages(chatData.messages);
            }
          }
        } catch (chatError) {
          console.log("Chat messages not available:", chatError);
        }

        setLoading(false);
      } catch (err) {
        toast.error(`Unexpected error occurred: ${err.message}`, {
          position: "top-right",
          autoClose: 4000,
        });
        setFetchError(err.message);
        setLoading(false);
        // Removed navigate to /events
      }
    };

    fetchData(true);
    
    const interval = setInterval(() => {
      fetchData();
    }, 20000); // 20 seconds

    return () => clearInterval(interval);
  }, [token, eventId, mapsLoaded, geocodeLocation]);

  useEffect(() => {
    // console.log("Socket setup useEffect");
    socket.on("connect", () => {
      // console.log("Socket connected to server");
      socket.emit("join", `event-${eventId}`);
      // console.log(`Joined room event-${eventId}`);
    });
    socket.on("connect_error", (err) =>
      console.error("Socket connection error:", err)
    );
    socket.on("new-event", (newEvent) => {
      if (newEvent.id === parseInt(eventId)) {
        // console.log("New event received:", newEvent);
        setEventInfo({ ...newEvent, tasks: undefined });
        setTasks(newEvent.tasks || []);
        if (newEvent.status === "open")
          setAlert(`New event: ${newEvent.title}`);
        const eventLocation = formatLocation(newEvent.location);
        if (eventLocation) geocodeLocation(eventLocation, setEventLatLng);
      }
    });
    socket.on("task-assigned", ({ task }) => {
      // console.log("Task assigned received:", task);
      setTasks((prev) => {
        const newTasks = [...prev.filter((t) => t.id !== task.id), task];
        // console.log("New tasks after task-assigned:", newTasks);
        return newTasks;
      });
      if (task.report_to_location)
        geocodeLocation(task.report_to_location, setTaskLocations, task.id);
    });
    socket.on("task-response", (task) => {
      // console.log("Task response received:", task);
      setTasks((prev) => {
        const updatedTasks = prev.map((t) =>
          t.assigned_to === task.assigned_to && t.event_id === task.event_id
            ? { ...task }
            : t
        );
        // console.log("Updated tasks after task-response:", updatedTasks);
        return updatedTasks;
      });
      if (task.report_to_location)
        geocodeLocation(task.report_to_location, setTaskLocations, task.id);
    });
    socket.on("chat", (message) => {
      setChatMessages((prev) => {
        // Avoid duplicates by checking if message already exists
        const exists = prev.some(msg => msg.id === message.id ||
          (msg.timestamp === message.timestamp && msg.text === message.text && msg.username === message.username));
        return exists ? prev : [...prev, message];
      });
    });
    socket.on("chat-message", (message) => {
      setChatMessages((prev) => {
        // Avoid duplicates by checking if message already exists
        const exists = prev.some(msg => msg.id === message.id ||
          (msg.timestamp === message.timestamp && msg.text === message.text && msg.username === message.username));
        return exists ? prev : [...prev, message];
      });
    });
    socket.on("responder-update", (responder) => {
      // console.log("Responder update received:", responder);
      setResponders((prev) =>
        prev.map((r) => (r.id === responder.id ? { ...r, ...responder } : r))
      );
    });

    return () => {
      // console.log("Cleaning up socket listeners");
      socket.off("connect");
      socket.off("connect_error");
      socket.off("new-event");
      socket.off("task-assigned");
      socket.off("task-response");
      socket.off("chat");
      socket.off("chat-message");
      socket.off("responder-update");
      socket.emit("leave", `event-${eventId}`);
    };
  }, [eventId, geocodeLocation]);

  const getResponderStatusData = useCallback(() => {
    const statusData = responders.map((responder) => {
      const assignedTask = tasks.find((t) => t.assigned_to === responder.id);
      const taskStatus = assignedTask
        ? (assignedTask.status || "pending").toLowerCase()
        : "available";
      return {
        username: responder.username || "Unknown",
        notified: notifiedResponders.some((r) => r.id === responder.id),
        total: true,
        assigned: !!assignedTask,
        acknowledged: taskStatus === "acknowledged",
        enroute: taskStatus === "enroute",
        delayed: taskStatus === "delayed",
        unable: taskStatus === "unable",
        available: !assignedTask,
        job_role: responder.job_role || "Unknown",
      };
    });

    return statusData;
  }, [responders, tasks, notifiedResponders]);

  const calculateETA = useCallback(
    (responder, taskId) => {
      if (
        !responder ||
        !responder.latitude ||
        !responder.longitude ||
        !taskLocations[taskId]
      ) {
        return { value: "TBD", fresh: false };
      }
      const R = 6371; // Earth radius in km
      const dLat =
        ((taskLocations[taskId].lat - responder.latitude) * Math.PI) / 180;
      const dLon =
        ((taskLocations[taskId].lng - responder.longitude) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((responder.latitude * Math.PI) / 180) *
          Math.cos((taskLocations[taskId].lat * Math.PI) / 180) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c; // Distance in km
      const speed = 50; // km/h
      const etaMinutes = Math.round((distance / speed) * 60);

      const now = new Date();
      const lastUpdate = new Date(responder.last_updated || now);
      const timeSinceUpdate = (now - lastUpdate) / (1000 * 60); // Minutes since update
      const fresh = timeSinceUpdate < 1;
      let adjustedETA = etaMinutes;
      if (!fresh && timeSinceUpdate > 1) {
        adjustedETA = Math.max(0, etaMinutes - Math.round(timeSinceUpdate));
      }
      return { value: `${adjustedETA} min`, fresh };
    },
    [taskLocations]
  );

  const getLocationsData = useCallback(() => {
    const locations = {};
    const eventLocation = formatLocation(eventInfo?.location);
    
    locations[eventLocation] = {
      assigned: [],
      enroute: [],
      arrived: [],
      staffNeeded:
        eventInfo?.included_report_to_locations?.reduce(
          (sum, loc) => sum + (loc.staffNeeded || 2),
          0
        ) || 0,
      resources: [],
      responding: [],
      locationTitle: eventInfo?.title,
      fullAddress: eventLocation
    };
    
    // Create a mapping of addresses to location titles for consistent key lookup
    const addressToTitleMap = {};
    
    eventInfo?.included_report_to_locations?.forEach((loc) => {
      
      // Use commonName as locationTitle if available, otherwise fallback to address
      const locationTitle = loc.commonName || `${loc.address || loc.location || "Unknown"}, ${
        loc.city || ""
      }, ${loc.state || ""} ${loc.zip || ""}`
        .replace(/,\s*,/g, ",")
        .replace(/,\s*$/, "")
        .trim();
      const fullAddress = `${loc.address || loc.location || "Unknown"}, ${loc.city || ""}, ${loc.state || ""} ${loc.zip || ""}`
        .replace(/,\s*,/g, ",")
        .replace(/,\s*$/, "")
        .trim();
      
      // Store the address to title mapping for later lookup
      if (loc.address) {
        addressToTitleMap[loc.address] = locationTitle;
      }
      if (loc.location) {
        addressToTitleMap[loc.location] = locationTitle;
      }
      
      locations[locationTitle] = {
        assigned: [],
        enroute: [],
        arrived: [],
        staffNeeded: loc.staffNeeded || 2,
        resources: loc.resources || [],
        responding: [],
        locationTitle: loc.commonName || locationTitle, // Store the title for reference
        fullAddress: fullAddress
      };
    });

    tasks.forEach((task) => {
      
      const responder = responders.find((r) => r.id === task.assigned_to);
      if (!responder) {
        // console.log(`Responder not found for task:`, task);
        return;
      }
      const locParts = eventInfo?.included_report_to_locations?.find(
        (loc) =>
          loc.address === task.report_to_location ||
          loc.location === task.report_to_location
      ) || {
        address: task.report_to_location || eventInfo?.location || "Unknown",
        city: "",
        state: "",
        zip: "",
        commonName: ""
      };
      // Find the appropriate location key using the address mapping or fallback to formatting
      let locKey;
      
      // First check if we have this location in our mapping
      if (locParts.address && addressToTitleMap[locParts.address]) {
        locKey = addressToTitleMap[locParts.address];
      } else if (locParts.location && addressToTitleMap[locParts.location]) {
        locKey = addressToTitleMap[locParts.location];
      } else if (locParts.commonName) {
        // Try to find by commonName
        locKey = locParts.commonName;
      } else {
        // Fallback to formatted address
        locKey = `${locParts.address}, ${locParts.city}, ${locParts.state} ${locParts.zip}`
          .replace(/,\s*,/g, ",")
          .replace(/,\s*$/, "")
          .trim();
      }
      
      // If we still don't have a valid location, use the event location as fallback
      if (!locations[locKey]) {
        // console.log(`Location not found for key: ${locKey}, using event location as fallback`);
        locKey = eventLocation;
      }
      
      const status = task.status?.toLowerCase() || "pending";
      const eta = calculateETA(responder, task.id);
      const responderData = {
        name: responder.username + "(" + responder.job_role + ")" || `Responder ${task.assigned_to}`,
        eta,
      };

      if (locations[locKey] && ["acknowledged", "enroute", "delayed"].includes(status)) {
        locations[locKey].assigned.push(responderData);
        if (status === "enroute") {
          locations[locKey].enroute.push(responderData);
          locations[locKey].responding.push(responderData.name);
        }
      }
      if (locations[locKey] && status === "arrived") {
        locations[locKey].arrived.push(responderData);
        locations[locKey].responding.push(responderData.name);
      }
    });

    Object.keys(locations).forEach((loc) => {
      const enrouteETAs = locations[loc].enroute.map(
        (r) => parseInt(r.eta.value) || Infinity
      );
      const etaToStaffed =
        locations[loc].staffNeeded <= locations[loc].enroute.length
          ? "Staffed"
          : enrouteETAs.length > 0
          ? `${Math.min(...enrouteETAs)} min`
          : "N/A";
      locations[loc].etaToStaffed = etaToStaffed;

      const resources = locations[loc].resources;
      if (resources.length > 0) {
        const enrouteCount = locations[loc].enroute.length;
        let totalAssigned = 0;
        let resourceIndex = 0;
        while (
          resourceIndex < resources.length &&
          totalAssigned < enrouteCount
        ) {
          totalAssigned += resources[resourceIndex].responderCount || 2;
          resourceIndex++;
        }
        const nextResource = resources[resourceIndex];
        const respondersToNext = nextResource
          ? (nextResource.responderCount || 2) -
            (enrouteCount -
              (totalAssigned - (nextResource?.responderCount || 2)))
          : 0;
        const etaToNext =
          respondersToNext <= 0
            ? "Ready"
            : enrouteETAs.length > 0
            ? `${Math.min(...enrouteETAs)} min`
            : "N/A";
        locations[loc].etaToNextResource = etaToNext;
      } else {
        locations[loc].etaToNextResource = "N/A";
      }
    });

    return locations;
  }, [eventInfo, tasks, responders, calculateETA]);

  const locationsData = getLocationsData();

  useEffect(() => {}, [tasks, taskLocations, reportToLocations]);

  return (
    <>
      <style>
        {`
          /* Mobile Responsive Styles */
          @media (max-width: 768px) {
            .dashboard-container {
              padding: 8px !important;
              min-height: 100vh !important;
            }
            .container {
              padding: 10px !important;
              max-width: 100% !important;
            }
            .dashboard-header {
              padding: 12px !important;
              margin-bottom: 15px !important;
              border-radius: 6px !important;
            }
            .dashboard-header h1 {
              font-size: 18px !important;
              line-height: 1.3 !important;
            }
            .dashboard-header h2 {
              font-size: 14px !important;
              line-height: 1.4 !important;
              margin: 6px 0 0 0 !important;
            }
            .event-details-grid {
              flex-direction: column !important;
              gap: 8px !important;
              align-items: stretch !important;
            }
            .event-detail-box {
              min-width: auto !important;
              width: 100% !important;
              padding: 8px 12px !important;
              text-align: center !important;
            }
            .event-detail-box div:first-child {
              font-size: 10px !important;
            }
            .event-detail-box div:last-child {
              font-size: 14px !important;
            }
            .status-cards-grid {
              flex-wrap: wrap !important;
              gap: 4px !important;
              justify-content: space-between !important;
            }
            .status-card {
              flex: 1 1 calc(50% - 2px) !important;
              min-width: calc(50% - 2px) !important;
              max-width: calc(50% - 2px) !important;
              padding: 6px !important;
              margin: 0 !important;
            }
            .status-card-icon {
              font-size: 14px !important;
              margin-bottom: 3px !important;
            }
            .status-card-number {
              font-size: 16px !important;
              margin-bottom: 2px !important;
              line-height: 1.2 !important;
            }
            .status-card-label {
              font-size: 9px !important;
              line-height: 1.2 !important;
              word-wrap: break-word !important;
            }
            .live-event-cards-grid {
              flex-wrap: wrap !important;
              gap: 4px !important;
              justify-content: space-between !important;
            }
            .live-event-card {
              flex: 1 1 calc(50% - 2px) !important;
              min-width: calc(50% - 2px) !important;
              max-width: calc(50% - 2px) !important;
              padding: 6px !important;
              margin: 0 !important;
            }
            .chat-ai-container {
              display: flex !important;
              flex-direction: column !important;
              gap: 12px !important;
            }
            .chat-section, .ai-section {
              width: 100% !important;
              margin: 0 !important;
            }
            .map-section {
              height: 250px !important;
              margin-bottom: 15px !important;
            }
            .modern-card {
              padding: 20px !important;
              margin-bottom: 15px !important;
              border-radius: 8px !important;
            }
            .loading-card, .error-card, .no-data-card {
              padding: 25px !important;
              font-size: 16px !important;
            }
            .loading-card div:first-child {
              width: 30px !important;
              height: 30px !important;
              margin-bottom: 15px !important;
            }
            .error-card div:first-child, .no-data-card div:first-child {
              font-size: 36px !important;
              margin-bottom: 15px !important;
            }
            /* Document Summary Mobile Styles */
            .document-summary-container {
              padding: 12px !important;
            }
            .document-summary-container h3 {
              font-size: 16px !important;
            }
            .document-item {
              padding: 10px !important;
              margin-bottom: 10px !important;
            }
            .document-actions {
              flex-direction: column !important;
              gap: 8px !important;
            }
            .document-actions button {
              width: 100% !important;
              padding: 8px !important;
              font-size: 12px !important;
            }
            /* Responder List Mobile Styles */
            .responder-item {
              padding: 8px !important;
              margin-bottom: 8px !important;
            }
            .responder-info {
              font-size: 12px !important;
            }
            .responder-status {
              font-size: 10px !important;
              padding: 2px 6px !important;
            }
            /* Chat Input Mobile Styles */
            .chat-input-container {
              flex-direction: column !important;
              gap: 8px !important;
            }
            .chat-input {
              width: 100% !important;
              padding: 12px !important;
              font-size: 16px !important;
              border-radius: 8px !important;
            }
            .chat-send-button {
              width: 100% !important;
              padding: 12px !important;
              font-size: 16px !important;
              border-radius: 8px !important;
              min-height: 48px !important;
            }
            /* Table Mobile Styles */
            .responder-table {
              font-size: 12px !important;
            }
            .responder-table th, .responder-table td {
              padding: 6px !important;
              word-wrap: break-word !important;
            }
          }

          /* Tablet Styles */
          @media (min-width: 769px) and (max-width: 1024px) {
            .dashboard-container {
              padding: 12px !important;
            }
            .container {
              padding: 20px 15px !important;
            }
            .dashboard-header {
              padding: 18px !important;
            }
            .dashboard-header h1 {
              font-size: 22px !important;
            }
            .dashboard-header h2 {
              font-size: 18px !important;
            }
            .event-details-grid {
              gap: 12px !important;
            }
            .event-detail-box {
              padding: 10px 15px !important;
            }
            .status-cards-grid {
              gap: 6px !important;
              flex-wrap: nowrap !important;
            }
            .status-card {
              padding: 10px !important;
              flex: 1 !important;
            }
            .status-card-number {
              font-size: 20px !important;
            }
            .status-card-label {
              font-size: 11px !important;
            }
            .live-event-cards-grid {
              gap: 6px !important;
              flex-wrap: nowrap !important;
            }
            .live-event-card {
              padding: 10px !important;
              flex: 1 !important;
            }
            .chat-ai-container {
              gap: 18px !important;
            }
            .map-section {
              height: 400px !important;
            }
            .modern-card {
              padding: 25px !important;
            }
          }

          /* Desktop Styles */
          @media (min-width: 1025px) {
            .dashboard-container {
              padding: 30px 20px !important;
            }
          }

          /* Touch and Interaction Improvements */
          .status-card, .live-event-card {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
          }

          .status-card:active, .live-event-card:active {
            transform: scale(0.98) !important;
          }

          .chat-input {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
          }

          .chat-send-button {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
          }

          /* Prevent zoom on input focus for iOS */
          @media screen and (-webkit-min-device-pixel-ratio: 0) {
            .chat-input {
              font-size: 16px !important;
            }
          }

          /* Improve scrolling on mobile */
          .dashboard-container {
            -webkit-overflow-scrolling: touch;
            overflow-x: hidden;
          }

          /* Better button spacing for touch */
          @media (max-width: 768px) {
            button {
              min-height: 44px !important;
              min-width: 44px !important;
            }
          }
        `}
      </style>
      <div
        className="dashboard-container"
        style={{
          minHeight: "100vh",
          background: "#f0f0f0",
          fontFamily: "'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
        }}
      >
      <div
        className="container"
        style={{
          padding: "30px 20px",
          maxWidth: "1400px",
          margin: "0 auto"
        }}
      >
        <ErrorBoundary>
          {loading ? (
            <div
              className="modern-card loading-card"
              style={{
                padding: "40px",
                borderRadius: "20px",
                background: "rgba(255, 255, 255, 0.95)",
                backdropFilter: "blur(10px)",
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                textAlign: "center",
                fontSize: "18px",
                color: "#4a5568",
                border: "1px solid rgba(255, 255, 255, 0.2)"
              }}
            >
              <div style={{
                display: "inline-block",
                width: "40px",
                height: "40px",
                border: "4px solid #e2e8f0",
                borderTop: "4px solid #667eea",
                borderRadius: "50%",
                animation: "spin 1s linear infinite",
                marginBottom: "20px"
              }}></div>
              <div>Loading event data...</div>
            </div>
          ) : fetchError ? (
            <div
              className="modern-card error-card"
              style={{
                padding: "40px",
                borderRadius: "20px",
                background: "rgba(255, 255, 255, 0.95)",
                backdropFilter: "blur(10px)",
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                color: "#e53e3e",
                textAlign: "center",
                fontSize: "18px",
                border: "1px solid rgba(229, 62, 62, 0.2)"
              }}
            >
              <div style={{ fontSize: "48px", marginBottom: "20px" }}>⚠️</div>
              <div>Error: {fetchError}</div>
            </div>
          ) : !eventInfo ? (
            <div
              className="modern-card no-data-card"
              style={{
                padding: "40px",
                borderRadius: "20px",
                background: "rgba(255, 255, 255, 0.95)",
                backdropFilter: "blur(10px)",
                boxShadow: "0 20px 40px rgba(0,0,0,0.1)",
                textAlign: "center",
                fontSize: "18px",
                color: "#4a5568",
                border: "1px solid rgba(255, 255, 255, 0.2)"
              }}
            >
              <div style={{ fontSize: "48px", marginBottom: "20px" }}>📊</div>
              <div>No event data available</div>
            </div>
          ) : (
          <>
            {/* Clean Dashboard Header */}
            <section
              className="dashboard-header"
              style={{
                padding: "20px",
                marginBottom: "20px",
                backgroundColor: "#ffffff",
                border: "1px solid #e5e7eb",
                borderRadius: "8px",
                boxShadow: "0 1px 3px rgba(0,0,0,0.1)"
              }}
            >
              {/* Event Name - First Line */}
              <div style={{ textAlign: "center", marginBottom: "15px" }}>
                <h1 style={{
                  color: "#1f2937",
                  fontSize: "24px",
                  fontWeight: "600",
                  margin: "0",
                  lineHeight: "1.2"
                }}>
                  Live Event Dashboard
                </h1>
                <h2 style={{
                  color: "#374151",
                  fontSize: "20px",
                  fontWeight: "500",
                  margin: "8px 0 0 0",
                  lineHeight: "1.3"
                }}>
                  {eventInfo.title || "Event Title Not Available"}
                </h2>
              </div>

              {/* Event Details - Second Line */}
              <div className="event-details-grid" style={{
                display: "flex",
                justifyContent: "center",
                gap: "15px",
                flexWrap: "wrap",
                alignItems: "center"
              }}>
                <div className="event-detail-box" style={{
                  backgroundColor: "#f9fafb",
                  border: "1px solid #d1d5db",
                  padding: "10px 15px",
                  borderRadius: "6px",
                  textAlign: "center",
                  minWidth: "120px"
                }}>
                  <div style={{ fontSize: "12px", color: "#6b7280", marginBottom: "2px" }}>Event ID</div>
                  <div style={{ fontSize: "16px", fontWeight: "600", color: "#1f2937" }}>
                    {eventId}
                  </div>
                </div>

                <div className="event-detail-box" style={{
                  backgroundColor: "#f9fafb",
                  border: "1px solid #d1d5db",
                  padding: "10px 15px",
                  borderRadius: "6px",
                  textAlign: "center",
                  minWidth: "120px"
                }}>
                  <div style={{ fontSize: "12px", color: "#6b7280", marginBottom: "2px" }}>Launch Date</div>
                  <div style={{ fontSize: "16px", fontWeight: "600", color: "#1f2937" }}>
                    {eventInfo.created_at ? new Date(eventInfo.created_at).toLocaleDateString() : "N/A"}
                  </div>
                </div>

                <div className="event-detail-box" style={{
                  backgroundColor: "#f9fafb",
                  border: "1px solid #d1d5db",
                  padding: "10px 15px",
                  borderRadius: "6px",
                  textAlign: "center",
                  minWidth: "120px"
                }}>
                  <div style={{ fontSize: "12px", color: "#6b7280", marginBottom: "2px" }}>Launch Time</div>
                  <div style={{ fontSize: "16px", fontWeight: "600", color: "#1f2937" }}>
                    {eventInfo.created_at ? new Date(eventInfo.created_at).toLocaleTimeString() : "N/A"}
                  </div>
                </div>

                <div className="event-detail-box" style={{
                  backgroundColor: "#f9fafb",
                  border: "1px solid #d1d5db",
                  padding: "10px 15px",
                  borderRadius: "6px",
                  textAlign: "center",
                  minWidth: "120px"
                }}>
                  <div style={{ fontSize: "12px", color: "#6b7280", marginBottom: "2px" }}>Time Elapsed</div>
                  <div style={{ fontSize: "16px", fontWeight: "600", color: "#1f2937" }}>
                    {eventInfo.created_at ? (() => {
                      const launchTime = new Date(eventInfo.created_at);
                      const now = new Date();
                      const diffMs = now - launchTime;
                      const diffMins = Math.floor(diffMs / 60000);
                      const diffHours = Math.floor(diffMins / 60);
                      const remainingMins = diffMins % 60;

                      if (diffHours > 0) {
                        return `${diffHours}h ${remainingMins}m`;
                      } else {
                        return `${diffMins}m`;
                      }
                    })() : "N/A"}
                  </div>
                </div>

                <div className="event-detail-box" style={{
                  backgroundColor: "#f9fafb",
                  border: "1px solid #d1d5db",
                  padding: "10px 15px",
                  borderRadius: "6px",
                  textAlign: "center",
                  minWidth: "120px"
                }}>
                  <div style={{ fontSize: "12px", color: "#6b7280", marginBottom: "2px" }}>Urgency</div>
                  <div style={{ fontSize: "16px", fontWeight: "600", color: "#1f2937" }}>
                    {eventInfo.urgency || "N/A"}
                  </div>
                </div>
              </div>
            </section>
              
              {/* Modern Status Cards Grid */}
              <section
                style={{
                  padding: "20px",
                  marginBottom: "20px",
                  backgroundColor: "#ffffff",
                  border: "1px solid #e5e7eb",
                  borderRadius: "8px",
                  boxShadow: "0 1px 3px rgba(0,0,0,0.1)"
                }}
              >
                <h2 style={{
                  color: "#1f2937",
                  fontSize: "18px",
                  fontWeight: "600",
                  marginBottom: "15px",
                  textAlign: "center"
                }}>
                  Response Status Overview
                </h2>

                <div className="status-cards-grid" style={{
                  display: "flex",
                  flexWrap: "nowrap",
                  gap: "8px",
                  marginBottom: "15px",
                  width: "100%"
                }}>
                  <div className="status-card" style={{
                    background: "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                    borderRadius: "8px",
                    padding: "10px",
                    color: "white",
                    boxShadow: "0 4px 12px rgba(79, 172, 254, 0.3)",
                    cursor: "pointer",
                    transform: "translateY(0)",
                    transition: "all 0.3s ease",
                    flex: "1",
                    textAlign: "center"
                  }}
                  onClick={() => handleStatCardClick('notified')}
                  onMouseEnter={(e) => e.target.style.transform = "translateY(-2px)"}
                  onMouseLeave={(e) => e.target.style.transform = "translateY(0)"}
                  >
                    <div className="status-card-icon" style={{ fontSize: "20px", marginBottom: "6px" }}>📢</div>
                    <div className="status-card-number" style={{ fontSize: "24px", fontWeight: "800", marginBottom: "4px" }}>
                      {notifiedResponders.length}
                    </div>
                    <div className="status-card-label" style={{ fontSize: "12px", fontWeight: "600", opacity: 0.9 }}>
                      Notified
                    </div>
                  </div>

                  <div className="status-card" style={{
                    background: "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
                    borderRadius: "8px",
                    padding: "10px",
                    color: "white",
                    boxShadow: "0 4px 12px rgba(67, 233, 123, 0.3)",
                    cursor: "pointer",
                    transform: "translateY(0)",
                    transition: "all 0.3s ease",
                    flex: "1",
                    textAlign: "center"
                  }}
                  onClick={() => handleStatCardClick('acknowledged')}
                  onMouseEnter={(e) => e.target.style.transform = "translateY(-2px)"}
                  onMouseLeave={(e) => e.target.style.transform = "translateY(0)"}
                  >
                    <div className="status-card-icon" style={{ fontSize: "20px", marginBottom: "6px" }}>✅</div>
                    <div className="status-card-number" style={{ fontSize: "24px", fontWeight: "800", marginBottom: "4px" }}>
                      {tasks.filter(t => t.status?.toLowerCase() === 'acknowledged').length}
                    </div>
                    <div className="status-card-label" style={{ fontSize: "12px", fontWeight: "600", opacity: 0.9 }}>
                      {eventInfo?.event_type === 'notification_only' ? "Participating" : "Acknowledged"}
                    </div>
                  </div>

                  <div className="status-card" style={{
                    background: "linear-gradient(135deg, #fa709a 0%, #fee140 100%)",
                    borderRadius: "8px",
                    padding: "10px",
                    color: "white",
                    boxShadow: "0 4px 12px rgba(250, 112, 154, 0.3)",
                    cursor: "pointer",
                    transform: "translateY(0)",
                    transition: "all 0.3s ease",
                    flex: "1",
                    textAlign: "center"
                  }}
                  onClick={() => handleStatCardClick('enroute')}
                  onMouseEnter={(e) => e.target.style.transform = "translateY(-2px)"}
                  onMouseLeave={(e) => e.target.style.transform = "translateY(0)"}
                  >
                    <div className="status-card-icon" style={{ fontSize: "20px", marginBottom: "6px" }}>🚗</div>
                    <div className="status-card-number" style={{ fontSize: "24px", fontWeight: "800", marginBottom: "4px" }}>
                      {tasks.filter(t => t.status?.toLowerCase() === 'enroute').length}
                    </div>
                    <div className="status-card-label" style={{ fontSize: "12px", fontWeight: "600", opacity: 0.9 }}>
                      {eventInfo?.event_type === 'notification_only' ? "Unable to Participate" : "Enroute"}
                    </div>
                  </div>

                  <div className="status-card" style={{
                    background: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)",
                    borderRadius: "8px",
                    padding: "10px",
                    color: "#2d3748",
                    boxShadow: "0 4px 12px rgba(168, 237, 234, 0.3)",
                    cursor: "pointer",
                    transform: "translateY(0)",
                    transition: "all 0.3s ease",
                    flex: "1",
                    textAlign: "center"
                  }}
                  onClick={() => handleStatCardClick('delayed')}
                  onMouseEnter={(e) => e.target.style.transform = "translateY(-2px)"}
                  onMouseLeave={(e) => e.target.style.transform = "translateY(0)"}
                  >
                    <div className="status-card-icon" style={{ fontSize: "20px", marginBottom: "6px" }}>⏰</div>
                    <div className="status-card-number" style={{ fontSize: "24px", fontWeight: "800", marginBottom: "4px" }}>
                      {tasks.filter(t => t.status?.toLowerCase() === 'delayed').length}
                    </div>
                    <div className="status-card-label" style={{ fontSize: "12px", fontWeight: "600" }}>
                      {eventInfo?.event_type === 'notification_only' ? "Removed from Event" : "Delayed"}
                    </div>
                  </div>

                  <div className="status-card" style={{
                    background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    borderRadius: "8px",
                    padding: "10px",
                    color: "white",
                    boxShadow: "0 4px 12px rgba(102, 126, 234, 0.3)",
                    cursor: "pointer",
                    transform: "translateY(0)",
                    transition: "all 0.3s ease",
                    flex: "1",
                    textAlign: "center"
                  }}
                  onClick={() => handleStatCardClick('unable')}
                  onMouseEnter={(e) => e.target.style.transform = "translateY(-2px)"}
                  onMouseLeave={(e) => e.target.style.transform = "translateY(0)"}
                  >
                    <div className="status-card-icon" style={{ fontSize: "20px", marginBottom: "6px" }}>❌</div>
                    <div className="status-card-number" style={{ fontSize: "24px", fontWeight: "800", marginBottom: "4px" }}>
                      {eventInfo?.event_type === 'notification_only' ?
                        Math.max(0, notifiedResponders.length -
                          tasks.filter(t => ['acknowledged', 'enroute', 'delayed'].includes(t.status?.toLowerCase())).length
                        ) :
                        tasks.filter(t => t.status?.toLowerCase() === 'unable').length
                      }
                    </div>
                    <div className="status-card-label" style={{ fontSize: "12px", fontWeight: "600", opacity: 0.9 }}>
                      {eventInfo?.event_type === 'notification_only' ? "No Response" : "Unable/Cancelled"}
                    </div>
                  </div>
                </div>
              </section>

              {/* Report to Locations Section - Only for 'response' event types */}
              {eventInfo?.event_type === 'response' && (
                <section
                  className="card"
                  style={{
                    padding: "20px",
                    marginBottom: "20px",
                    borderRadius: "10px",
                    boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginBottom: "15px",
                    }}
                  >
                    <h2
                      style={{
                        color: "#1a73e8",
                        margin: 0,
                        fontSize: "24px",
                        fontWeight: "bold",
                      }}
                    >
                      Report to Locations
                    </h2>
                  </div>
                  <div style={{ display: "flex", flexWrap: "wrap", gap: "15px", marginBottom: "20px" }}>
                    {/* Event Location Card */}
                    <div
                      onClick={() => handleStatCardClick('primaryLocation')}
                      style={{
                        flex: "1",
                        minWidth: "200px",
                        maxWidth: "300px",
                        border: "2px solid #1565C0",
                        borderRadius: "4px",
                        overflow: "hidden",
                        boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                        cursor: "pointer",
                        transition: "transform 0.2s, box-shadow 0.2s",
                      }}>
                      <div style={{ backgroundColor: "#1565C0", padding: "8px", borderBottom: "1px solid #1565C0", textAlign: "center", fontWeight: "800", fontSize: "14px", color: "white" }}>
                        Primary Event Location
                      </div>
                      <div style={{ padding: "5px", backgroundColor: "#f8f9fa", borderBottom: "1px solid #ddd", fontSize: "13px", textAlign: "center", fontWeight: "bold" }}>
                        {formatLocation(eventInfo.location)}
                      </div>
                      <div>
                        <div
                          style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStatCardClick('primaryLocation_enroute');
                          }}
                        >
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Enroute</div>
                          <div style={{ width: "60%", padding: "6px", backgroundColor: "#8BC34A", color: "#fff", textAlign: "center", fontSize: "13px" }}>
                            {locationsData[formatLocation(eventInfo.location)]?.enroute?.length || 0}
                          </div>
                        </div>
                        <div
                          style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStatCardClick('primaryLocation_delayed');
                          }}
                        >
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Delayed</div>
                          <div style={{ width: "60%", padding: "6px", backgroundColor: "#FFEB3B", color: "#000", textAlign: "center", fontSize: "13px" }}>
                            {tasks.filter(t => t.status?.toLowerCase() === 'delayed' && (!t.report_to_location || t.report_to_location === formatLocation(eventInfo.location))).length}
                          </div>
                        </div>
                        <div style={{ display: "flex" }}>
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>ETA to Staffed</div>
                          <div style={{ width: "60%", padding: "6px", textAlign: "center", fontSize: "13px" }}>
                            {locationsData[formatLocation(eventInfo.location)]?.etaToStaffed || 'N/A'}
                          </div>
                        </div>
                      </div>
                    </div>
                    {/* Additional Location Cards */}
                    {Object.keys(locationsData)
                      .filter(loc => loc !== formatLocation(eventInfo.location))
                      .map((loc, index) => (
                        <div
                          key={index}
                          onClick={() => handleStatCardClick(`additionalLocation_${index}`)}
                          style={{
                            flex: "1",
                            minWidth: "200px",
                            maxWidth: "300px",
                            border: "1px solid #ddd",
                            borderRadius: "4px",
                            overflow: "hidden",
                            cursor: "pointer",
                            transition: "transform 0.2s, box-shadow 0.2s",
                          }}>
                          <div style={{ backgroundColor: index % 3 === 0 ? "#4CAF50" : index % 3 === 1 ? "#FF9800" : "#9C27B0", padding: "8px", borderBottom: "1px solid #ddd", textAlign: "center", fontWeight: "bold", fontSize: "14px", whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis", color: "white" }}>
                            {locationsData[loc]?.locationTitle}
                          </div>
                          <div style={{ padding: "5px", backgroundColor: "#f8f9fa", borderBottom: "1px solid #ddd", fontSize: "13px", textAlign: "center", fontWeight: "bold" }}>
                            {loc}
                          </div>
                          <div>
                            <div
                              style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStatCardClick(`additionalLocation_${index}_enroute`);
                              }}
                            >
                              <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Enroute</div>
                              <div style={{ width: "60%", padding: "6px", backgroundColor: "#8BC34A", color: "#fff", textAlign: "center", fontSize: "13px" }}>
                                {locationsData[loc]?.enroute?.length || 0}
                              </div>
                            </div>
                            <div
                              style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStatCardClick(`additionalLocation_${index}_delayed`);
                              }}
                            >
                              <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Delayed</div>
                              <div style={{ width: "60%", padding: "6px", backgroundColor: "#FFEB3B", color: "#000", textAlign: "center", fontSize: "13px" }}>
                                {tasks.filter(t => t.status?.toLowerCase() === 'delayed' && t.report_to_location === loc).length}
                              </div>
                            </div>
                            <div style={{ display: "flex" }}>
                              <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>ETA to Staffed</div>
                              <div style={{ width: "60%", padding: "6px", textAlign: "center", fontSize: "13px" }}>
                                {locationsData[loc]?.etaToStaffed || 'N/A'}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    }
                  </div>
                </section>
              )}

              {/* Job Role Status Cards Section */}
              {/* <section
                className="modern-card"
                style={{
                  padding: "25px",
                  marginBottom: "30px",
                  borderRadius: "16px",
                  background: "rgba(255, 255, 255, 0.95)",
                  backdropFilter: "blur(20px)",
                  boxShadow: "0 15px 30px rgba(0,0,0,0.1)",
                  border: "1px solid rgba(255, 255, 255, 0.2)"
                }}
              >
                <h2 style={{
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                  fontSize: "20px",
                  fontWeight: "700",
                  marginBottom: "8px",
                  textAlign: "center"
                }}>
                  👥 Job Role Status Overview
                </h2>
                <div style={{
                  width: "50px",
                  height: "2px",
                  background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                  margin: "0 auto 20px auto",
                  borderRadius: "2px"
                }}></div>

                <div style={{ display: "flex", flexWrap: "wrap", gap: "15px", justifyContent: "center" }}>
                  {['commander', 'lead', 'staff', 'viewer'].map((role, index) => {
                    const roleResponders = responders.filter(r => r.job_role?.toLowerCase() === role);
                    const roleTasks = tasks.filter(t => {
                      const responder = responders.find(r => r.id === t.assigned_to);
                      return responder?.job_role?.toLowerCase() === role;
                    });
                    const acknowledgedCount = roleTasks.filter(t => t.status?.toLowerCase() === 'acknowledged').length;
                    const enrouteCount = roleTasks.filter(t => t.status?.toLowerCase() === 'enroute').length;
                    const delayedCount = roleTasks.filter(t => t.status?.toLowerCase() === 'delayed').length;
                    const unableCount = roleTasks.filter(t => t.status?.toLowerCase() === 'unable').length;

                    const roleColors = {
                      commander: { bg: '#dc2626', icon: '👑' },
                      lead: { bg: '#ea580c', icon: '🎯' },
                      staff: { bg: '#059669', icon: '👷' },
                      viewer: { bg: '#7c3aed', icon: '👁️' }
                    };

                    return (
                      <div
                        key={role}
                        style={{
                          background: "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
                          border: `2px solid ${roleColors[role].bg}`,
                          borderRadius: "12px",
                          padding: "15px",
                          minWidth: "200px",
                          flex: "1",
                          maxWidth: "250px",
                          boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
                          transition: "transform 0.2s ease"
                        }}
                        onMouseEnter={(e) => e.target.style.transform = "translateY(-2px)"}
                        onMouseLeave={(e) => e.target.style.transform = "translateY(0)"}
                      >
                        <div style={{
                          background: roleColors[role].bg,
                          color: "white",
                          padding: "8px",
                          borderRadius: "8px",
                          textAlign: "center",
                          marginBottom: "12px",
                          fontWeight: "700",
                          fontSize: "14px",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          gap: "8px"
                        }}>
                          <span style={{ fontSize: "16px" }}>{roleColors[role].icon}</span>
                          {role.charAt(0).toUpperCase() + role.slice(1)}s ({roleResponders.length})
                        </div>

                        <div style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "8px", fontSize: "12px" }}>
                          <div style={{ textAlign: "center", padding: "6px", background: "#dcfce7", borderRadius: "6px" }}>
                            <div style={{ fontWeight: "700", color: "#166534" }}>✅ {acknowledgedCount}</div>
                            <div style={{ color: "#166534", fontSize: "10px" }}>Acknowledged</div>
                          </div>
                          <div style={{ textAlign: "center", padding: "6px", background: "#dbeafe", borderRadius: "6px" }}>
                            <div style={{ fontWeight: "700", color: "#1e40af" }}>🚗 {enrouteCount}</div>
                            <div style={{ color: "#1e40af", fontSize: "10px" }}>Enroute</div>
                          </div>
                          <div style={{ textAlign: "center", padding: "6px", background: "#fef3c7", borderRadius: "6px" }}>
                            <div style={{ fontWeight: "700", color: "#92400e" }}>⏰ {delayedCount}</div>
                            <div style={{ color: "#92400e", fontSize: "10px" }}>Delayed</div>
                          </div>
                          <div style={{ textAlign: "center", padding: "6px", background: "#fee2e2", borderRadius: "6px" }}>
                            <div style={{ fontWeight: "700", color: "#991b1b" }}>❌ {unableCount}</div>
                            <div style={{ color: "#991b1b", fontSize: "10px" }}>Unable</div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </section> */}

              <section
                className="modern-card"
                style={{
                  padding: "35px",
                  marginBottom: "30px",
                  borderRadius: "24px",
                  background: "rgba(255, 255, 255, 0.95)",
                  backdropFilter: "blur(20px)",
                  boxShadow: "0 25px 50px rgba(0,0,0,0.15)",
                  border: "1px solid rgba(255, 255, 255, 0.2)"
                }}
              >
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "10px",
                }}
              >
                <p>
                  <strong>Info:</strong> {eventInfo.info || "N/A"}
                </p>
                <p>
                  <strong>Scale:</strong> {eventInfo.scale || "N/A"}
                </p>
                <p>
                  <strong>Urgency:</strong> {eventInfo.urgency || "N/A"}
                </p>
                <p>
                  <strong>Location:</strong>{" "}
                  {formatLocation(eventInfo.location)}
                </p>
                <p>
                  <strong>Status:</strong> {eventInfo.status || "N/A"}
                </p>
              </div>
              {alert && (
                <div style={{ color: "var(--warning)", marginTop: "10px" }}>
                  {alert}
                </div>
              )}
              </section>



            {/* <pre>{JSON.stringify(eventInfo, null, 2)}</pre> */}

            {/* Report to Locations Section - HIDDEN as requested */}
            {false && (
            <section
              className="card"
              style={{
                padding: "20px",
                marginBottom: "20px",
                borderRadius: "10px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "15px",
                }}
              >
                <h2
                  style={{
                    color: "#1a73e8",
                    margin: 0,
                    fontSize: "24px",
                    fontWeight: "bold",
                  }}
                >
                  Report to Locations
                </h2>
              </div>
              <div style={{ display: "flex", flexWrap: "wrap", gap: "15px", marginBottom: "20px" }}>
                {/* Event Location Card */}
                <div 
                  // onClick={() => handleStatCardClick('primaryLocation')}
                  style={{ 
                    flex: "1", 
                    minWidth: "200px", 
                    maxWidth: "300px", 
                    border: "2px solid #1565C0", 
                    borderRadius: "4px", 
                    overflow: "hidden", 
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    cursor: "pointer",
                    transition: "transform 0.2s, box-shadow 0.2s",
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                    }
                  }}>
                  <div style={{ backgroundColor: "#1565C0", padding: "8px", borderBottom: "1px solid #1565C0", textAlign: "center", fontWeight: "800", fontSize: "14px", color: "white" }}>
                    Primary Event Location
                  </div>
                  <div style={{ padding: "5px", backgroundColor: "#f8f9fa", borderBottom: "1px solid #ddd", fontSize: "13px", textAlign: "center", fontWeight: "bold" }}>
                    {formatLocation(eventInfo.location)}
                  </div>
                  <div>
                    <div 
                      style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering the parent onClick
                        handleStatCardClick('primaryLocation_enroute');
                      }}
                    >
                      <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Enroute</div>
                      <div style={{ width: "60%", padding: "6px", backgroundColor: "#8BC34A", color: "#fff", textAlign: "center", fontSize: "13px" }}>
                        {tasks.filter(t => t.status?.toLowerCase() === 'enroute' && isMatchingLocation(t.report_to_location, formatLocation(eventInfo.location))).length}
                      </div>
                    </div>
                    <div 
                      style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering the parent onClick
                        handleStatCardClick('primaryLocation_delayed');
                      }}
                    >
                      <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Delayed</div>
                      <div style={{ width: "60%", padding: "6px", backgroundColor: "#FFEB3B", color: "#000", textAlign: "center", fontSize: "13px" }}>
                        {tasks.filter(t => t.status?.toLowerCase() === 'delayed' && isMatchingLocation(t.report_to_location, formatLocation(eventInfo.location))).length}
                      </div>
                    </div>
                    <div style={{ display: "flex" }}>
                      <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>ETA to Staffed</div>
                      <div style={{ width: "60%", padding: "6px", textAlign: "center", fontSize: "13px" }}>
                        {locationsData[formatLocation(eventInfo.location)].etaToStaffed}
                        
                      </div>
                    </div>
                  </div>
                </div>
                {/* Additional Location Cards */}
                {Object.keys(locationsData)
                  .filter(loc => loc !== formatLocation(eventInfo.location))
                  .map((loc, index) => (
                    
                    <div 
                      key={index} 
                      // onClick={() => handleStatCardClick(`additionalLocation_${index}`)}
                      style={{ 
                        flex: "1", 
                        minWidth: "200px", 
                        maxWidth: "300px", 
                        border: "1px solid #ddd", 
                        borderRadius: "4px", 
                        overflow: "hidden",
                        cursor: "pointer",
                        transition: "transform 0.2s, box-shadow 0.2s",
                        '&:hover': {
                          transform: 'translateY(-3px)',
                          boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                        }
                      }}>
                      <div style={{ backgroundColor: index % 3 === 0 ? "#4CAF50" : index % 3 === 1 ? "#FF9800" : "#9C27B0", padding: "8px", borderBottom: "1px solid #ddd", textAlign: "center", fontWeight: "bold", fontSize: "14px", whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis", color: "white" }}>
                        {locationsData[loc]?.locationTitle}
                      </div>
                      <div style={{ padding: "5px", backgroundColor: "#f8f9fa", borderBottom: "1px solid #ddd", fontSize: "13px", textAlign: "center", fontWeight: "bold" }}>
                      {locationsData[loc]?.fullAddress}
                      </div>
                      <div>
                        <div 
                          style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering the parent onClick
                            handleStatCardClick(`additionalLocation_${index}_enroute`);
                          }}
                        >
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Enroute</div>
                          <div style={{ width: "60%", padding: "6px", backgroundColor: "#8BC34A", color: "#fff", textAlign: "center", fontSize: "13px" }}>
                            {locationsData[loc].enroute.length}
                          </div>
                        </div>
                        <div 
                          style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering the parent onClick
                            handleStatCardClick(`additionalLocation_${index}_delayed`);
                          }}
                        >
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Delayed</div>
                          <div style={{ width: "60%", padding: "6px", backgroundColor: "#FFEB3B", color: "#000", textAlign: "center", fontSize: "13px" }}>
                            {tasks.filter(t => t.status?.toLowerCase() === 'delayed' && t.report_to_location === loc).length}
                          </div>
                        </div>
                        <div style={{ display: "flex" }}>
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>ETA to Staffed</div>
                          <div style={{ width: "60%", padding: "6px", textAlign: "center", fontSize: "13px" }}>
                            {locationsData[loc].etaToStaffed}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                }
              </div>
            </section>
            )}

            {/* Response Profiles Section - Removed and replaced with Document Summary */}

            {/* Document Summary Section - Shows AI summaries of documents uploaded during event launch */}
            <DocumentSummary
              eventId={eventId}
              token={token}
              eventInfo={eventInfo}
            />

            {/* Report to Locations Section - Only for response events */}
            {console.log("eventInfo.event_type:", eventInfo.event_type)};
            
            {eventInfo.event_type === 'response' && (
              <section
                className="card"
                style={{
                  padding: "20px",
                  marginBottom: "20px",
                  borderRadius: "10px",
                  boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                }}
              >
                <h2 style={{ color: "#1a73e8", marginBottom: "15px" }}>
                  Report to Locations
                </h2>
                <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))", gap: "15px" }}>
                  {eventInfo.report_to_locations && eventInfo.report_to_locations.length > 0 ? (
                    eventInfo.report_to_locations.map((location, index) => (
                      <div
                        key={index}
                        style={{
                          padding: "15px",
                          backgroundColor: "#f8f9fa",
                          borderRadius: "8px",
                          border: "1px solid #e9ecef",
                        }}
                      >
                        <div style={{ fontWeight: "600", color: "#495057", marginBottom: "8px" }}>
                          {location.commonName || "Location"}
                        </div>
                        <div style={{ fontSize: "14px", color: "#6c757d", marginBottom: "4px" }}>
                          {location.address}
                        </div>
                        <div style={{ fontSize: "14px", color: "#6c757d" }}>
                          {location.city}, {location.state} {location.zip}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div style={{
                      padding: "20px",
                      textAlign: "center",
                      color: "#6c757d",
                      fontStyle: "italic"
                    }}>
                      No report-to locations specified for this event.
                    </div>
                  )}
                </div>
              </section>
            )}

            {/* Map Section */}
            <section
              className="card map-section"
              style={{
                padding: "20px",
                marginBottom: "20px",
                borderRadius: "10px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                maxHeight: "560px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between", // Align items to the left and right
                  alignItems: "center", // Vertically center the items
                  marginBottom: "15px",
                }}
              >
                <h2 style={{ color: "#1a73e8", marginBottom: "15px" }}>Map</h2>
                <button
                  className="btn-secondary"
                  style={{ marginBottom: "1rem" }}
                  onClick={() =>
                    setShowMapModal(`/map-popup/${eventId}?token=${token}`)
                  }
                >
                  View Map
                </button>
              </div>
              {mapsLoaded && eventLatLng ? (
                <GoogleMap
                  mapContainerStyle={mapContainerStyle}
                  center={eventLatLng}
                  zoom={10}
                >
                  <Marker
                    position={eventLatLng}
                    title={eventInfo.title || "Event Location"}
                    icon={{
                      url: "http://maps.google.com/mapfiles/ms/icons/red-dot.png",
                    }}
                  />
                  {Object.entries(reportToLocations).map(([loc, coords]) => (
                    <Marker
                      key={loc}
                      position={coords}
                      title={loc}
                      icon={{
                        url: "http://maps.google.com/mapfiles/ms/icons/green-dot.png",
                      }}
                    />
                  ))}
                  {tasks.map((task) => {
                    const responder = responders.find(
                      (r) => r.id === task.assigned_to
                    );
                    return responder &&
                      responder.latitude &&
                      responder.longitude ? (
                      <Marker
                        key={responder.id}
                        position={{
                          lat: responder.latitude,
                          lng: responder.longitude,
                        }}
                        title={
                          responder.username || `Responder ${responder.id}`
                        }
                        icon={{
                          url: "http://maps.google.com/mapfiles/ms/icons/blue-dot.png",
                        }}
                      />
                    ) : null;
                  })}
                </GoogleMap>
              ) : (
                <div>
                  {mapsLoaded
                    ? "No event location data available - check console for geocoding errors"
                    : "Map unavailable—Google Maps API failed to load"}
                </div>
              )}
            </section>

            {/* Event Chat Section - Enhanced Design */}
        <section
          className="card"
          style={{
            padding: "25px",
            marginBottom: "20px",
            background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
            borderRadius: "15px",
            boxShadow: "0 8px 32px rgba(102, 126, 234, 0.3)",
            border: "1px solid rgba(255, 255, 255, 0.1)",
          }}
        >
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: "20px",
            }}
          >
            <h2
              style={{
                color: "#ffffff",
                margin: 0,
                fontSize: "28px",
                fontWeight: "700",
                textShadow: "0 2px 4px rgba(0,0,0,0.3)",
                display: "flex",
                alignItems: "center",
                gap: "10px"
              }}
            >
              💬 Event Chat
            </h2>
            <button
              onClick={handleSummarizeChat}
              style={{
                background: "linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)",
                color: "white",
                border: "none",
                padding: "12px 20px",
                borderRadius: "25px",
                cursor: "pointer",
                fontSize: "14px",
                fontWeight: "600",
                boxShadow: "0 4px 15px rgba(255, 107, 53, 0.4)",
                transition: "all 0.3s ease",
                display: "flex",
                alignItems: "center",
                gap: "8px"
              }}
              disabled={chatMessages.length === 0}
              onMouseEnter={(e) => {
                e.target.style.transform = "translateY(-2px)";
                e.target.style.boxShadow = "0 6px 20px rgba(255, 107, 53, 0.6)";
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = "translateY(0)";
                e.target.style.boxShadow = "0 4px 15px rgba(255, 107, 53, 0.4)";
              }}
            >
              🤖 Summarize with AI
            </button>
          </div>

          <div
            style={{
              height: "320px",
              overflowY: "auto",
              border: "none",
              borderRadius: "12px",
              padding: "15px",
              marginBottom: "15px",
              background: "rgba(255, 255, 255, 0.95)",
              backdropFilter: "blur(10px)",
              boxShadow: "inset 0 2px 10px rgba(0,0,0,0.1)",
            }}
          >
            {chatMessages.length === 0 ? (
              <div style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: "100%",
                color: "#6b7280",
                fontSize: "16px"
              }}>
                <div style={{ fontSize: "48px", marginBottom: "10px" }}>💭</div>
                <p style={{ margin: 0, fontStyle: "italic" }}>No messages yet. Start the conversation!</p>
              </div>
            ) : (
              chatMessages.map((msg, index) => (
                <div key={index} style={{
                  marginBottom: "12px",
                  padding: "12px 15px",
                  background: "linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)",
                  borderRadius: "10px",
                  border: "1px solid rgba(0,0,0,0.05)",
                  boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
                  transition: "transform 0.2s ease"
                }}
                onMouseEnter={(e) => e.target.style.transform = "translateY(-1px)"}
                onMouseLeave={(e) => e.target.style.transform = "translateY(0)"}
                >
                  <div style={{
                    fontSize: "12px",
                    color: "#6366f1",
                    marginBottom: "4px",
                    fontWeight: "600"
                  }}>
                    <span style={{ color: "#1f2937" }}>👤 {msg.username || "Unknown User"}</span>
                    <span style={{ color: "#9ca3af", marginLeft: "8px" }}>
                      🕒 {new Date(msg.timestamp).toLocaleString()}
                    </span>
                  </div>
                  <div style={{
                    fontSize: "14px",
                    color: "#374151",
                    lineHeight: "1.5"
                  }}>{msg.text}</div>
                </div>
              ))
            )}
          </div>

          <div className="chat-ai-container" style={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr",
            gap: "25px",
            background: "rgba(255, 255, 255, 0.1)",
            padding: "20px",
            borderRadius: "15px",
            backdropFilter: "blur(10px)"
          }}>
            {/* Chat Input Section - Left Side */}
            <div className="chat-section chat-input-container" style={{
              display: "flex",
              gap: "12px",
              alignItems: "center"
            }}>
              <input
                className="chat-input"
                type="text"
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                placeholder="💭 Type your message..."
                style={{
                  flex: 1,
                  padding: "12px 16px",
                  border: "2px solid rgba(255, 255, 255, 0.3)",
                  borderRadius: "25px",
                  fontSize: "14px",
                  background: "rgba(255, 255, 255, 0.9)",
                  color: "#374151",
                  outline: "none",
                  transition: "all 0.3s ease",
                  boxShadow: "0 4px 15px rgba(0,0,0,0.1)"
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = "#6366f1";
                  e.target.style.boxShadow = "0 4px 20px rgba(99, 102, 241, 0.3)";
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = "rgba(255, 255, 255, 0.3)";
                  e.target.style.boxShadow = "0 4px 15px rgba(0,0,0,0.1)";
                }}
              />
              <button
                className="chat-send-button"
                onClick={handleSendMessage}
                disabled={!chatInput.trim()}
                style={{
                  background: "linear-gradient(135deg, #10b981 0%, #059669 100%)",
                  color: "white",
                  border: "none",
                  padding: "12px 20px",
                  borderRadius: "25px",
                  cursor: "pointer",
                  fontSize: "14px",
                  fontWeight: "600",
                  boxShadow: "0 4px 15px rgba(16, 185, 129, 0.4)",
                  transition: "all 0.3s ease",
                  display: "flex",
                  alignItems: "center",
                  gap: "6px"
                }}
                onMouseEnter={(e) => {
                  if (!e.target.disabled) {
                    e.target.style.transform = "translateY(-2px)";
                    e.target.style.boxShadow = "0 6px 20px rgba(16, 185, 129, 0.6)";
                  }
                }}
                onMouseLeave={(e) => {
                  e.target.style.transform = "translateY(0)";
                  e.target.style.boxShadow = "0 4px 15px rgba(16, 185, 129, 0.4)";
                }}
              >
                📤 Send
              </button>
            </div>

            {/* AI Summary Section - Right Side */}
            <div className="ai-section">
              {aiChatSummary ? (
                <div
                  style={{
                    padding: "20px",
                    background: "linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)",
                    borderRadius: "15px",
                    border: "2px solid rgba(245, 158, 11, 0.3)",
                    height: "120px",
                    overflowY: "auto",
                    boxShadow: "0 8px 25px rgba(245, 158, 11, 0.2)",
                    backdropFilter: "blur(10px)"
                  }}
                >
                  <h4 style={{
                    color: "#92400e",
                    marginBottom: "12px",
                    fontSize: "16px",
                    fontWeight: "700",
                    display: "flex",
                    alignItems: "center",
                    gap: "8px"
                  }}>
                    🤖 AI Summary
                  </h4>
                  <p style={{
                    color: "#78350f",
                    margin: 0,
                    fontSize: "13px",
                    lineHeight: "1.5",
                    fontWeight: "500"
                  }}>
                    {aiChatSummary}
                  </p>
                </div>
              ) : (
                <div
                  style={{
                    padding: "20px",
                    background: "rgba(255, 255, 255, 0.9)",
                    borderRadius: "15px",
                    border: "2px dashed rgba(255, 255, 255, 0.5)",
                    height: "120px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    color: "rgba(255, 255, 255, 0.8)",
                    fontSize: "14px",
                    fontStyle: "italic",
                    textAlign: "center",
                    backdropFilter: "blur(10px)"
                  }}
                >
                  <div style={{ fontSize: "32px", marginBottom: "8px" }}>🤖</div>
                  <div>Click "Summarize with AI" to generate summary</div>
                </div>
              )}
            </div>
          </div>
        </section>
          </>
        )}
      </ErrorBoundary>

      {/* Responder Activity Modal */}
      {showResponderModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowResponderModal(false)}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="modal-content"
            style={{
              backgroundColor: "#fff",
              borderRadius: "8px",
              padding: "20px",
              width: "90%",
              maxWidth: "800px",
              maxHeight: "80vh",
              overflow: "auto",
              boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
            }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ margin: 0 }}>
                {activeCardType === 'notified' && 'Notified Responders'}
                {activeCardType === 'acknowledged' && 'Acknowledged Responders'}
                {activeCardType === 'enroute' && 'Enroute Responders'}
                {activeCardType === 'delayed' && 'Delayed Responders'}
                {activeCardType === 'unable' && 'Unable/Cancelled Responders'}
                {activeCardType === 'arrived' && 'Arrived Responders'}
                {activeCardType === 'completed' && 'Completed Tasks'}
                {activeCardType === 'primaryLocation' && `Primary Location: ${formatLocation(eventInfo.location)}`}
                {activeCardType === 'primaryLocation_enroute' && `Enroute to Primary Location: ${formatLocation(eventInfo.location)}`}
                {activeCardType === 'primaryLocation_delayed' && `Delayed to Primary Location: ${formatLocation(eventInfo.location)}`}
                {activeCardType.startsWith('location_') && `Location: ${activeCardType.split('_').slice(1).join('_')}`}
                {activeCardType.startsWith('enroute_at_') && `Enroute to ${activeCardType.split('_at_')[1]}`}
                {activeCardType.startsWith('delayed_at_') && `Delayed to ${activeCardType.split('_at_')[1]}`}
              </h2>
              <button
                onClick={() => setShowResponderModal(false)}
                style={{
                  backgroundColor: "#f44336",
                  border: "none",
                  borderRadius: "50%",
                  width: "30px",
                  height: "30px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  cursor: "pointer",
                  color: "white",
                  fontWeight: "bold",
                }}
              >
                ✕
              </button>
            </div>
            
            {responderData.length > 0 ? (
              <div>
                <table className="responder-table" style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ backgroundColor: '#f5f5f5' }}>
                      <th 
                        onClick={() => {
                          setSortDirection(sortField === 'name' && sortDirection === 'asc' ? 'desc' : 'asc');
                          setSortField('name');
                        }}
                        style={{ 
                          padding: '10px', 
                          textAlign: 'left', 
                          borderBottom: '1px solid #ddd',
                          cursor: 'pointer',
                          position: 'relative'
                        }}
                      >
                        Name {sortField === 'name' && (sortDirection === 'asc' ? ' ↑' : ' ↓')}
                      </th>
                      <th style={{ padding: '10px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Status</th>
                      {/* <th 
                        onClick={() => {
                          setSortDirection(sortField === 'timestamp' && sortDirection === 'asc' ? 'desc' : 'asc');
                          setSortField('timestamp');
                        }}
                        style={{ 
                          padding: '10px', 
                          textAlign: 'left', 
                          borderBottom: '1px solid #ddd',
                          cursor: 'pointer'
                        }}
                      >
                        Time {sortField === 'timestamp' && (sortDirection === 'asc' ? ' ↑' : ' ↓')}
                      </th> */}
                      {activeCardType !== 'notified' && (
                        <th 
                          onClick={() => {
                            setSortDirection(sortField === 'eta' && sortDirection === 'asc' ? 'desc' : 'asc');
                            setSortField('eta');
                          }}
                          style={{ 
                            padding: '10px', 
                            textAlign: 'left', 
                            borderBottom: '1px solid #ddd',
                            cursor: 'pointer'
                          }}
                        >
                          ETA {sortField === 'eta' && (sortDirection === 'asc' ? ' ↑' : ' ↓')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {responderData.map((responder, index) => (
                      <tr key={index} className="responder-item" style={{ borderBottom: '1px solid #ddd' }}>
                        <td className="responder-info" style={{ padding: '10px' }}>{responder.name || 'Unknown'}</td>
                        <td className="responder-status" style={{ padding: '10px' }}>{responder.status || 'N/A'}</td>
                        {/* <td style={{ padding: '10px' }}>{responder.timestamp ? new Date(responder.timestamp).toLocaleString() : 'N/A'}</td> */}
                        {activeCardType !== 'notified' && (
                          <td style={{ padding: '10px' }}>{responder.eta || 'N/A'}</td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <p>No data available for this category.</p>
              </div>
            )}
          </div>
        </div>
      )}
      
      {!!showMapModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowMapModal("")}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="iframe-wrapper"
            style={{
              position: "relative",
              width: "100vw",
              height: "100vh",
              overflow: "auto",
              border: "none",
              outline: "none",
              backgroundColor: "#fff",
              borderRadius: "8px",
            }}
          >
            {/* Close Button - shown on md+ screens */}
            <button
              onClick={() => setShowMapModal("")}
              className="close-btn-md"
              style={{
                position: "absolute",
                top: "10px",
                right: "10px",
                backgroundColor: "#00a8b5",
                border: "none",
                borderRadius: "50%",
                width: "40px",
                height: "40px",
                color: "#fff",
                display: "none", // hidden by default, shown via media query
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
                zIndex: 10000,
              }}
            >
              ✕
            </button>

            <iframe
              src={showMapModal}
              title="Map"
              width="100%"
              height="100%"
              style={{
                border: "none",
                outline: "none",
              }}
            />
          </div>
        </div>
      )}

      {!!showMapModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowMapModal("")}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="iframe-wrapper"
            style={{
              position: "relative",
              width: "100vw",
              height: "100vh",
              overflow: "auto",
              border: "none",
              outline: "none",
              backgroundColor: "#fff",
              borderRadius: "8px",
            }}
          >
            {/* Close Button - shown on md+ screens */}
            <button
              onClick={() => setShowMapModal("")}
              className="close-btn-md"
              style={{
                position: "absolute",
                top: "10px",
                right: "10px",
                backgroundColor: "#00a8b5",
                border: "none",
                borderRadius: "50%",
                width: "40px",
                height: "40px",
                color: "#fff",
                display: "none", // hidden by default, shown via media query
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
                zIndex: 10000,
              }}
            >
              ✕
            </button>

            <iframe
              src={showMapModal}
              title="Map"
              width="100%"
              height="100%"
              style={{
                border: "none",
                outline: "none",
              }}
            />
          </div>
        </div>
      )}

      {/*
      {!!showMapModal && (
          <div className="delete-modal-overlay">
            <div className="delete-modal">
              <div style={{display: 'flex', justifyContent: 'space-between'}}>
                <h2>Add New User</h2>
                <button
                  type="button" style={{height: '40px', width: '40px', display: 'flex', justifyContent: "center", alignItems: 'center', backgroundColor: '#00a8b5', border: 'none', borderRadius: '10px', cursor: 'pointer' }}
                  onClick={() => setShowMapModal("")}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 50 50">
                    <path fill="#fff" d="M 9.15625 6.3125 L 6.3125 9.15625 L 22.15625 25 L 6.21875 40.96875 L 9.03125 43.78125 L 25 27.84375 L 40.9375 43.78125 L 43.78125 40.9375 L 27.84375 25 L 43.6875 9.15625 L 40.84375 6.3125 L 25 22.15625 Z"></path>
                  </svg>
                </button>
              </div>

              <iframe src={showMapModal} title="" width={1200} height={800} />
            </div>
          </div>
        )}
      */}
      </div>
    </div>
    </>
  );
}

export default Dashboard;
