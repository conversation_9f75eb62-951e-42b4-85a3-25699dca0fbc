-- Test script to verify AI features are working correctly
-- Run this after the migration to test the implementation

-- 1. Check if migration was successful
SELECT 'Checking migration status...' AS test_step;

-- Check new columns exist
SELECT 
    CASE 
        WHEN COUNT(*) = 2 THEN 'PASS: AI columns exist in company_settings'
        ELSE 'FAIL: Missing AI columns in company_settings'
    END AS column_check
FROM information_schema.columns 
WHERE table_name = 'company_settings' 
AND column_name IN ('ai_prompts', 'ai_preconditions');

-- Check new tables exist
SELECT 
    CASE 
        WHEN COUNT(*) = 3 THEN 'PASS: All AI tables created'
        ELSE 'FAIL: Missing AI tables'
    END AS table_check
FROM information_schema.tables 
WHERE table_name IN ('checklist_templates', 'document_questions', 'document_summaries');

-- 2. Test AI settings for ttornstrom user
SELECT 'Testing AI settings...' AS test_step;

-- Check if company_settings has AI data
SELECT 
    company_id,
    CASE 
        WHEN ai_prompts IS NOT NULL AND ai_prompts != '{}' THEN 'PASS: AI prompts configured'
        ELSE 'FAIL: AI prompts not configured'
    END AS prompts_check,
    CASE 
        WHEN ai_preconditions IS NOT NULL AND ai_preconditions != '{}' THEN 'PASS: AI preconditions configured'
        ELSE 'FAIL: AI preconditions not configured'
    END AS preconditions_check
FROM company_settings 
LIMIT 1;

-- 3. Insert test AI settings (if not already present)
INSERT INTO company_settings (company_id, ai_prompts, ai_preconditions) 
SELECT 1, 
    '{"document_summary": "Test prompt for document summarization"}',
    '{"document_qa_prompt": "Test prompt for Q&A", "action_generation_prompt": "Test prompt for actions"}'
WHERE NOT EXISTS (SELECT 1 FROM company_settings WHERE company_id = 1)
ON CONFLICT (company_id) DO UPDATE SET
    ai_prompts = EXCLUDED.ai_prompts,
    ai_preconditions = EXCLUDED.ai_preconditions;

-- 4. Test checklist templates
SELECT 'Testing checklist templates...' AS test_step;

-- Insert a test template
INSERT INTO checklist_templates (name, description, template_data, created_by)
VALUES (
    'Test Emergency Checklist',
    'Test checklist for emergency response',
    '["Check safety equipment", "Contact emergency services", "Evacuate if necessary", "Report status"]',
    1
) ON CONFLICT DO NOTHING;

-- Verify template was created
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS: Checklist template created'
        ELSE 'FAIL: Checklist template not created'
    END AS template_check
FROM checklist_templates 
WHERE name = 'Test Emergency Checklist';

-- 5. Test document questions table
SELECT 'Testing document questions...' AS test_step;

-- Insert a test question (assuming event_id 1 and user_id 1 exist)
INSERT INTO document_questions (event_id, user_id, question, answer, document_context)
VALUES (
    1, 1, 
    'Test question about emergency procedures?',
    'Test answer about emergency procedures.',
    '{"documents": ["test-document.pdf"]}'
) ON CONFLICT DO NOTHING;

-- Verify question was stored
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS: Document question stored'
        ELSE 'FAIL: Document question not stored'
    END AS question_check
FROM document_questions 
WHERE question LIKE 'Test question%';

-- 6. Test document summaries table
SELECT 'Testing document summaries...' AS test_step;

-- Insert a test summary
INSERT INTO document_summaries (event_id, user_id, document_name, summary, checklist_items)
VALUES (
    1, 1,
    'test-document.pdf',
    'Test summary of emergency procedures document',
    '["Review evacuation routes", "Check emergency contacts", "Verify safety equipment"]'
) ON CONFLICT (event_id, user_id, document_name) DO UPDATE SET
    summary = EXCLUDED.summary,
    checklist_items = EXCLUDED.checklist_items;

-- Verify summary was stored
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS: Document summary stored'
        ELSE 'FAIL: Document summary not stored'
    END AS summary_check
FROM document_summaries 
WHERE document_name = 'test-document.pdf';

-- 7. Final verification
SELECT 'Final verification...' AS test_step;

-- Show current AI settings
SELECT 
    'Current AI Settings:' AS info,
    ai_prompts,
    ai_preconditions
FROM company_settings 
WHERE company_id = 1;

-- Show test data counts
SELECT 
    'Test Data Summary:' AS info,
    (SELECT COUNT(*) FROM checklist_templates) AS template_count,
    (SELECT COUNT(*) FROM document_questions) AS question_count,
    (SELECT COUNT(*) FROM document_summaries) AS summary_count;

-- 8. Cleanup test data (optional - uncomment to clean up)
-- DELETE FROM document_questions WHERE question LIKE 'Test question%';
-- DELETE FROM document_summaries WHERE document_name = 'test-document.pdf';
-- DELETE FROM checklist_templates WHERE name = 'Test Emergency Checklist';

SELECT 'AI Features Test Complete!' AS final_status;
