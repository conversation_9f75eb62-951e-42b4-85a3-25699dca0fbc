import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { jwtDecode } from 'jwt-decode';
import './styles.css';
import Footer from './Footer';

const baseUrl = process.env.REACT_APP_BASE_URL;

const OTP = ({ setToken, token }) => {
    
    const [verificationCode, setVerificationCode] = useState('');
    const [formData, setFormData] = useState({
        username: '',
        otp: '',
      });
    const [showVerification, setShowVerification] = useState(false);
    
    const navigate = useNavigate();

    const handleVerificationSubmit = async (e) => {
        e.preventDefault();
    
        // Minimal check to prevent empty OTP submission
        if (!verificationCode.trim()) {
          toast.error('Please enter a verification code', { position: 'top-right', autoClose: 3000 });
          return;
        }
    
        // // Verify username is not empty
        // if (!formData.username) {
        //   console.error('Username is empty during verification');
        //   toast.error('Username is missing. Please sign up again.', { position: 'top-right', autoClose: 3000 });
        //   setShowVerification(false);
        //   return;
        // }
    
        const verificationData = {
          username: formData.username,
          otp: verificationCode,
        };
    
        try {
          const response = await fetch(`${baseUrl}/verify-otp`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(verificationData),
          });
          const data = await response.json();
          if (!response.ok) throw new Error(data.error || 'Verification failed');
    
          localStorage.setItem('token', data.token);
          setToken(data.token);
          toast.success('Verification successful!', { position: 'top-right', autoClose: 3000 });
    
          setShowVerification(false);
          setVerificationCode('');
          navigate('/dashboard');
        } catch (err) {
          console.error('Verification error:', err);
          toast.error(err.message, { position: 'top-right', autoClose: 3000 });
        }
    };

    return (
        <>
            <div className="form-container">
                <h2>Verify Phone Number</h2>
                <p>Enter the code sent to your phone.</p>
                <form onSubmit={handleVerificationSubmit}>
                <div>
                    <label>Verification Code</label>
                    <input
                    type="text"
                    placeholder="Enter code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="input-field"
                    />
                </div>
                <button type="submit" className="button">Verify Code</button>
                </form>
            </div>
        </>
    )

}

export default OTP;