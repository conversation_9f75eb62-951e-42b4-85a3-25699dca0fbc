-- Migration script for AI Features
-- This script safely adds new columns and tables without affecting existing data
-- Run this instead of the full setup-db.sql

-- Start transaction to ensure atomicity
BEGIN;

-- Add new columns to company_settings table (if they don't exist)
DO $$ 
BEGIN
    -- Add ai_prompts column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'company_settings' AND column_name = 'ai_prompts'
    ) THEN
        ALTER TABLE company_settings ADD COLUMN ai_prompts JSONB DEFAULT '{}';
        RAISE NOTICE 'Added ai_prompts column to company_settings';
    ELSE
        RAISE NOTICE 'ai_prompts column already exists in company_settings';
    END IF;

    -- Add ai_preconditions column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'company_settings' AND column_name = 'ai_preconditions'
    ) THEN
        ALTER TABLE company_settings ADD COLUMN ai_preconditions JSONB DEFAULT '{}';
        RAISE NOTICE 'Added ai_preconditions column to company_settings';
    ELSE
        RAISE NOTICE 'ai_preconditions column already exists in company_settings';
    END IF;

    -- Add updated_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'company_settings' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE company_settings ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added updated_at column to company_settings';
    ELSE
        RAISE NOTICE 'updated_at column already exists in company_settings';
    END IF;
END $$;

-- Create checklist_templates table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS checklist_templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_data JSONB NOT NULL,
    created_by INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create document_questions table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS document_questions (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    answer TEXT,
    document_context JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create document_summaries table (if it doesn't exist)
CREATE TABLE IF NOT EXISTS document_summaries (
    id SERIAL PRIMARY KEY,
    event_id INTEGER REFERENCES events(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    document_name VARCHAR(255) NOT NULL,
    summary TEXT NOT NULL,
    checklist_items JSONB DEFAULT '[]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(event_id, user_id, document_name)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_document_questions_event_user ON document_questions(event_id, user_id);
CREATE INDEX IF NOT EXISTS idx_document_summaries_event_user ON document_summaries(event_id, user_id);
CREATE INDEX IF NOT EXISTS idx_checklist_templates_active ON checklist_templates(is_active);

-- Insert default AI prompts for existing companies (if not already set)
UPDATE company_settings 
SET ai_prompts = COALESCE(ai_prompts, '{}') || '{
    "document_summary": "You are an AI assistant helping emergency responders analyze documents. Provide a concise summary of the document focusing on key information relevant to emergency response, including procedures, contacts, resources, and safety guidelines. Format the summary with bullet points for easy reading."
}'::jsonb,
ai_preconditions = COALESCE(ai_preconditions, '{}') || '{
    "document_qa_prompt": "You are an AI assistant helping emergency responders analyze documents. Provide accurate, concise answers based on the uploaded documents.",
    "action_generation_prompt": "You are an AI assistant helping emergency responders. Generate specific, actionable tasks based on the user role and event type."
}'::jsonb
WHERE ai_prompts IS NULL OR ai_prompts = '{}' OR ai_preconditions IS NULL OR ai_preconditions = '{}';

-- Verify the migration
DO $$
DECLARE
    table_count INTEGER;
    column_count INTEGER;
BEGIN
    -- Check new tables
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables 
    WHERE table_name IN ('checklist_templates', 'document_questions', 'document_summaries');
    
    -- Check new columns
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = 'company_settings' 
    AND column_name IN ('ai_prompts', 'ai_preconditions');
    
    RAISE NOTICE 'Migration verification:';
    RAISE NOTICE '- New tables created: %', table_count;
    RAISE NOTICE '- New columns added: %', column_count;
    
    IF table_count = 3 AND column_count = 2 THEN
        RAISE NOTICE 'Migration completed successfully!';
    ELSE
        RAISE WARNING 'Migration may be incomplete. Please check manually.';
    END IF;
END $$;

-- Commit the transaction
COMMIT;

-- Display final status
SELECT 'AI Features Migration Completed Successfully' AS status;




