<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - AlertComm1</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .signup-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 450px;
        text-align: center;
      }

      .logo {
        margin-bottom: 30px;
      }

      .logo-icon {
        font-size: 48px;
        margin-bottom: 10px;
      }

      .logo-text {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
      }

      .subtitle {
        color: #666;
        font-size: 14px;
        margin-bottom: 30px;
      }

      .restriction-notice {
        background: linear-gradient(135deg, #ff9a56 0%, #ff6b6b 100%);
        color: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
      }

      .restriction-notice h3 {
        font-size: 20px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .restriction-notice p {
        font-size: 16px;
        line-height: 1.5;
        margin-bottom: 20px;
      }

      .visit-btn {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 2px solid rgba(255, 255, 255, 0.3);
        padding: 12px 24px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        display: inline-block;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .visit-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .form-section {
        opacity: 0.6;
        pointer-events: none;
        position: relative;
      }

      .form-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 15px;
        z-index: 1;
      }

      .form-group {
        margin-bottom: 20px;
        text-align: left;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
      }

      .form-group input,
      .form-group select {
        width: 100%;
        padding: 15px;
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        font-size: 16px;
        background: #fff;
      }

      .signup-btn {
        width: 100%;
        padding: 15px;
        background: #ccc;
        color: white;
        border: none;
        border-radius: 10px;
        font-size: 16px;
        font-weight: 600;
        cursor: not-allowed;
        margin-bottom: 20px;
      }

      .links {
        margin-top: 20px;
        padding-top: 20px;
        border-top: 1px solid #e1e5e9;
      }

      .links a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        margin: 0 10px;
      }

      .links a:hover {
        text-decoration: underline;
      }

      @media (max-width: 480px) {
        .signup-container {
          padding: 30px 20px;
        }

        .logo-text {
          font-size: 24px;
        }

        .restriction-notice {
          padding: 20px;
        }

        .restriction-notice h3 {
          font-size: 18px;
        }

        .restriction-notice p {
          font-size: 14px;
        }
      }
    </style>
  </head>
  <body>
    <div class="signup-container">
      <div class="logo">
        <div class="logo-icon">🚨</div>
        <h1 class="logo-text">AlertComm1</h1>
        <p class="subtitle">Emergency Response System</p>
      </div>

      <div class="restriction-notice">
        <h3>
          <span>🚧</span>
          Registration Currently Unavailable
        </h3>
        <p>
          We're working hard to bring you the best emergency response platform.
          Account registration is temporarily disabled while we finalize our security and onboarding processes.
        </p>
        <a href="https://alertcomm1.com/" class="visit-btn" target="_blank">
          Visit AlertComm1.com for Updates
        </a>
      </div>

      <div class="form-section">
        <form id="signupForm">
          <div class="form-group">
            <label for="email">Email Address</label>
            <input type="email" id="email" name="email" required disabled>
          </div>

          <div class="form-group">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" required disabled>
          </div>

          <div class="form-group">
            <label for="password">Password</label>
            <input type="password" id="password" name="password" required disabled>
          </div>

          <div class="form-group">
            <label for="role">Role</label>
            <select id="role" name="role" required disabled>
              <option value="" disabled selected>Select Role</option>
              <option value="staff">Staff</option>
              <option value="lead">Lead</option>
              <option value="commander">Commander</option>
              <option value="viewer">Viewer</option>
            </select>
          </div>

          <button type="submit" class="signup-btn" disabled>
            Create Account (Coming Soon)
          </button>
        </form>
      </div>

      <div class="links">
        <a href="/login.html">Already have an account? Sign In</a>
        <a href="/">Back to Home</a>
      </div>
    </div>

    <script>
      // Show alert when user tries to interact with the form
      document.getElementById('signupForm').addEventListener('submit', function(e) {
        e.preventDefault();
        showRestrictionAlert();
      });

      // Add click listeners to form elements
      const formElements = document.querySelectorAll('#signupForm input, #signupForm select, #signupForm button');
      formElements.forEach(element => {
        element.addEventListener('click', showRestrictionAlert);
        element.addEventListener('focus', showRestrictionAlert);
      });

      function showRestrictionAlert() {
        alert('🚧 Registration is currently unavailable.\n\nPlease visit https://alertcomm1.com/ for updates on when account creation will be available.\n\nIf you already have an account, please use the Sign In page.');
      }

      // Redirect to alertcomm1.com after showing alert
      function redirectToMainSite() {
        setTimeout(() => {
          if (confirm('Would you like to visit AlertComm1.com now for more information?')) {
            window.open('https://alertcomm1.com/', '_blank');
          }
        }, 2000);
      }

      // Show the restriction alert on page load after a short delay
      setTimeout(() => {
        const hasSeenAlert = sessionStorage.getItem('signupAlertShown');
        if (!hasSeenAlert) {
          showRestrictionAlert();
          sessionStorage.setItem('signupAlertShown', 'true');
          redirectToMainSite();
        }
      }, 1000);
    </script>
  </body>
</html>
