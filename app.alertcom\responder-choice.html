<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Responder Choice</title>
    <link rel="stylesheet" href="/frontend/src/styles.css" />
    <style>
      body {
        margin: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      .choice-container {
        text-align: center;
        padding-top: 2rem;
        max-width: 400px;
        margin: 0 auto;
        flex: 1;
      }
      .card {
        background-color: #fff;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #1a73e8;
        margin-bottom: 15px;
      }
      p {
        color: #333;
        margin: 5px 0;
      }
      .highlight {
        background-color: #e6f0fa;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
        font-weight: bold;
      }
      .choice-buttons {
        display: flex;
        justify-content: center;
        gap: 1rem;
        flex-wrap: wrap;
        margin-top: 1rem;
      }
      .choice-buttons button {
        padding: 12px 20px;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        cursor: pointer;
        transition: background-color 0.2s;
      }
      .choice-buttons .btn-primary {
        background-color: #1a73e8;
        color: #fff;
      }
      .choice-buttons .btn-primary:hover {
        background-color: #145ab5;
      }
      .choice-buttons .btn-secondary {
        background-color: #388e3c;
        color: #fff;
      }
      .choice-buttons .btn-secondary:hover {
        background-color: #2c6b30;
      }
      .choice-buttons .btn-danger {
        background-color: #d32f2f;
        color: #fff;
      }
      .choice-buttons .btn-danger:hover {
        background-color: #a62626;
      }
      #report-to{
        margin-top: 15px;
      }
      @media (max-width: 768px) {
        .choice-container {
          padding: 1rem;
          max-width: 95%;
        }
        .card {
          padding: 16px;
          margin: 10px;
        }
        .choice-buttons {
          flex-direction: column;
          gap: 12px;
        }
        .choice-buttons button {
          width: 100%;
          padding: 16px 20px;
          font-size: 16px;
        }
        h1 {
          font-size: 1.5rem;
        }
        p {
          font-size: 14px;
        }
        .highlight {
          padding: 12px;
          font-size: 14px;
        }
      }

      @media (max-width: 480px) {
        .choice-container {
          padding: 0.5rem;
          padding-top: 1rem;
        }
        .card {
          padding: 12px;
          border-radius: 8px;
        }
        .choice-buttons button {
          padding: 14px 16px;
          font-size: 15px;
        }
        h1 {
          font-size: 1.3rem;
        }
        .highlight {
          padding: 10px;
          font-size: 13px;
        }
      }
      footer {
        background-color: #1a73e8;
        color: #fff;
        padding: 20px;
        text-align: center;
        margin-top: auto;
      }
      footer .footer-links {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 10px;
      }
      footer a {
        color: #fff;
        text-decoration: none;
        font-size: 16px;
        transition: color 0.2s;
      }
      footer a:hover {
        color: #e6f0fa;
      }
      footer .copyright {
        font-size: 14px;
        margin-top: 10px;
      }
      footer .logo {
        margin: 10px 0;
      }
      footer .logo img {
        height: 40px;
      }
      @media (max-width: 600px) {
        footer .footer-links {
          flex-direction: column;
          gap: 10px;
        }
      }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.5.1/socket.io.js"></script>
    <!-- Define all functions before loading jwt-decode -->
    <script>
      let jwtDecodeLoaded = false;
      let userId = null;
      let role = null;
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get("token");
      const eventId = urlParams.get("eventId");
      const socket = io(window.location.origin);

      function initializeUserId() {
        try {
          if (typeof jwtDecode === "function") {
            const decoded = jwtDecode(token);
            userId = decoded.id;
            role = decoded.role;
          } else {
            // Fallback: manually decode the token
            const payload = JSON.parse(atob(token.split(".")[1]));
            userId = payload.id;
            role = payload.role;
          }
          console.log(
            "User ID from token:",
            userId,
            "Event ID:",
            eventId,
            "Role:",
            role
          );
        } catch (e) {
          console.error("Error decoding token:", e);
          alert("Invalid token. Please log in again.");
        }
      }

      function setupSocket() {
        if (!userId) {
          console.error("User ID not initialized");
          return;
        }
        socket.on("connect", () => {
          console.log("Connected to server");
          socket.emit("join", `event-${eventId}`);
          socket.emit("join", `user-${userId}`);
        });
      }

      function fetchEventData() {
        if (!userId) {
          console.error("User ID not initialized");
          return;
        }
        fetch(`${window.location.origin}/active-events/${eventId}`, {
          headers: { Authorization: `Bearer ${token}` },
        })
          .then((res) => {
            console.log("Event fetch status:", res.status);
            if (!res.ok) {
              return res.json().then((err) => {
                throw new Error(
                  `HTTP error: ${res.status} - ${err.error || "Unknown error"}`
                );
              });
            }
            return res.json();
          })
          .then((data) => {
            console.log("Event data:", data);
            const eventType = data.event_type || "response";

            // Display event info with appropriate instructions based on event type
            if (eventType === "notification_only") {
              document.getElementById("event-info").innerHTML = `
                <h2>${data.title}</h2>
                <div class="highlight" style="background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404;">
                  <strong>📢 NOTIFICATION ONLY EVENT</strong><br>
                  No response required. This is for information and coordination only.
                </div>
                <p>${data.info}</p>
                <p><strong>📍 Event Location:</strong> ${formatLocation(data.location)}</p>
                <p><strong>📅 Event Date:</strong> ${new Date(data.created_at).toLocaleDateString()}</p>
                <p><strong>🕐 Event Time:</strong> ${new Date(data.created_at).toLocaleTimeString()}</p>
                <div style="margin-top: 15px; padding: 10px; background-color: #e8f4fd; border-radius: 5px; font-size: 14px;">
                  <strong>Instructions:</strong><br>
                  • <strong>Participate:</strong> Join the event chat and coordination<br>
                  • <strong>Unable to Participate:</strong> Stay informed but not actively involved<br>
                  • <strong>Remove:</strong> Stop receiving updates for this event
                </div>
              `;
              document.getElementById("report-to").innerHTML = "";
            } else {
              document.getElementById("event-info").innerHTML = `
                <h2>${data.title}</h2>
                <div class="highlight" style="background-color: #ffebee; border: 1px solid #ffcdd2; color: #c62828;">
                  <strong>🚨 RESPONSE EVENT</strong><br>
                  Active response required. Please respond immediately.
                </div>
                <p>${data.info}</p>
                <p><strong>📍 Event Location:</strong> ${formatLocation(data.location)}</p>
                <p><strong>📅 Event Date:</strong> ${new Date(data.created_at).toLocaleDateString()}</p>
                <p><strong>🕐 Event Time:</strong> ${new Date(data.created_at).toLocaleTimeString()}</p>
                <div style="margin-top: 15px; padding: 10px; background-color: #e8f4fd; border-radius: 5px; font-size: 14px;">
                  <strong>Instructions:</strong><br>
                  • <strong>Acknowledge and Participate:</strong> Confirm you will respond to the location<br>
                  • <strong>Unable to Participate:</strong> Stay updated but cannot respond<br>
                  • <strong>Remove:</strong> Stop receiving updates for this event
                </div>
              `;

              // Show "Report to" field for response events
              const task = data.tasks.find((t) => t.assigned_to === userId);
              document.getElementById(
                "report-to"
              ).innerHTML = `<strong class="highlight">🎯 Report to:</strong> ${
                task?.report_to_location || "Unknown"
              }`;
            }
          })
          .catch((err) => console.error("Error fetching event:", err));
      }

      function onJwtDecodeLoaded() {
        jwtDecodeLoaded = true;
        console.log("jwt-decode loaded successfully");
        initializeUserId();
        setupSocket();
        fetchEventData();
        renderFooterLinks();
      }

      function onJwtDecodeError() {
        console.error("Failed to load jwt-decode library, using fallback");
        // Fallback: manually decode the token
        initializeUserId();
        setupSocket();
        fetchEventData();
        renderFooterLinks();
      }

      function formatLocation(location) {
        if (!location || typeof location !== "object") {
          return "Unknown";
        }
        const { commonName, address, city, state, zip } = location;
        return commonName
          ? `${commonName}, ${address || ""}, ${city || ""}, ${state || ""} ${
              zip || ""
            }`
              .trim()
              .replace(/,\s*,/g, ",")
              .replace(/,\s*$/, "")
          : `${address || ""}, ${city || ""}, ${state || ""} ${zip || ""}`
              .trim()
              .replace(/,\s*,/g, ",")
              .replace(/,\s*$/, "") || "Unknown";
      }

      function renderFooterLinks() {
        const footerLinks = document.getElementById("footer-links");
        let linksHTML = `
                <a href="/">Home</a>
            `;
        if (role === "commander" || role === "lead" || role === "staff") {
          linksHTML += `
                    <a href="/events">Events</a>
                    <a href="/dashboard">Dashboard</a>
                `;
        }
        if (role === "commander") {
          linksHTML += `
                    <a href="/launch-event">Launch Event</a>
                    <a href="/templates">Templates</a>
                    <a href="/user-management">User Management</a>
                    <a href="/reporting">Reporting</a>
                `;
        }
        footerLinks.innerHTML = linksHTML;
      }
    </script>
    <script
      src="https://cdn.jsdelivr.net/npm/jwt-decode@4.0.0/build/jwt-decode.min.js"
      onload="onJwtDecodeLoaded()"
      onerror="onJwtDecodeError()"
    ></script>
  </head>
  <body>
    <div class="container choice-container">
      <div class="card">
        <h1>Emergency Response</h1>
        <div id="event-info"></div>
        <div id="report-to"></div>
        <div class="choice-buttons">
          <button class="btn-primary" onclick="respond('yes')">
            Acknowledge and Participate
          </button>
          <button class="btn-secondary" onclick="respond('no-update')">
            Acknowledge, but unable to participate
          </button>
          <button class="btn-danger" onclick="respond('no-end')">
            Remove me from event
          </button>
        </div>
      </div>
    </div>

    <footer>
      <div class="footer-links" id="footer-links"></div>
      <div class="logo">
        <img src="/frontend/public/emsmg.png" alt="EMS Management Group Logo" />
      </div>
      <div class="copyright">
        © 2025 EMS Management Group, LLC. All rights reserved.
      </div>
    </footer>

    <script>
      function respond(choice) {
        if (!userId) {
          console.error("User ID not initialized");
          alert("User ID not available. Please refresh the page.");
          return;
        }

        if (
          choice === "no-end" &&
          !confirm("Are you sure? This will end updates for this event.")
        ) {
          return;
        }

        // Pre-open a new tab immediately on user interaction
        let responderWindow = null;
        if (choice === "yes") {
          responderWindow = window.open("", "_blank"); // Open blank tab first
        }

        const taskData = {
          event_id: eventId,
          assigned_to: userId,
          status: choice === "yes" ? "acknowledged" : "unable",
        };

        console.log("Sending task response:", taskData);

        fetch(`${window.location.origin}/tasks`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(taskData),
        })
          .then((res) => {
            console.log("Response status:", res.status);
            if (!res.ok) {
              return res.json().then((err) => {
                throw new Error(
                  `HTTP error: ${res.status} - ${err.error || "Unknown error"}`
                );
              });
            }
            return res.json();
          })
          .then((task) => {
            console.log("Task response:", task);
            if (choice === "yes" && responderWindow) {
              responderWindow.location.href = `responder.html?token=${token}&eventId=${eventId}`;
            } else if (choice === "no-update") {
              alert("You will be kept updated via chat.");
            } else {
              socket.emit("leave", `event-${eventId}`);
              document.body.innerHTML =
                '<div class="container"><h1>You’ve opted out—contact dispatch if needed.</h1></div>';
            }
          })
          .catch((err) => {
            console.error("Error responding:", err);
            if (responderWindow) responderWindow.close(); // Close tab if fetch fails
          });
      }

    </script>
  </body>
</html>
