import React, { useState, useEffect, useCallback } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { jwtDecode } from "jwt-decode";
import { toast } from "react-toastify";

import Footer from "./Footer";
import "./styles.css";

function Events({ token, onLogout }) {
  const [activeEvents, setActiveEvents] = useState([]);
  const [previousEvents, setPreviousEvents] = useState([]);
  const [selectedEvents, setSelectedEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [fetchError, setFetchError] = useState(null);
  const [role, setRole] = useState("");
  const location = useLocation();
  const navigate = useNavigate();
  const baseUrl = process.env.REACT_APP_BASE_URL;

  // Pagination states
  const [activePage, setActivePage] = useState(1);
  const [previousPage, setPreviousPage] = useState(1);
  const eventsPerPage = 10;

  // Initialize state from localStorage
  const [activeExpanded, setActiveExpanded] = useState(() => {
    const saved = localStorage.getItem("events-activeExpanded");
    return saved !== null ? JSON.parse(saved) : true;
  });
  const [previousExpanded, setPreviousExpanded] = useState(() => {
    const saved = localStorage.getItem("events-previousExpanded");
    return saved !== null ? JSON.parse(saved) : false;
  });

  const fetchEvents = useCallback(async () => {
    if (!token) {
      console.log("No token provided, skipping fetch");
      setFetchError("Please log in to view events");
      setLoading(false);
      return;
    }

    try {
      const decoded = jwtDecode(token);
      setRole(decoded.role || "unknown");
      const response = await fetch(`${baseUrl}/all-events`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      console.log("Response status:", response.status);
      if (!response.ok) {
        // Handle 403 Forbidden (Invalid token) by logging out
        if (response.status === 403) {
          console.error("Invalid token detected, logging out user");
          if (onLogout) {
            onLogout();
          }
          return;
        }
        const errorText = await response.text();
        throw new Error(`Failed to fetch events: ${response.status} - ${errorText}`);
      }
      const data = await response.json();
      setActiveEvents(data.filter((event) => event.status !== "resolved"));
      setPreviousEvents(data.filter((event) => event.status === "resolved"));
    } catch (err) {
      console.error("Error fetching events:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  }, [token, onLogout]);

  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  // Sync state to localStorage on change
  useEffect(() => {
    localStorage.setItem("events-activeExpanded", JSON.stringify(activeExpanded));
    localStorage.setItem("events-previousExpanded", JSON.stringify(previousExpanded));
  }, [activeExpanded, previousExpanded]);

  const handleSelectEvent = (eventId) => {
    setSelectedEvents((prev) =>
      prev.includes(eventId) ? prev.filter((id) => id !== eventId) : [...prev, eventId]
    );
  };

  const handleDeleteSelected = async () => {
    if (selectedEvents.length === 0) {
      alert("No events selected for deletion.");
      return;
    }
    if (!window.confirm(`Are you sure you want to delete ${selectedEvents.length} event(s)?`)) return;

    try {
      setLoading(true);
      await Promise.all(
        selectedEvents.map((eventId) =>
          fetch(`${baseUrl}/events/${eventId}`, {
            method: "DELETE",
            headers: { Authorization: `Bearer ${token}` },
          }).then((res) => {
            if (!res.ok) throw new Error(`Failed to delete event ${eventId}`);
            toast.success( "Event deleted successfully!", {
              position: "top-right",
              autoClose: 3000,
            });
            return res.json();
          })
        )
      );
      await fetchEvents();
      setSelectedEvents([]);
      console.log("Deleted events:", selectedEvents);
    } catch (err) {
      console.error("Error deleting selected events:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteEvent = async (eventId) => {
    if (!window.confirm("Are you sure you want to delete this event?")) return;
    console.log("Deleting event with ID:", eventId);
    try {
      setLoading(true);
      const response = await fetch(`${baseUrl}/events/${eventId}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to delete event: ${response.status} - ${errorText}`);
      }
      toast.success("Event deleted successfully!", {
        position: "top-right",
        autoClose: 3000,
      });
      // await fetchEvents();
    } catch (err) {
      console.error("Error deleting event:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCloseEvent = async (eventId) => {
    if (!window.confirm("Are you sure you want to close this event?")) return;
    console.log("Closing event with ID:", eventId);
    try {
      setLoading(true);
      const response = await fetch(`${baseUrl}/event/${eventId}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ status: "resolved" }),
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to close event: ${response.status} - ${errorText}`);
      }
      await fetchEvents();
    } catch (err) {
      console.error("Error closing event:", err.message);
      setFetchError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Pagination logic
  const getPaginatedEvents = (events, page) => {
    const startIndex = (page - 1) * eventsPerPage;
    const endIndex = startIndex + eventsPerPage;
    return events.slice(startIndex, endIndex);
  };

  const getPageNumbers = (totalEvents) => {
    const pageCount = Math.ceil(totalEvents / eventsPerPage);
    return Array.from({ length: pageCount }, (_, i) => i + 1);
  };

  // Accordion component styles
  const accordionStyle = {
    marginBottom: "20px",
    borderRadius: "8px",
    overflow: "hidden",
    backgroundColor: "#fff",
  };
  const headerStyle = {
    fontSize: "1.2em",
    color: "#1a73e8",
    cursor: "pointer",
    padding: "10px",
    backgroundColor: "#f5f5f5",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  };
  const contentStyle = (isOpen) => ({
    maxHeight: isOpen ? "1000px" : "0",
    overflow: "hidden",
    transition: "max-height 0.3s ease",
  });

  return (
    <div style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}>
      <div style={{ flex: "1" }}>
        <div className="container" style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}>
          <h1 style={{ color: "#1a73e8", marginBottom: "20px" }}>Events</h1>
          {loading ? (
            <p>Loading events...</p>
          ) : fetchError ? (
            <p style={{ color: "var(--danger)" }}>Error: {fetchError}</p>
          ) : (
            <>
              <button
                onClick={handleDeleteSelected}
                className="btn-danger"
                style={{
                  padding: "10px 20px",
                  marginBottom: "20px",
                  background:
                    selectedEvents.length > 0 && role !== "staff" ? "#d32f2f" : "#ccc",
                  cursor:
                    selectedEvents.length > 0 && role !== "staff" ? "pointer" : "not-allowed",
                  boxShadow:
                    selectedEvents.length > 0 && role === "staff"
                      ? "0 0 5px red"
                      : "none",
                  opacity: role === "staff" ? 0.6 : 1,
                }}
                disabled={selectedEvents.length === 0 || role === "staff"}
              >
                Delete Selected ({selectedEvents.length})
              </button>

              {/* Active Events Accordion */}
              <div style={accordionStyle}>
                <div
                  style={headerStyle}
                  onClick={() => setActiveExpanded(!activeExpanded)}
                >
                  <span>Active Events ({activeEvents.length})</span>
                  <span>{activeExpanded ? "▼" : "▶"}</span>
                </div>
                <div style={contentStyle(activeExpanded)}>
                  {activeEvents.length > 0 ? (
                    <>
                      <table
                        style={{
                          width: "100%",
                          borderCollapse: "collapse",
                          backgroundColor: "#fff",
                          borderRadius: "8px",
                          overflow: "hidden",
                        }}
                      >
                        <thead>
                          <tr style={{ backgroundColor: "#1a73e8", color: "#fff" }}>
                            <th style={{ padding: "12px", textAlign: "left" }}>Select</th>
                            <th style={{ padding: "12px", textAlign: "left" }}>Title</th>
                            <th style={{ padding: "12px", textAlign: "left" }}>Status</th>
                            <th style={{ padding: "12px", textAlign: "left" }}>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {getPaginatedEvents(activeEvents, activePage).map((event) => (
                            <tr key={event.id} style={{ borderBottom: "1px solid #eee" }}>
                              <td style={{ padding: "12px" }}>
                                <input
                                  type="checkbox"
                                  checked={selectedEvents.includes(event.id)}
                                  onChange={() => handleSelectEvent(event.id)}
                                />
                              </td>
                              <td style={{ padding: "12px" }}>
                                <Link style={{ color: 'black' }} to={`/dashboard/${event.id}`}>
                                  {event.title}
                                </Link>
                              </td>
                              <td style={{ padding: "12px" }}>{event.status}</td>
                              <td style={{ padding: "12px", display: 'flex' }}>
                                <button
                                  className="btn-danger"
                                  onClick={() => handleDeleteEvent(event.id)}
                                  style={{
                                    marginRight: "10px",
                                    cursor: role === "staff" ? "not-allowed" : "pointer",
                                    opacity: role === "staff" ? 0.6 : 1,
                                    boxShadow: role === "staff" ? "0 0 5px red" : "none",
                                  }}
                                  disabled={role === "staff"}
                                >
                                  Delete
                                </button>
                                <button
                                  className="btn-secondary"
                                  onClick={() => handleCloseEvent(event.id)}
                                  style={{
                                    cursor: role === "staff" ? "not-allowed" : "pointer",
                                    opacity: role === "staff" ? 0.6 : 1,
                                    boxShadow: role === "staff" ? "0 0 5px gray" : "none",
                                  }}
                                  disabled={role === "staff"}
                                >
                                  Close
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {/* Pagination Controls for Active Events */}
                      <div style={{ marginTop: "20px", display: "flex", justifyContent: "center", gap: "10px" , marginBottom: '15px' }}>
                        <button
                          onClick={() => setActivePage((prev) => Math.max(prev - 1, 1))}
                          disabled={activePage === 1}
                          style={{
                            padding: "8px 16px",
                            cursor: activePage === 1 ? "not-allowed" : "pointer",
                            backgroundColor: activePage === 1 ? "#ccc" : "#1a73e8",
                            color: "#fff",
                            border: "none",
                            borderRadius: "4px",
                          }}
                        >
                          Previous
                        </button>
                        {getPageNumbers(activeEvents.length).map((page) => (
                          <button
                            key={page}
                            onClick={() => setActivePage(page)}
                            style={{
                              padding: "8px 16px",
                              cursor: "pointer",
                              backgroundColor: activePage === page ? "#1a73e8" : "#f5f5f5",
                              color: activePage === page ? "#fff" : "#000",
                              border: "1px solid #ccc",
                              borderRadius: "4px",
                            }}
                          >
                            {page}
                          </button>
                        ))}
                        <button
                          onClick={() => setActivePage((prev) => Math.min(prev + 1, Math.ceil(activeEvents.length / eventsPerPage)))}
                          disabled={activePage === Math.ceil(activeEvents.length / eventsPerPage)}
                          style={{
                            padding: "8px 16px",
                            cursor: activePage === Math.ceil(activeEvents.length / eventsPerPage) ? "not-allowed" : "pointer",
                            backgroundColor: activePage === Math.ceil(activeEvents.length / eventsPerPage) ? "#ccc" : "#1a73e8",
                            color: "#fff",
                            border: "none",
                            borderRadius: "4px",
                          }}
                        >
                          Next
                        </button>
                      </div>
                    </>
                  ) : (
                    <p style={{ padding: "10px" }}>No active events.</p>
                  )}
                </div>
              </div>

              {/* Previous Events Accordion */}
              <div style={accordionStyle}>
                <div
                  style={headerStyle}
                  onClick={() => setPreviousExpanded(!previousExpanded)}
                >
                  <span>Previous Events ({previousEvents.length})</span>
                  <span>{previousExpanded ? "▼" : "▶"}</span>
                </div>
                <div style={contentStyle(previousExpanded)}>
                  {previousEvents.length > 0 ? (
                    <>
                      <table
                        style={{
                          width: "100%",
                          borderCollapse: "collapse",
                          backgroundColor: "#fff",
                          borderRadius: "8px",
                          overflow: "hidden",
                        }}
                      >
                        <thead>
                          <tr style={{ backgroundColor: "#1a73e8", color: "#fff" }}>
                            <th style={{ padding: "12px", textAlign: "left" }}>Select</th>
                            <th style={{ padding: "12px", textAlign: "left" }}>Title</th>
                            <th style={{ padding: "12px", textAlign: "left" }}>Status</th>
                            <th style={{ padding: "12px", textAlign: "left" }}>Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          {getPaginatedEvents(previousEvents, previousPage).map((event) => (
                            <tr key={event.id} style={{ borderBottom: "1px solid #eee" }}>
                              <td style={{ padding: "12px" }}>
                                <input
                                  type="checkbox"
                                  checked={selectedEvents.includes(event.id)}
                                  onChange={() => handleSelectEvent(event.id)}
                                />
                              </td>
                              <td style={{ padding: "12px", color: 'black' }}>
                                <span
                                  style={{
                                    color: event.id && event.title ? "#000" : "#666",
                                    cursor: event.id && event.title ? "pointer" : "not-allowed",
                                    textDecoration: event.id && event.title ? "underline" : "none",
                                  }}
                                  onClick={() => {
                                    if (event.id && event.title) {
                                      navigate(`/dashboard/${event.id}`);
                                    } else {
                                      toast.error("Event data is invalid or unavailable");
                                    }
                                  }}
                                >
                                  {event.title || "Untitled Event"}
                                </span>
                              </td>
                              <td style={{ padding: "12px" }}>{event.status}</td>
                              <td style={{ padding: "12px" }}>
                                <button
                                  className="btn-danger"
                                  onClick={() => handleDeleteEvent(event.id)}
                                  style={{
                                    marginRight: "10px",
                                    cursor: role === "staff" ? "not-allowed" : "pointer",
                                    opacity: role === "staff" ? 0.6 : 1,
                                    boxShadow: role === "staff" ? "0 0 5px red" : "none",
                                  }}
                                  disabled={role === "staff"}
                                >
                                  Delete
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      {/* Pagination Controls for Previous Events */}
                      <div style={{ marginTop: "20px", display: "flex", justifyContent: "center", gap: "10px", marginBottom: '15px' }}>
                        <button
                          onClick={() => setPreviousPage((prev) => Math.max(prev - 1, 1))}
                          disabled={previousPage === 1}
                          style={{
                            padding: "8px 16px",
                            cursor: previousPage === 1 ? "not-allowed" : "pointer",
                            backgroundColor: previousPage === 1 ? "#ccc" : "#1a73e8",
                            color: "#fff",
                            border: "none",
                            borderRadius: "4px",
                          }}
                        >
                          Previous
                        </button>
                        {getPageNumbers(previousEvents.length).map((page) => (
                          <button
                            key={page}
                            onClick={() => setPreviousPage(page)}
                            style={{
                              padding: "8px 16px",
                              cursor: "pointer",
                              backgroundColor: previousPage === page ? "#1a73e8" : "#f5f5f5",
                              color: previousPage === page ? "#fff" : "#000",
                              border: "1px solid #ccc",
                              borderRadius: "4px",
                            }}
                          >
                            {page}
                          </button>
                        ))}
                        <button
                          onClick={() => setPreviousPage((prev) => Math.min(prev + 1, Math.ceil(previousEvents.length / eventsPerPage)))}
                          disabled={previousPage === Math.ceil(previousEvents.length / eventsPerPage)}
                          style={{
                            padding: "8px 16px",
                            cursor: previousPage === Math.ceil(previousEvents.length / eventsPerPage) ? "not-allowed" : "pointer",
                            backgroundColor: previousPage === Math.ceil(previousEvents.length / eventsPerPage) ? "#ccc" : "#1a73e8",
                            color: "#fff",
                            border: "none",
                            borderRadius: "4px",
                          }}
                        >
                          Next
                        </button>
                      </div>
                    </>
                  ) : (
                    <p style={{ padding: "10px" }}>No previous events.</p>
                  )}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <Footer token={token} />
    </div>
  );
}

export default Events;