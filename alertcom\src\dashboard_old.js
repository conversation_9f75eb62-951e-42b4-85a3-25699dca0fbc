/* eslint-disable react-hooks/rules-of-hooks */
// src/Dashboard.js
import React, { useState, useEffect, useCallback } from "react";
import { useParams, Navigate, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";

import io from "socket.io-client";
import { GoogleMap, Marker } from "@react-google-maps/api";
import "./styles.css";
import config from "./config";
import StatCard from "./components/StatCard";
const { baseUrl } = config;

const socket = io(`${baseUrl}`, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
});

const mapContainerStyle = {
  width: "100%",
  height: "400px",
};

class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ color: "red" }}>
          Error: {this.state.error?.message || "Something went wrong"}
        </div>
      );
    }
    return this.props.children;
  }
}

function Dashboard({ token, mapsLoaded }) {
  const { eventId } = useParams();
  const [eventInfo, setEventInfo] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [responders, setResponders] = useState([]);
  const [notifiedResponders, setNotifiedResponders] = useState([]);
  const [alert, setAlert] = useState("");
  const [fetchError, setFetchError] = useState(null);
  const [chatMessages, setChatMessages] = useState([]);
  const [chatInput, setChatInput] = useState("");
  const [loading, setLoading] = useState(true);
  const [eventLatLng, setEventLatLng] = useState(null);
  const [taskLocations, setTaskLocations] = useState({});
  const [reportToLocations, setReportToLocations] = useState({});
  const [showMapModal, setShowMapModal] = useState("");
  const [showResponderModal, setShowResponderModal] = useState(false);
  const [activeCardType, setActiveCardType] = useState(null);
  const [responderData, setResponderData] = useState([]);
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');

  // Handle StatCard click to show responder data in modal
  const handleStatCardClick = (cardType) => {
    setActiveCardType(cardType);
    let filteredData = [];
    let locationName = '';
    
    // Helper function to get responder name from various possible sources
    const getResponderName = (item) => {
      // Log the item to debug
      // console.log('Responder data:', JSON.stringify(item));
      
      // Find the assigned user if available
      if (item.assigned_to) {
        // console.log('Found assigned_to:', item.assigned_to);
        // Try to find the user in responders array
        const assignedUser = responders.find(r => r.id === item.assigned_to);
        if (assignedUser) {
          // console.log('Found assigned user:', assignedUser);
          return assignedUser.name || assignedUser.username || `User ${item.assigned_to}`;
        }
      }
      
      // Try all possible paths to find a name
      return item.responderName || 
             item.name || 
             item.username || 
             (item.user ? (item.user.name || item.user.username) : null) || 
             (item.responder ? (item.responder.name || item.responder.username) : null) || 
             (item.userId ? `User ${item.userId}` : null) || 
             (item.assigned_to ? `User ${item.assigned_to}` : null) || 
             (item.responderId ? `Responder ${item.responderId}` : null) || 
             'Unknown';
    };
    
    
    switch(cardType) {
      case 'notified':
        filteredData = notifiedResponders.map(responder => ({
          name: getResponderName(responder),
          status: 'Notified',
          timestamp: responder.notifiedAt || responder.createdAt,
          rawData: responder // Store the original data for debugging
        }));
        break;
      case 'acknowledged':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'acknowledged')
          .map(task => ({
            name: getResponderName(task),
            status: 'Acknowledged',
            timestamp: task.updatedAt || task.createdAt,
            eta: task.eta || 'N/A'
          }));
        break;
      case 'enroute':
        // For the main enroute card, show all enroute tasks
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'enroute')
          .map(task => {
            // console.log('Enroute task:', JSON.stringify(task));
            return {
              name: getResponderName(task),
              status: 'Enroute',
              timestamp: task.updatedAt || task.createdAt,
              eta: task.eta || 'N/A',
              rawData: task // Store the original data for debugging
            };
          });
        break;
      case 'delayed':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'delayed')
          .map(task => ({
            name: getResponderName(task),
            status: 'Delayed',
            timestamp: task.updatedAt || task.createdAt,
            eta: task.eta || 'N/A'
          }));
        break;
      case 'unable':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'unable')
          .map(task => ({
            name: getResponderName(task),
            status: 'Unable/Cancelled',
            timestamp: task.updatedAt || task.createdAt,
            eta: 'N/A'
          }));
        break;
      case 'arrived':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'arrived')
          .map(task => ({
            name: getResponderName(task),
            status: 'Arrived',
            timestamp: task.updatedAt || task.createdAt,
            eta: '0 min'
          }));
        break;
      case 'completed':
        filteredData = tasks
          .filter(t => t.status?.toLowerCase() === 'completed')
          .map(task => ({
            name: getResponderName(task),
            status: 'Completed',
            timestamp: task.updatedAt || task.createdAt,
            eta: 'N/A'
          }));
        break;
      case 'primaryLocation':
        // console.log('Primary location:', eventInfo.location);
        // For primary location, show all tasks reporting to the primary location
        locationName = formatLocation(eventInfo.location);
        // console.log('Primary location name 111:', eventInfo.location);

        // console.log('Primary location name sajib:', locationName.replace(/^[^,]+,\s*/, ''));
        // console.log('Tasks:', tasks);
        
        filteredData = tasks
          .filter(t => !t.report_to_location || isMatchingLocation(t.report_to_location, eventInfo.location))
          .map(task => ({
            name: getResponderName(task),
            status: task.status || 'Unknown',
            timestamp: task.updatedAt || task.createdAt,
            eta: task.eta || 'N/A',
            rawData: task // Store raw data for debugging
          }));
        break;
      default:
        // console.log('Default case:', cardType);
        // Handle additional locations or location-specific enroute/delayed
        if (cardType.startsWith('additionalLocation_')) {
          // console.log('additionalLocation_');
          const parts = cardType.split('_');
          const locationIndex = parseInt(parts[1] || '0');
          const additionalLocations = Object.keys(locationsData).filter(loc => loc !== formatLocation(eventInfo.location));
          
          if (additionalLocations.length > locationIndex) {
            locationName = additionalLocations[locationIndex];
            
            // Check if this is for a specific status (enroute/delayed)
            if (parts.length > 2 && parts[2] === 'enroute') {
              // Handle enroute for this location
              filteredData = tasks
                .filter(t => t.status?.toLowerCase() === 'enroute' && t.report_to_location === locationName)
                .map(task => ({
                  name: getResponderName(task),
                  status: 'Enroute',
                  timestamp: task.updatedAt || task.createdAt,
                  eta: task.eta || 'N/A',
                  rawData: task
                }));
              // Set a specific title
              setActiveCardType(`enroute_at_${locationName}`);
            } else if (parts.length > 2 && parts[2] === 'delayed') {
              // Handle delayed for this location
              filteredData = tasks
                .filter(t => t.status?.toLowerCase() === 'delayed' && t.report_to_location === locationName)
                .map(task => ({
                  name: getResponderName(task),
                  status: 'Delayed',
                  timestamp: task.updatedAt || task.createdAt,
                  eta: task.eta || 'N/A',
                  rawData: task
                }));
              // Set a specific title
              setActiveCardType(`delayed_at_${locationName}`);
            } else {
              // Show all tasks for this location
              filteredData = tasks
                .filter(t => t.report_to_location === locationName)
                .map(task => ({
                  name: getResponderName(task),
                  status: task.status || 'Unknown',
                  timestamp: task.updatedAt || task.createdAt,
                  eta: task.eta || 'N/A',
                  rawData: task
                }));
            }
          }
        } else if (cardType.startsWith('primaryLocation_')) {
          // console.log('primaryLocation_');
          // Handle specific status for primary location
          locationName = formatLocation(eventInfo.location);
          locationName = locationName.replace(/^[^,]+,\s*/, '');
          const status = cardType.split('_')[1];
          // console.log('Primary location name 22:', locationName);
          // console.log('Status:', status);
          
          if (status === 'enroute') {
            filteredData = tasks
              .filter(t => t.status?.toLowerCase() === 'enroute' && 
                       (!t.report_to_location || isMatchingLocation(t.report_to_location, locationName)))
              .map(task => ({
                name: getResponderName(task),
                status: 'Enroute',
                timestamp: task.updatedAt || task.createdAt,
                eta: task.eta || 'N/A',
                rawData: task // Store raw data for debugging
              }));
          } else if (status === 'delayed') {
            filteredData = tasks
              .filter(t => t.status?.toLowerCase() === 'delayed' && 
                       (!t.report_to_location || isMatchingLocation(t.report_to_location, locationName)))
              .map(task => ({
                name: getResponderName(task),
                status: 'Delayed',
                timestamp: task.updatedAt || task.createdAt,
                eta: task.eta || 'N/A',
                rawData: task // Store raw data for debugging
              }));
          }
        } else if (cardType.startsWith('additionalLocation_') && cardType.includes('_enroute')) {
          // Handle enroute for a specific additional location
          // console.log('additionalLocation_enroute');
          const locationParts = cardType.split('_');
          const locationIndex = parseInt(locationParts[1] || '0');
          const additionalLocations = Object.keys(locationsData).filter(loc => loc !== formatLocation(eventInfo.location));
          if (additionalLocations.length > locationIndex) {
            locationName = additionalLocations[locationIndex];
            filteredData = tasks
              .filter(t => t.status?.toLowerCase() === 'enroute' && t.report_to_location === locationName)
              .map(task => ({
                name: getResponderName(task),
                status: 'Enroute',
                timestamp: task.updatedAt || task.createdAt,
                eta: task.eta || 'N/A'
              }));
          }
        }
        break;
    }
    
    // Set a custom title for location-based views
    if (locationName && (cardType === 'primaryLocation' || cardType.startsWith('additionalLocation_'))) {
      setActiveCardType(`location_${locationName}`);
    }
    
    // For primary location enroute, we need special handling
    if (cardType === 'primaryLocation' && filteredData.length === 0) {
      // Try to get enroute tasks for this location specifically
      const primaryLocationName = formatLocation(eventInfo.location);
      const enrouteTasks = tasks.filter(t => 
        t.status?.toLowerCase() === 'enroute' && 
        (!t.report_to_location || t.report_to_location === primaryLocationName)
      );
      
      if (enrouteTasks.length > 0) {
        // console.log('Found enroute tasks for primary location:', enrouteTasks);
        filteredData = enrouteTasks.map(task => ({
          name: getResponderName(task),
          status: 'Enroute',
          timestamp: task.updatedAt || task.createdAt,
          eta: task.eta || 'N/A',
          rawData: task
        }));
      }
    }
    
    // Sort the data based on sortField and sortDirection
    const sortedData = [...filteredData].sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];
      
      // Handle special cases for sorting
      if (sortField === 'eta') {
        // Convert ETA strings to minutes for sorting
        aValue = aValue === 'N/A' ? 9999 : parseInt(aValue);
        bValue = bValue === 'N/A' ? 9999 : parseInt(bValue);
      } else if (sortField === 'timestamp') {
        // Convert timestamps to Date objects for sorting
        aValue = aValue ? new Date(aValue) : new Date(0);
        bValue = bValue ? new Date(bValue) : new Date(0);
      }
      
      // Sort based on direction
      if (sortDirection === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    setResponderData(sortedData);
    setShowResponderModal(true);
  };

  const navigate = useNavigate();

  if (!token) {
    // console.log("No token provided to Dashboard, redirecting to login");
    return <Navigate to="/login" />;
  }

  // Format location for display
  const formatLocation = (location) => {
    if (!location) return "Unknown";
    if (typeof location === "string") return location;
    if (typeof location === "object") {
      const { commonName, address, city, state, zip } = location;
      const namePart = commonName || "";
      const addressPart = address || "";
      const cityPart = city || "";
      const statePart = state || "";
      const zipPart = zip || "";
      return (
        `${namePart}${namePart && addressPart ? ", " : ""}${addressPart}${
          addressPart || namePart ? ", " : ""
        }${cityPart}${cityPart && statePart ? ", " : ""}${statePart} ${zipPart}`
          .trim()
          .replace(/,\s*,/g, ",")
          .replace(/,\s*$/, "") || "Unknown"
      );
    }
    return "Unknown";
  };

  // Check if a task's report_to_location matches the given location
  const isMatchingLocation = (taskLocation, targetLocation) => {
    // If the task has no report_to_location, it's considered to be at the primary location
    if (!taskLocation && targetLocation === eventInfo.location) {
      return true;
    }
    
    // If targetLocation is an object (like eventInfo.location)
    if (typeof targetLocation === 'object') {
      // If taskLocation is a string, it might be the formatted version or just the commonName
      if (typeof taskLocation === 'string') {
        const formattedTarget = formatLocation(targetLocation);
        return taskLocation === formattedTarget || 
               taskLocation === targetLocation.commonName;
      }
      // If both are objects, compare their properties
      if (typeof taskLocation === 'object') {
        return taskLocation.commonName === targetLocation.commonName && 
               taskLocation.address === targetLocation.address;
      }
    }
    
    // Simple string comparison
    return taskLocation === targetLocation;
  };


  const geocodeLocation = useCallback(
    (location, setter, id = null) => {
      if (!mapsLoaded || !window.google || !window.google.maps) {
        // console.log("Google Maps API not loaded yet for geocoding:", location);
        return;
      }
      console.time(`Geocode-${id || "event"}`);
      const geocoder = new window.google.maps.Geocoder();
      geocoder.geocode({ address: location }, (results, status) => {
        if (status === "OK") {
          const { lat, lng } = results[0].geometry.location;
          const coords = { lat: lat(), lng: lng() };
          if (id) {
            setter((prev) => {
              const newLocations = { ...prev, [id]: coords };
              return newLocations;
            });
          } else {
            setter(coords);
          }
        } else {
          console.error(
            `Geocoding failed for ${location} (${id || "event"}):`,
            status
          );
          if (!id) setter(null);
        }
        console.timeEnd(`Geocode-${id || "event"}`);
      });
    },
    [mapsLoaded]
  );

  useEffect(() => {
    const fetchData = async (init = false) => {
      if (!token || !eventId) {
        toast.error("Missing required data for fetch: token or event ID!", {
          position: "top-right",
          autoClose: 3000,
        });
        setLoading(false);
        return;
      }

      if (init) {
        setLoading(true);
      }

      try {
        const eventResponse = await fetch(
          `${baseUrl}/active-events/${eventId}`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );

        if (!eventResponse.ok) {
          const eventData = await eventResponse.json();
          toast.error(
            `Failed to fetch event: ${eventResponse.status} - ${
              eventData?.error || "Unknown error"
            }`,
            {
              position: "top-right",
              autoClose: 4000,
            }
          );
          setLoading(false);
          return; // Avoid further execution or reload
        }

        const eventData = await eventResponse.json();

        setEventInfo({ ...eventData, tasks: undefined });
        setTasks(eventData.tasks || []);

        const assignedIds = eventData.assignIds || [];
        const notifyAll = eventData.notifyAll && assignedIds.length === 0;

        // Build URL with query params only if needed
        let respondersUrl = `${baseUrl}/responders`;

        if (assignedIds.length > 0 && !notifyAll) {
          const params = new URLSearchParams();
          assignedIds.forEach((id) => params.append("ids[]", id));
          respondersUrl += `?${params.toString()}`;
        }

        const respondersResponse = await fetch(respondersUrl, {
          headers: { Authorization: `Bearer ${token}` },
        });

        if (!respondersResponse.ok) {
          const errorText = await respondersResponse.text();
          toast.error(
            `Failed to fetch responders: ${respondersResponse.status} - ${errorText}`,
            {
              position: "top-right",
              autoClose: 4000,
            }
          );
          setLoading(false);
          return; // Also avoid further execution
        }

        const allResponders = await respondersResponse.json();

        setResponders(allResponders || []);
        setNotifiedResponders(
          notifyAll
            ? allResponders
            : allResponders.filter((r) => assignedIds.includes(r.id))
        );

        const eventLocation = formatLocation(eventData.location);

        if (eventLocation && mapsLoaded)
          geocodeLocation(eventLocation, setEventLatLng);

        eventData.included_report_to_locations?.forEach((loc) => {
          const locString = `${loc.commonName || ""}, ${loc.address || ""}, ${
            loc.city || ""
          }, ${loc.state || ""} ${loc.zip || ""}`
            .trim()
            .replace(/,\s*,/g, ",")
            .replace(/,\s*$/, "");
          if (locString && mapsLoaded)
            geocodeLocation(locString, setReportToLocations, locString);
        });

        eventData.tasks?.forEach((task) => {
          if (task.report_to_location && mapsLoaded)
            geocodeLocation(task.report_to_location, setTaskLocations, task.id);
        });

        setLoading(false);
      } catch (err) {
        toast.error(`Unexpected error occurred: ${err.message}`, {
          position: "top-right",
          autoClose: 4000,
        });
        setFetchError(err.message);
        setLoading(false);
        // Removed navigate to /events
      }
    };

    fetchData(true);

    const interval = setInterval(() => {
      fetchData();
    }, 20000); // 20 seconds

    return () => clearInterval(interval);
  }, [token, eventId, mapsLoaded, geocodeLocation]);

  useEffect(() => {
    // console.log("Socket setup useEffect");
    socket.on("connect", () => {
      // console.log("Socket connected to server");
      socket.emit("join", `event-${eventId}`);
      // console.log(`Joined room event-${eventId}`);
    });
    socket.on("connect_error", (err) =>
      console.error("Socket connection error:", err)
    );
    socket.on("new-event", (newEvent) => {
      if (newEvent.id === parseInt(eventId)) {
        // console.log("New event received:", newEvent);
        setEventInfo({ ...newEvent, tasks: undefined });
        setTasks(newEvent.tasks || []);
        if (newEvent.status === "open")
          setAlert(`New event: ${newEvent.title}`);
        const eventLocation = formatLocation(newEvent.location);
        if (eventLocation) geocodeLocation(eventLocation, setEventLatLng);
      }
    });
    socket.on("task-assigned", ({ task }) => {
      // console.log("Task assigned received:", task);
      setTasks((prev) => {
        const newTasks = [...prev.filter((t) => t.id !== task.id), task];
        // console.log("New tasks after task-assigned:", newTasks);
        return newTasks;
      });
      if (task.report_to_location)
        geocodeLocation(task.report_to_location, setTaskLocations, task.id);
    });
    socket.on("task-response", (task) => {
      // console.log("Task response received:", task);
      setTasks((prev) => {
        const updatedTasks = prev.map((t) =>
          t.assigned_to === task.assigned_to && t.event_id === task.event_id
            ? { ...task }
            : t
        );
        // console.log("Updated tasks after task-response:", updatedTasks);
        return updatedTasks;
      });
      if (task.report_to_location)
        geocodeLocation(task.report_to_location, setTaskLocations, task.id);
    });
    socket.on("chat", (message) => {
      setChatMessages((prev) => [...prev, message]);
    });
    socket.on("responder-update", (responder) => {
      // console.log("Responder update received:", responder);
      setResponders((prev) =>
        prev.map((r) => (r.id === responder.id ? { ...r, ...responder } : r))
      );
    });

    return () => {
      // console.log("Cleaning up socket listeners");
      socket.off("connect");
      socket.off("connect_error");
      socket.off("new-event");
      socket.off("task-assigned");
      socket.off("task-response");
      socket.off("chat");
      socket.off("responder-update");
      socket.emit("leave", `event-${eventId}`);
    };
  }, [eventId, geocodeLocation]);

  const getResponderStatusData = useCallback(() => {
    const statusData = responders.map((responder) => {
      const assignedTask = tasks.find((t) => t.assigned_to === responder.id);
      const taskStatus = assignedTask
        ? (assignedTask.status || "pending").toLowerCase()
        : "available";
      return {
        username: responder.username || "Unknown",
        notified: notifiedResponders.some((r) => r.id === responder.id),
        total: true,
        assigned: !!assignedTask,
        acknowledged: taskStatus === "acknowledged",
        enroute: taskStatus === "enroute",
        delayed: taskStatus === "delayed",
        unable: taskStatus === "unable",
        available: !assignedTask,
        job_role: responder.job_role || "Unknown",
      };
    });

    return statusData;
  }, [responders, tasks, notifiedResponders]);

  const calculateETA = useCallback(
    (responder, taskId) => {
      if (
        !responder ||
        !responder.latitude ||
        !responder.longitude ||
        !taskLocations[taskId]
      ) {
        return { value: "TBD", fresh: false };
      }
      const R = 6371; // Earth radius in km
      const dLat =
        ((taskLocations[taskId].lat - responder.latitude) * Math.PI) / 180;
      const dLon =
        ((taskLocations[taskId].lng - responder.longitude) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((responder.latitude * Math.PI) / 180) *
          Math.cos((taskLocations[taskId].lat * Math.PI) / 180) *
          Math.sin(dLon / 2) *
          Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c; // Distance in km
      const speed = 50; // km/h
      const etaMinutes = Math.round((distance / speed) * 60);

      const now = new Date();
      const lastUpdate = new Date(responder.last_updated || now);
      const timeSinceUpdate = (now - lastUpdate) / (1000 * 60); // Minutes since update
      const fresh = timeSinceUpdate < 1;
      let adjustedETA = etaMinutes;
      if (!fresh && timeSinceUpdate > 1) {
        adjustedETA = Math.max(0, etaMinutes - Math.round(timeSinceUpdate));
      }
      return { value: `${adjustedETA} min`, fresh };
    },
    [taskLocations]
  );

  const getLocationsData = useCallback(() => {
    const locations = {};
    const eventLocation = formatLocation(eventInfo?.location);
    locations[eventLocation] = {
      assigned: [],
      enroute: [],
      arrived: [],
      staffNeeded:
        eventInfo?.included_report_to_locations?.reduce(
          (sum, loc) => sum + (loc.staffNeeded || 2),
          0
        ) || 0,
      resources: [],
      responding: [],
      locationTitle: "Event Location"
    };
    
    // Create a mapping of addresses to location titles for consistent key lookup
    const addressToTitleMap = {};
    
    eventInfo?.included_report_to_locations?.forEach((loc) => {
      // Use commonName as locationTitle if available, otherwise fallback to address
      const locationTitle = loc.commonName || `${loc.address || loc.location || "Unknown"}, ${
        loc.city || ""
      }, ${loc.state || ""} ${loc.zip || ""}`
        .replace(/,\s*,/g, ",")
        .replace(/,\s*$/, "")
        .trim();
      
      // Store the address to title mapping for later lookup
      if (loc.address) {
        addressToTitleMap[loc.address] = locationTitle;
      }
      if (loc.location) {
        addressToTitleMap[loc.location] = locationTitle;
      }
      
      locations[locationTitle] = {
        assigned: [],
        enroute: [],
        arrived: [],
        staffNeeded: loc.staffNeeded || 2,
        resources: loc.resources || [],
        responding: [],
        locationTitle: loc.commonName || locationTitle // Store the title for reference
      };
    });

    tasks.forEach((task) => {
      
      const responder = responders.find((r) => r.id === task.assigned_to);
      if (!responder) {
        // console.log(`Responder not found for task:`, task);
        return;
      }
      const locParts = eventInfo?.included_report_to_locations?.find(
        (loc) =>
          loc.address === task.report_to_location ||
          loc.location === task.report_to_location
      ) || {
        address: task.report_to_location || eventInfo?.location || "Unknown",
        city: "",
        state: "",
        zip: "",
        commonName: ""
      };
      // Find the appropriate location key using the address mapping or fallback to formatting
      let locKey;
      
      // First check if we have this location in our mapping
      if (locParts.address && addressToTitleMap[locParts.address]) {
        locKey = addressToTitleMap[locParts.address];
      } else if (locParts.location && addressToTitleMap[locParts.location]) {
        locKey = addressToTitleMap[locParts.location];
      } else if (locParts.commonName) {
        // Try to find by commonName
        locKey = locParts.commonName;
      } else {
        // Fallback to formatted address
        locKey = `${locParts.address}, ${locParts.city}, ${locParts.state} ${locParts.zip}`
          .replace(/,\s*,/g, ",")
          .replace(/,\s*$/, "")
          .trim();
      }
      
      // If we still don't have a valid location, use the event location as fallback
      if (!locations[locKey]) {
        // console.log(`Location not found for key: ${locKey}, using event location as fallback`);
        locKey = eventLocation;
      }
      
      const status = task.status?.toLowerCase() || "pending";
      const eta = calculateETA(responder, task.id);
      const responderData = {
        name: responder.username + "(" + responder.job_role + ")" || `Responder ${task.assigned_to}`,
        eta,
      };

      if (locations[locKey] && ["acknowledged", "enroute", "delayed"].includes(status)) {
        locations[locKey].assigned.push(responderData);
        if (status === "enroute") {
          locations[locKey].enroute.push(responderData);
          locations[locKey].responding.push(responderData.name);
        }
      }
      if (locations[locKey] && status === "arrived") {
        locations[locKey].arrived.push(responderData);
        locations[locKey].responding.push(responderData.name);
      }
    });

    Object.keys(locations).forEach((loc) => {
      const enrouteETAs = locations[loc].enroute.map(
        (r) => parseInt(r.eta.value) || Infinity
      );
      const etaToStaffed =
        locations[loc].staffNeeded <= locations[loc].enroute.length
          ? "Staffed"
          : enrouteETAs.length > 0
          ? `${Math.min(...enrouteETAs)} min`
          : "N/A";
      locations[loc].etaToStaffed = etaToStaffed;

      const resources = locations[loc].resources;
      if (resources.length > 0) {
        const enrouteCount = locations[loc].enroute.length;
        let totalAssigned = 0;
        let resourceIndex = 0;
        while (
          resourceIndex < resources.length &&
          totalAssigned < enrouteCount
        ) {
          totalAssigned += resources[resourceIndex].responderCount || 2;
          resourceIndex++;
        }
        const nextResource = resources[resourceIndex];
        const respondersToNext = nextResource
          ? (nextResource.responderCount || 2) -
            (enrouteCount -
              (totalAssigned - (nextResource?.responderCount || 2)))
          : 0;
        const etaToNext =
          respondersToNext <= 0
            ? "Ready"
            : enrouteETAs.length > 0
            ? `${Math.min(...enrouteETAs)} min`
            : "N/A";
        locations[loc].etaToNextResource = etaToNext;
      } else {
        locations[loc].etaToNextResource = "N/A";
      }
    });

    return locations;
  }, [eventInfo, tasks, responders, calculateETA]);

  const locationsData = getLocationsData();

  useEffect(() => {}, [tasks, taskLocations, reportToLocations]);

  return (
    <div
      className="container"
      style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}
    >
      <ErrorBoundary>
        {loading ? (
          <div
            className="card"
            style={{
              padding: "20px",
              borderRadius: "10px",
              boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            }}
          >
            Loading event data...
          </div>
        ) : fetchError ? (
          <div
            className="card"
            style={{
              padding: "20px",
              borderRadius: "10px",
              boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              color: "var(--danger)",
            }}
          >
            Error: {fetchError}
          </div>
        ) : !eventInfo ? (
          <div
            className="card"
            style={{
              padding: "20px",
              borderRadius: "10px",
              boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
            }}
          >
            No event data available
          </div>
        ) : (
          <>
            <section
              className="card"
              style={{
                padding: "20px",
                marginBottom: "20px",
                borderRadius: "10px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                maxHeight: "600px",
              }}
            >
              <h1 style={{ color: "#1a73e8", marginBottom: "15px", textAlign: "center", fontSize: "28px", fontWeight: "bold" }}>
                Live Event Dashboard
              </h1>
              <div style={{ display: "flex", justifyContent: "center", marginBottom: "15px", gap: "20px" }}>
                <h3 style={{ margin: 0 }}>Event ID: {eventInfo.id || "N/A"}</h3>
                <h3 style={{ margin: 0 }}>Event Title: {eventInfo.title || "N/A"}</h3>

              </div>
              
              {/* Status Boxes */}
              <div style={{ display: "flex", justifyContent: "space-between", marginBottom: "20px", flexWrap: "wrap", gap: "10px" }}>
                <div style={{ flex: "1", minWidth: "150px", maxWidth: "200px" }}>
                  <StatCard
                    title="Notified"
                    value={notifiedResponders.length}
                    color="#2196f3"
                    onClick={() => handleStatCardClick('notified')}
                  />
                </div>
                
                <div style={{ flex: "1", minWidth: "150px", maxWidth: "200px" }}>
                  <StatCard
                    title="Acknowledged"
                    value={tasks.filter(t => t.status?.toLowerCase() === 'acknowledged').length}
                    color="#4caf50"
                    onClick={() => handleStatCardClick('acknowledged')}
                  />
                </div>
                
                <div style={{ flex: "1", minWidth: "150px", maxWidth: "200px" }}>
                  <StatCard
                    title="Enroute"
                    value={tasks.filter(t => t.status?.toLowerCase() === 'enroute').length}
                    onClick={() => handleStatCardClick('enroute')}
                    color="#2196f3"
                  />
                </div>
                
                <div style={{ flex: "1", minWidth: "150px", maxWidth: "200px" }}>
                  <StatCard
                    title="Delayed"
                    value={tasks.filter(t => t.status?.toLowerCase() === 'delayed').length}
                    color="#ff9800"
                    onClick={() => handleStatCardClick('delayed')}
                  />
                </div>
                
                <div style={{ flex: "1", minWidth: "150px", maxWidth: "200px" }}>
                  <StatCard
                    title="Unable/Cancelled"
                    value={tasks.filter(t => t.status?.toLowerCase() === 'unable').length}
                    color="#f44336"
                    onClick={() => handleStatCardClick('unable')}
                  />
                </div>
              </div>
              
              <div
                style={{
                  display: "grid",
                  gridTemplateColumns: "1fr 1fr",
                  gap: "10px",
                }}
              >
                <p>
                  <strong>Info:</strong> {eventInfo.info || "N/A"}
                </p>
                <p>
                  <strong>Scale:</strong> {eventInfo.scale || "N/A"}
                </p>
                <p>
                  <strong>Urgency:</strong> {eventInfo.urgency || "N/A"}
                </p>
                <p>
                  <strong>Location:</strong>{" "}
                  {formatLocation(eventInfo.location)}
                </p>
                <p>
                  <strong>Status:</strong> {eventInfo.status || "N/A"}
                </p>
              </div>
              {alert && (
                <div style={{ color: "var(--warning)", marginTop: "10px" }}>
                  {alert}
                </div>
              )}
            </section>



            {/* <pre>{JSON.stringify(eventInfo, null, 2)}</pre> */}
            <section
              className="card"
              style={{
                padding: "20px",
                marginBottom: "20px",
                borderRadius: "10px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: "15px",
                }}
              >
                <h2
                  style={{
                    color: "#1a73e8",
                    margin: 0,
                    fontSize: "24px",
                    fontWeight: "bold",
                  }}
                >
                  Report to Locations
                </h2>
              </div>
              <div style={{ display: "flex", flexWrap: "wrap", gap: "15px", marginBottom: "20px" }}>
                {/* Event Location Card */}
                <div 
                  onClick={() => handleStatCardClick('primaryLocation')}
                  style={{ 
                    flex: "1", 
                    minWidth: "200px", 
                    maxWidth: "300px", 
                    border: "2px solid #1565C0", 
                    borderRadius: "4px", 
                    overflow: "hidden", 
                    boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                    cursor: "pointer",
                    transition: "transform 0.2s, box-shadow 0.2s",
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                    }
                  }}>
                  <div style={{ backgroundColor: "#1565C0", padding: "8px", borderBottom: "1px solid #1565C0", textAlign: "center", fontWeight: "800", fontSize: "14px", color: "white" }}>
                    Primary Event Location
                  </div>
                  <div style={{ padding: "5px", backgroundColor: "#f8f9fa", borderBottom: "1px solid #ddd", fontSize: "13px", textAlign: "center", fontWeight: "bold" }}>
                    {formatLocation(eventInfo.location)}
                  </div>
                  <div>
                    <div 
                      style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering the parent onClick
                        handleStatCardClick('primaryLocation_enroute');
                      }}
                    >
                      <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Enroute</div>
                      <div style={{ width: "60%", padding: "6px", backgroundColor: "#8BC34A", color: "#fff", textAlign: "center", fontSize: "13px" }}>
                        {locationsData[formatLocation(eventInfo.location)].enroute.length}
                      </div>
                    </div>
                    <div 
                      style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent triggering the parent onClick
                        handleStatCardClick('primaryLocation_delayed');
                      }}
                    >
                      <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Delayed</div>
                      <div style={{ width: "60%", padding: "6px", backgroundColor: "#FFEB3B", color: "#000", textAlign: "center", fontSize: "13px" }}>
                        {tasks.filter(t => t.status?.toLowerCase() === 'delayed' && (!t.report_to_location || t.report_to_location === formatLocation(eventInfo.location))).length}
                      </div>
                    </div>
                    <div style={{ display: "flex" }}>
                      <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>ETA to Staffed</div>
                      <div style={{ width: "60%", padding: "6px", textAlign: "center", fontSize: "13px" }}>
                        {locationsData[formatLocation(eventInfo.location)].etaToStaffed}
                      </div>
                    </div>
                  </div>
                </div>
                {/* Additional Location Cards */}
                {Object.keys(locationsData)
                  .filter(loc => loc !== formatLocation(eventInfo.location))
                  .map((loc, index) => (
                    
                    <div 
                      key={index} 
                      onClick={() => handleStatCardClick(`additionalLocation_${index}`)}
                      style={{ 
                        flex: "1", 
                        minWidth: "200px", 
                        maxWidth: "300px", 
                        border: "1px solid #ddd", 
                        borderRadius: "4px", 
                        overflow: "hidden",
                        cursor: "pointer",
                        transition: "transform 0.2s, box-shadow 0.2s",
                        '&:hover': {
                          transform: 'translateY(-3px)',
                          boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                        }
                      }}>
                      <div style={{ backgroundColor: index % 3 === 0 ? "#4CAF50" : index % 3 === 1 ? "#FF9800" : "#9C27B0", padding: "8px", borderBottom: "1px solid #ddd", textAlign: "center", fontWeight: "bold", fontSize: "14px", whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis", color: "white" }}>
                        {locationsData[loc]?.locationTitle}
                      </div>
                      <div style={{ padding: "5px", backgroundColor: "#f8f9fa", borderBottom: "1px solid #ddd", fontSize: "13px", textAlign: "center", fontWeight: "bold" }}>
                        {loc}
                      </div>
                      <div>
                        <div 
                          style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering the parent onClick
                            handleStatCardClick(`additionalLocation_${index}_enroute`);
                          }}
                        >
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Enroute</div>
                          <div style={{ width: "60%", padding: "6px", backgroundColor: "#8BC34A", color: "#fff", textAlign: "center", fontSize: "13px" }}>
                            {locationsData[loc].enroute.length}
                          </div>
                        </div>
                        <div 
                          style={{ display: "flex", borderBottom: "1px solid #ddd", cursor: 'pointer' }}
                          onClick={(e) => {
                            e.stopPropagation(); // Prevent triggering the parent onClick
                            handleStatCardClick(`additionalLocation_${index}_delayed`);
                          }}
                        >
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>Delayed</div>
                          <div style={{ width: "60%", padding: "6px", backgroundColor: "#FFEB3B", color: "#000", textAlign: "center", fontSize: "13px" }}>
                            {tasks.filter(t => t.status?.toLowerCase() === 'delayed' && t.report_to_location === loc).length}
                          </div>
                        </div>
                        <div style={{ display: "flex" }}>
                          <div style={{ width: "40%", padding: "6px", fontWeight: "bold", fontSize: "13px" }}>ETA to Staffed</div>
                          <div style={{ width: "60%", padding: "6px", textAlign: "center", fontSize: "13px" }}>
                            {locationsData[loc].etaToStaffed}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))
                }
              </div>
            </section>

            {/* Response Profiles Section */}
            <section
              className="card"
              style={{
                padding: "20px",
                marginBottom: "20px",
                borderRadius: "10px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
              }}
            >
              <h2
                style={{
                  color: "#1a73e8",
                  margin: "0 0 15px 0",
                  fontSize: "24px",
                  fontWeight: "bold",
                  textAlign: "center"
                }}
              >
                Response Profiles
              </h2>
              
              {/* Group responders by job role in a card-based layout */}
              <div style={{ display: "flex", flexWrap: "wrap", gap: "15px" }}>
                {(() => {
                  // Group responders by role
                  const respondersByRole = {};
                  responders.filter(r => r.job_role).forEach(responder => {
                    const role = responder.job_role || "Unknown";
                    if (!respondersByRole[role]) {
                      respondersByRole[role] = [];
                    }
                    
                    // Add ETA information to responder object
                    const assignedTask = tasks.find(t => t.assigned_to === responder.id);
                    const eta = assignedTask && taskLocations[assignedTask.id] ? 
                      calculateETA(responder, assignedTask.id).value : "N/A";
                      
                    respondersByRole[role].push({
                      id: responder.id,
                      name: responder.username || "Unknown",
                      eta: eta
                    });
                  });
                  
                  // Create a card for each role
                  return Object.keys(respondersByRole).map((role, roleIndex) => (
                    <div 
                      key={role} 
                      style={{ 
                        flex: "1", 
                        minWidth: "200px", 
                        border: "1px solid #ddd", 
                        borderRadius: "4px", 
                        overflow: "hidden" 
                      }}
                    >
                      <div 
                        style={{ 
                          backgroundColor: roleIndex % 4 === 0 ? "#3F51B5" : roleIndex % 4 === 1 ? "#009688" : roleIndex % 4 === 2 ? "#E91E63" : "#FF5722", 
                          padding: "8px", 
                          borderBottom: "1px solid #ddd", 
                          color: "white", 
                          fontWeight: "bold", 
                          fontSize: "14px", 
                          textAlign: "center" 
                        }}
                      >
                        {role} ({respondersByRole[role].length})
                      </div>
                      <div style={{ maxHeight: "300px", overflowY: "auto" }}>
                        {respondersByRole[role].length > 0 ? (
                          respondersByRole[role].map((responder, idx) => (
                            <div 
                              key={idx} 
                              style={{ 
                                padding: "8px", 
                                borderBottom: idx < respondersByRole[role].length - 1 ? "1px solid #eee" : "none",
                                backgroundColor: idx % 2 === 0 ? "#f9f9f9" : "#fff"
                              }}
                            >
                              <div style={{ fontWeight: "bold", fontSize: "13px" }}>{responder.name}</div>
                              <div style={{ fontSize: "12px", color: "#666" }}>ETA: {responder.eta}</div>
                            </div>
                          ))
                        ) : (
                          <div style={{ padding: "10px", textAlign: "center", color: "#666", fontSize: "13px" }}>No responders</div>
                        )}
                      </div>
                    </div>
                  ));
                })()}
              </div>
            </section>

            {/* Map Section */}
            <section
              className="card"
              style={{
                padding: "20px",
                marginBottom: "20px",
                borderRadius: "10px",
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                maxHeight: "560px",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between", // Align items to the left and right
                  alignItems: "center", // Vertically center the items
                  marginBottom: "15px",
                }}
              >
                <h2 style={{ color: "#1a73e8", marginBottom: "15px" }}>Map</h2>
                <button
                  className="btn-secondary"
                  style={{ marginBottom: "1rem" }}
                  onClick={() =>
                    setShowMapModal(`/map-popup/${eventId}?token=${token}`)
                  }
                >
                  View Map
                </button>
              </div>
              {mapsLoaded && eventLatLng ? (
                <GoogleMap
                  mapContainerStyle={mapContainerStyle}
                  center={eventLatLng}
                  zoom={10}
                >
                  <Marker
                    position={eventLatLng}
                    title={eventInfo.title || "Event Location"}
                    icon={{
                      url: "http://maps.google.com/mapfiles/ms/icons/red-dot.png",
                    }}
                  />
                  {Object.entries(reportToLocations).map(([loc, coords]) => (
                    <Marker
                      key={loc}
                      position={coords}
                      title={loc}
                      icon={{
                        url: "http://maps.google.com/mapfiles/ms/icons/green-dot.png",
                      }}
                    />
                  ))}
                  {tasks.map((task) => {
                    const responder = responders.find(
                      (r) => r.id === task.assigned_to
                    );
                    return responder &&
                      responder.latitude &&
                      responder.longitude ? (
                      <Marker
                        key={responder.id}
                        position={{
                          lat: responder.latitude,
                          lng: responder.longitude,
                        }}
                        title={
                          responder.username || `Responder ${responder.id}`
                        }
                        icon={{
                          url: "http://maps.google.com/mapfiles/ms/icons/blue-dot.png",
                        }}
                      />
                    ) : null;
                  })}
                </GoogleMap>
              ) : (
                <div>
                  {mapsLoaded
                    ? "No event location data available - check console for geocoding errors"
                    : "Map unavailable—Google Maps API failed to load"}
                </div>
              )}
            </section>
          </>
        )}
      </ErrorBoundary>
      
      {/* Responder Activity Modal */}
      {showResponderModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowResponderModal(false)}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="modal-content"
            style={{
              backgroundColor: "#fff",
              borderRadius: "8px",
              padding: "20px",
              width: "90%",
              maxWidth: "800px",
              maxHeight: "80vh",
              overflow: "auto",
              boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
            }}
          >
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
              <h2 style={{ margin: 0 }}>
                {activeCardType === 'notified' && 'Notified Responders'}
                {activeCardType === 'acknowledged' && 'Acknowledged Responders'}
                {activeCardType === 'enroute' && 'Enroute Responders'}
                {activeCardType === 'delayed' && 'Delayed Responders'}
                {activeCardType === 'unable' && 'Unable/Cancelled Responders'}
                {activeCardType === 'arrived' && 'Arrived Responders'}
                {activeCardType === 'completed' && 'Completed Tasks'}
                {activeCardType === 'primaryLocation' && `Primary Location: ${formatLocation(eventInfo.location)}`}
                {activeCardType === 'primaryLocation_enroute' && `Enroute to Primary Location: ${formatLocation(eventInfo.location)}`}
                {activeCardType === 'primaryLocation_delayed' && `Delayed to Primary Location: ${formatLocation(eventInfo.location)}`}
                {activeCardType.startsWith('location_') && `Location: ${activeCardType.split('_').slice(1).join('_')}`}
                {activeCardType.startsWith('enroute_at_') && `Enroute to ${activeCardType.split('_at_')[1]}`}
                {activeCardType.startsWith('delayed_at_') && `Delayed to ${activeCardType.split('_at_')[1]}`}
              </h2>
              <button
                onClick={() => setShowResponderModal(false)}
                style={{
                  backgroundColor: "#f44336",
                  border: "none",
                  borderRadius: "50%",
                  width: "30px",
                  height: "30px",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  cursor: "pointer",
                  color: "white",
                  fontWeight: "bold",
                }}
              >
                ✕
              </button>
            </div>
            {console.log("responderData",responderData)}
            {responderData.length > 0 ? (
              <div>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ backgroundColor: '#f5f5f5' }}>
                      <th 
                        onClick={() => {
                          setSortDirection(sortField === 'name' && sortDirection === 'asc' ? 'desc' : 'asc');
                          setSortField('name');
                        }}
                        style={{ 
                          padding: '10px', 
                          textAlign: 'left', 
                          borderBottom: '1px solid #ddd',
                          cursor: 'pointer',
                          position: 'relative'
                        }}
                      >
                        Name {sortField === 'name' && (sortDirection === 'asc' ? ' ↑' : ' ↓')}
                      </th>
                      <th style={{ padding: '10px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>Status</th>
                      {/* <th 
                        onClick={() => {
                          setSortDirection(sortField === 'timestamp' && sortDirection === 'asc' ? 'desc' : 'asc');
                          setSortField('timestamp');
                        }}
                        style={{ 
                          padding: '10px', 
                          textAlign: 'left', 
                          borderBottom: '1px solid #ddd',
                          cursor: 'pointer'
                        }}
                      >
                        Time {sortField === 'timestamp' && (sortDirection === 'asc' ? ' ↑' : ' ↓')}
                      </th> */}
                      {activeCardType !== 'notified' && (
                        <th 
                          onClick={() => {
                            setSortDirection(sortField === 'eta' && sortDirection === 'asc' ? 'desc' : 'asc');
                            setSortField('eta');
                          }}
                          style={{ 
                            padding: '10px', 
                            textAlign: 'left', 
                            borderBottom: '1px solid #ddd',
                            cursor: 'pointer'
                          }}
                        >
                          ETA {sortField === 'eta' && (sortDirection === 'asc' ? ' ↑' : ' ↓')}
                        </th>
                      )}
                    </tr>
                  </thead>
                  <tbody>
                    {responderData.map((responder, index) => (
                      <tr key={index} style={{ borderBottom: '1px solid #ddd' }}>
                        <td style={{ padding: '10px' }}>{responder.name || 'Unknown'}</td>
                        <td style={{ padding: '10px' }}>{responder.status || 'N/A'}</td>
                        {/* <td style={{ padding: '10px' }}>{responder.timestamp ? new Date(responder.timestamp).toLocaleString() : 'N/A'}</td> */}
                        {activeCardType !== 'notified' && (
                          <td style={{ padding: '10px' }}>{responder.eta || 'N/A'}</td>
                        )}
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <p>No data available for this category.</p>
              </div>
            )}
          </div>
        </div>
      )}
      
      {!!showMapModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowMapModal("")}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="iframe-wrapper"
            style={{
              position: "relative",
              width: "100vw",
              height: "100vh",
              overflow: "auto",
              border: "none",
              outline: "none",
              backgroundColor: "#fff",
              borderRadius: "8px",
            }}
          >
            {/* Close Button - shown on md+ screens */}
            <button
              onClick={() => setShowMapModal("")}
              className="close-btn-md"
              style={{
                position: "absolute",
                top: "10px",
                right: "10px",
                backgroundColor: "#00a8b5",
                border: "none",
                borderRadius: "50%",
                width: "40px",
                height: "40px",
                color: "#fff",
                display: "none", // hidden by default, shown via media query
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
                zIndex: 10000,
              }}
            >
              ✕
            </button>

            <iframe
              src={showMapModal}
              title="Map"
              width="100%"
              height="100%"
              style={{
                border: "none",
                outline: "none",
              }}
            />
          </div>
        </div>
      )}

      {!!showMapModal && (
        <div
          className="modal-overlay"
          onClick={() => setShowMapModal("")}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 9999,
          }}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="iframe-wrapper"
            style={{
              position: "relative",
              width: "100vw",
              height: "100vh",
              overflow: "auto",
              border: "none",
              outline: "none",
              backgroundColor: "#fff",
              borderRadius: "8px",
            }}
          >
            {/* Close Button - shown on md+ screens */}
            <button
              onClick={() => setShowMapModal("")}
              className="close-btn-md"
              style={{
                position: "absolute",
                top: "10px",
                right: "10px",
                backgroundColor: "#00a8b5",
                border: "none",
                borderRadius: "50%",
                width: "40px",
                height: "40px",
                color: "#fff",
                display: "none", // hidden by default, shown via media query
                justifyContent: "center",
                alignItems: "center",
                cursor: "pointer",
                zIndex: 10000,
              }}
            >
              ✕
            </button>

            <iframe
              src={showMapModal}
              title="Map"
              width="100%"
              height="100%"
              style={{
                border: "none",
                outline: "none",
              }}
            />
          </div>
        </div>
      )}

      {/* {!!showMapModal && (
          <div className="delete-modal-overlay">
            <div className="delete-modal">
              <div style={{display: 'flex', justifyContent: 'space-between'}}>
                <h2>Add New User</h2>
                <button
                  type="button" style={{height: '40px', width: '40px', display: 'flex', justifyContent: "center", alignItems: 'center', backgroundColor: '#00a8b5', border: 'none', borderRadius: '10px', cursor: 'pointer' }}
                  onClick={() => setShowMapModal("")}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="20" height="20" viewBox="0 0 50 50">
                    <path fill="#fff" d="M 9.15625 6.3125 L 6.3125 9.15625 L 22.15625 25 L 6.21875 40.96875 L 9.03125 43.78125 L 25 27.84375 L 40.9375 43.78125 L 43.78125 40.9375 L 27.84375 25 L 43.6875 9.15625 L 40.84375 6.3125 L 25 22.15625 Z"></path>
                  </svg>
                </button>
              </div>

              <iframe src={showMapModal} title="" width={1200} height={800} />
            </div>
          </div>
        )} */}
    </div>  );

}

export default Dashboard;
