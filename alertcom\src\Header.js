// src/Header.js
import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import ActiveEventsDropdown from './ActiveEventsDropdown';

function Header({ token, role, handleLogout }) {
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const headerStyles = {
        background: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
        padding: '0',
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
        position: 'sticky',
        top: 0,
        zIndex: 1000,
        borderBottom: '3px solid #00bcd4',
    };

    const containerStyles = {
        maxWidth: '1200px',
        margin: '0 auto',
        padding: '15px 20px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'relative',
    };

    const logoStyles = {
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
    };

    const logoIconStyles = {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '40px',
        height: '40px',
        background: 'linear-gradient(135deg, #00bcd4, #0097a7)',
        borderRadius: '10px',
        boxShadow: '0 4px 8px rgba(0, 188, 212, 0.3)',
    };

    const logoTextStyles = {
        color: '#fff',
        fontSize: 'calc(var(--font-size-large) * 1.2)',
        fontWeight: '700',
        textDecoration: 'none',
        letterSpacing: '1px',
        textShadow: '2px 2px 4px rgba(0, 0, 0, 0.3)',
        fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif',
    };

    const navStyles = {
        display: 'flex',
        gap: '8px',
        alignItems: 'center',
    };

    const linkBaseStyles = {
        color: '#fff',
        textDecoration: 'none',
        fontSize: 'var(--font-size-base)',
        fontWeight: '500',
        padding: '10px 16px',
        borderRadius: '8px',
        transition: 'all 0.3s ease',
        position: 'relative',
        overflow: 'hidden',
        fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif',
    };

    const buttonStyles = {
        ...linkBaseStyles,
        background: 'linear-gradient(135deg, #ff6b35, #f7931e)',
        boxShadow: '0 4px 15px rgba(255, 107, 53, 0.3)',
        border: 'none',
        cursor: 'pointer',
        fontWeight: '600',
        textTransform: 'uppercase',
        letterSpacing: '0.5px',
    };

    const mobileMenuButtonStyles = {
        display: 'none',
        flexDirection: 'column',
        background: 'none',
        border: 'none',
        cursor: 'pointer',
        padding: '8px',
        borderRadius: '4px',
    };

    const hamburgerLineStyles = {
        width: '25px',
        height: '3px',
        backgroundColor: '#fff',
        margin: '3px 0',
        transition: '0.3s',
        borderRadius: '2px',
    };

    return (
        <>
            <style>
                {`
                    /* Mobile Styles */
                    @media (max-width: 768px) {
                        .header-container {
                            position: relative;
                        }
                        .mobile-nav {
                            display: ${isMobileMenuOpen ? 'flex' : 'none'} !important;
                            flex-direction: column;
                            position: absolute;
                            top: 100%;
                            left: 0;
                            right: 0;
                            background: linear-gradient(135deg, #1e3c72, #2a5298);
                            padding: 20px;
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                            gap: 12px;
                            z-index: 1000;
                            border-top: 1px solid rgba(255, 255, 255, 0.1);
                        }
                        .desktop-nav {
                            display: none !important;
                        }
                        .mobile-menu-btn {
                            display: flex !important;
                        }
                        .header-container {
                            padding: 12px 15px;
                        }
                        .logo-text {
                            font-size: calc(var(--font-size-large) * 0.9) !important;
                        }
                    }

                    /* Tablet Styles */
                    @media (min-width: 769px) and (max-width: 1024px) {
                        .mobile-nav {
                            display: none !important;
                        }
                        .desktop-nav {
                            display: flex !important;
                            gap: 6px;
                        }
                        .mobile-menu-btn {
                            display: none !important;
                        }
                        .nav-link, .launch-btn, .logout-btn {
                            padding: 8px 12px !important;
                            font-size: var(--font-size-small) !important;
                        }
                        .logo-text {
                            font-size: var(--font-size-large) !important;
                        }
                    }

                    /* Desktop Styles */
                    @media (min-width: 1025px) {
                        .mobile-nav {
                            display: none !important;
                        }
                        .desktop-nav {
                            display: flex !important;
                        }
                        .mobile-menu-btn {
                            display: none !important;
                        }
                    }

                    /* Hover Effects */
                    .nav-link:hover {
                        background: rgba(255, 255, 255, 0.1);
                        transform: translateY(-2px);
                        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
                    }
                    .launch-btn:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
                    }
                    .logout-btn:hover {
                        background: rgba(255, 255, 255, 0.1);
                        transform: translateY(-2px);
                    }
                    .mobile-menu-btn:hover {
                        background: rgba(255, 255, 255, 0.1);
                    }
                `}
            </style>
            <nav style={headerStyles}>
                <div className="header-container" style={containerStyles}>
                    <div style={logoStyles}>
                        <div style={logoIconStyles}>
                            <span style={{ color: '#fff', fontSize: 'var(--font-size-large)', fontWeight: 'bold' }}>⚡</span>
                        </div>
                        <Link to="/" className="logo-text" style={logoTextStyles}>
                            ALERTCOMM1
                        </Link>
                    </div>

                    {/* Desktop Navigation */}
                    <div className="desktop-nav" style={navStyles}>
                        <Link to="/" className="nav-link" style={linkBaseStyles}>
                            🏠 Home
                        </Link>
                        {(role === 'commander' || role === 'lead' || role === 'staff') && (
                            <>
                                <Link to="/events" className="nav-link" style={linkBaseStyles}>
                                    📅 Events
                                </Link>
                                <Link to="/dashboard" className="nav-link" style={linkBaseStyles}>
                                    📊 Dashboard
                                </Link>
                            </>
                        )}
                        
                        {role === 'commander' && (
                            <>
                                <Link to="/launch-event" className="launch-btn" style={buttonStyles}>
                                    🚀 Launch Event
                                </Link>
                                <Link to="/templates" className="nav-link" style={linkBaseStyles}>
                                    📋 Templates
                                </Link>
                                <Link to="/user-management" className="nav-link" style={linkBaseStyles}>
                                    👥 Users
                                </Link>
                                <Link to="/reporting" className="nav-link" style={linkBaseStyles}>
                                    📈 Reports
                                </Link>
                                <Link to="/settings" className="nav-link" style={linkBaseStyles}>
                                    ⚙️ Settings
                                </Link>
                            </>
                        )}
                        {role === 'staff' && <ActiveEventsDropdown token={token} />}
                        {token && (
                            <button onClick={handleLogout} className="logout-btn" style={{
                                ...linkBaseStyles,
                                background: 'linear-gradient(135deg, #f44336, #d32f2f)',
                                border: 'none',
                                cursor: 'pointer',
                                boxShadow: '0 4px 15px rgba(244, 67, 54, 0.3)',
                            }}>
                                🚪 Logout
                            </button>
                        )}
                    </div>

                    {/* Mobile Menu Button */}
                    <button 
                        className="mobile-menu-btn"
                        style={mobileMenuButtonStyles}
                        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    >
                        <div style={hamburgerLineStyles}></div>
                        <div style={hamburgerLineStyles}></div>
                        <div style={hamburgerLineStyles}></div>
                    </button>
                </div>

                {/* Mobile Navigation */}
                <div className="mobile-nav">
                    <Link to="/" style={linkBaseStyles} onClick={() => setIsMobileMenuOpen(false)}>
                        🏠 Home
                    </Link>
                    {(role === 'commander' || role === 'lead' || role === 'staff') && (
                        <>
                            <Link to="/events" style={linkBaseStyles} onClick={() => setIsMobileMenuOpen(false)}>
                                📅 Events
                            </Link>
                            <Link to="/dashboard" style={linkBaseStyles} onClick={() => setIsMobileMenuOpen(false)}>
                                📊 Dashboard
                            </Link>
                        </>
                    )}
                    
                    {role === 'commander' && (
                        <>
                            <Link to="/launch-event" style={buttonStyles} onClick={() => setIsMobileMenuOpen(false)}>
                                🚀 Launch Event
                            </Link>
                            <Link to="/templates" style={linkBaseStyles} onClick={() => setIsMobileMenuOpen(false)}>
                                📋 Templates
                            </Link>
                            <Link to="/user-management" style={linkBaseStyles} onClick={() => setIsMobileMenuOpen(false)}>
                                👥 Users
                            </Link>
                            <Link to="/reporting" style={linkBaseStyles} onClick={() => setIsMobileMenuOpen(false)}>
                                📈 Reports
                            </Link>
                            <Link to="/settings" style={linkBaseStyles} onClick={() => setIsMobileMenuOpen(false)}>
                                ⚙️ Settings
                            </Link>
                        </>
                    )}
                    {role === 'staff' && <ActiveEventsDropdown token={token} />}
                    {token && (
                        <button onClick={() => { handleLogout(); setIsMobileMenuOpen(false); }} style={{
                            ...linkBaseStyles,
                            background: 'linear-gradient(135deg, #f44336, #d32f2f)',
                            border: 'none',
                            cursor: 'pointer',
                            width: '100%',
                            textAlign: 'center',
                        }}>
                            🚪 Logout
                        </button>
                    )}
                </div>
            </nav>
        </>
    );
}

export default Header;