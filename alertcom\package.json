{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@csstools/normalize.css": "^12.1.1", "@googlemaps/markerclustererplus": "^1.2.10", "@react-google-maps/api": "^2.20.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "jwt-decode": "^4.0.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-router-dom": "^7.4.1", "react-scripts": "5.0.1", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "sanitize.css": "^13.0.0", "socket.io-client": "^4.8.1", "sweetalert2": "^11.21.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "description": "React frontend for AlertComm1 dashboard", "author": "ttornstrom", "license": "ISC", "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}