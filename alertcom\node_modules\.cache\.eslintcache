[{"C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Login.js": "4", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Events.js": "5", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\EventForm.js": "6", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Home.js": "7", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Templates.js": "8", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Dashboard.js": "9", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\MapPopup.js": "10", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\config.js": "11", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\context\\UseTypeContext.js": "12", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Settings.js": "13", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\DashboardHome.js": "14", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Reporting.js": "15", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\UserManagement.js": "16", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\themes.js": "17", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Footer.js": "18", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\LaunchTemplate.js": "19", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\HelpMeLaunch.js": "20", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\signup.js": "21", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\ResetVerification.js": "22", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\ResetPassword.js": "23", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\components\\StatCard.js": "24", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\context\\FontSizeContext.js": "25", "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\ChecklistStatus.js": "26"}, {"size": 448, "mtime": 1745828366482, "results": "27", "hashOfConfig": "28"}, {"size": 20250, "mtime": 1752475004000, "results": "29", "hashOfConfig": "28"}, {"size": 375, "mtime": 1745828366484, "results": "30", "hashOfConfig": "28"}, {"size": 3386, "mtime": 1752383673549, "results": "31", "hashOfConfig": "28"}, {"size": 22634, "mtime": 1752383673533, "results": "32", "hashOfConfig": "28"}, {"size": 23645, "mtime": 1752398620486, "results": "33", "hashOfConfig": "28"}, {"size": 2311, "mtime": 1751453584021, "results": "34", "hashOfConfig": "28"}, {"size": 65380, "mtime": 1752383673574, "results": "35", "hashOfConfig": "28"}, {"size": 155416, "mtime": 1752484996894, "results": "36", "hashOfConfig": "28"}, {"size": 29136, "mtime": 1752383673557, "results": "37", "hashOfConfig": "28"}, {"size": 101, "mtime": 1745828366480, "results": "38", "hashOfConfig": "28"}, {"size": 16787, "mtime": 1752427793681, "results": "39", "hashOfConfig": "28"}, {"size": 82001, "mtime": 1752484571998, "results": "40", "hashOfConfig": "28"}, {"size": 8279, "mtime": 1751453584009, "results": "41", "hashOfConfig": "28"}, {"size": 9525, "mtime": 1752383673561, "results": "42", "hashOfConfig": "28"}, {"size": 36449, "mtime": 1751453584046, "results": "43", "hashOfConfig": "28"}, {"size": 1889, "mtime": 1745828366487, "results": "44", "hashOfConfig": "28"}, {"size": 8788, "mtime": 1752403908250, "results": "45", "hashOfConfig": "28"}, {"size": 72925, "mtime": 1752398564741, "results": "46", "hashOfConfig": "28"}, {"size": 54136, "mtime": 1752383673540, "results": "47", "hashOfConfig": "28"}, {"size": 17671, "mtime": 1752383673591, "results": "48", "hashOfConfig": "28"}, {"size": 5753, "mtime": 1752383673566, "results": "49", "hashOfConfig": "28"}, {"size": 3376, "mtime": 1752383673563, "results": "50", "hashOfConfig": "28"}, {"size": 1566, "mtime": 1752403324929, "results": "51", "hashOfConfig": "28"}, {"size": 1744, "mtime": 1752403396021, "results": "52", "hashOfConfig": "28"}, {"size": 10785, "mtime": 1752472344170, "results": "53", "hashOfConfig": "28"}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "d9p4br", {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\App.js", ["132", "133"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Events.js", ["134", "135"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\EventForm.js", ["136", "137"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Home.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Templates.js", ["138", "139", "140"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Dashboard.js", ["141", "142", "143", "144", "145", "146", "147", "148"], ["149", "150", "151", "152", "153", "154", "155"], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\MapPopup.js", ["156", "157", "158"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\config.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\context\\UseTypeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Settings.js", ["159", "160", "161", "162", "163", "164", "165", "166", "167", "168", "169"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\DashboardHome.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Reporting.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\UserManagement.js", ["170"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\themes.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\LaunchTemplate.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\HelpMeLaunch.js", ["171"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\signup.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\ResetVerification.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\ResetPassword.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\components\\StatCard.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\context\\FontSizeContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\Alertcome\\alertcom\\src\\ChecklistStatus.js", ["172"], [], {"ruleId": "173", "severity": 1, "message": "174", "line": 1, "column": 38, "nodeType": "175", "messageId": "176", "endLine": 1, "endColumn": 49}, {"ruleId": "177", "severity": 1, "message": "178", "line": 563, "column": 6, "nodeType": "179", "endLine": 563, "endColumn": 13, "suggestions": "180"}, {"ruleId": "173", "severity": 1, "message": "181", "line": 16, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 16, "endColumn": 17}, {"ruleId": "177", "severity": 1, "message": "182", "line": 71, "column": 6, "nodeType": "179", "endLine": 71, "endColumn": 23, "suggestions": "183"}, {"ruleId": "173", "severity": 1, "message": "184", "line": 3, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 3, "endColumn": 15}, {"ruleId": "177", "severity": 1, "message": "185", "line": 140, "column": 6, "nodeType": "179", "endLine": 140, "endColumn": 13, "suggestions": "186"}, {"ruleId": "173", "severity": 1, "message": "187", "line": 54, "column": 21, "nodeType": "175", "messageId": "176", "endLine": 54, "endColumn": 33}, {"ruleId": "173", "severity": 1, "message": "188", "line": 56, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 56, "endColumn": 17}, {"ruleId": "177", "severity": 1, "message": "189", "line": 101, "column": 6, "nodeType": "179", "endLine": 101, "endColumn": 13, "suggestions": "190"}, {"ruleId": "173", "severity": 1, "message": "191", "line": 6, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 6, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "192", "line": 12, "column": 8, "nodeType": "175", "messageId": "176", "endLine": 12, "endColumn": 16}, {"ruleId": "173", "severity": 1, "message": "193", "line": 259, "column": 11, "nodeType": "175", "messageId": "176", "endLine": 259, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "194", "line": 951, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 951, "endColumn": 21}, {"ruleId": "173", "severity": 1, "message": "195", "line": 951, "column": 23, "nodeType": "175", "messageId": "176", "endLine": 951, "endColumn": 37}, {"ruleId": "173", "severity": 1, "message": "196", "line": 1032, "column": 19, "nodeType": "175", "messageId": "176", "endLine": 1032, "endColumn": 38}, {"ruleId": "173", "severity": 1, "message": "188", "line": 1420, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 1420, "endColumn": 17}, {"ruleId": "173", "severity": 1, "message": "197", "line": 1742, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 1742, "endColumn": 31}, {"ruleId": "198", "severity": 2, "message": "199", "line": 1477, "column": 27, "nodeType": "175", "endLine": 1477, "endColumn": 38, "suppressions": "200"}, {"ruleId": "198", "severity": 2, "message": "201", "line": 1510, "column": 3, "nodeType": "175", "endLine": 1510, "endColumn": 12, "suppressions": "202"}, {"ruleId": "198", "severity": 2, "message": "201", "line": 1660, "column": 3, "nodeType": "175", "endLine": 1660, "endColumn": 12, "suppressions": "203"}, {"ruleId": "198", "severity": 2, "message": "199", "line": 1742, "column": 34, "nodeType": "175", "endLine": 1742, "endColumn": 45, "suppressions": "204"}, {"ruleId": "198", "severity": 2, "message": "199", "line": 1765, "column": 24, "nodeType": "175", "endLine": 1765, "endColumn": 35, "suppressions": "205"}, {"ruleId": "198", "severity": 2, "message": "199", "line": 1804, "column": 28, "nodeType": "175", "endLine": 1804, "endColumn": 39, "suppressions": "206"}, {"ruleId": "198", "severity": 2, "message": "201", "line": 1970, "column": 3, "nodeType": "175", "endLine": 1970, "endColumn": 12, "suppressions": "207"}, {"ruleId": "173", "severity": 1, "message": "208", "line": 63, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 63, "endColumn": 22}, {"ruleId": "173", "severity": 1, "message": "209", "line": 63, "column": 24, "nodeType": "175", "messageId": "176", "endLine": 63, "endColumn": 39}, {"ruleId": "177", "severity": 1, "message": "210", "line": 256, "column": 6, "nodeType": "179", "endLine": 256, "endColumn": 32, "suggestions": "211"}, {"ruleId": "173", "severity": 1, "message": "212", "line": 73, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 73, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "213", "line": 73, "column": 21, "nodeType": "175", "messageId": "176", "endLine": 73, "endColumn": 33}, {"ruleId": "173", "severity": 1, "message": "214", "line": 74, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 74, "endColumn": 20}, {"ruleId": "173", "severity": 1, "message": "215", "line": 75, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 75, "endColumn": 23}, {"ruleId": "173", "severity": 1, "message": "216", "line": 75, "column": 25, "nodeType": "175", "messageId": "176", "endLine": 75, "endColumn": 41}, {"ruleId": "173", "severity": 1, "message": "217", "line": 87, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 87, "endColumn": 21}, {"ruleId": "173", "severity": 1, "message": "218", "line": 87, "column": 23, "nodeType": "175", "messageId": "176", "endLine": 87, "endColumn": 37}, {"ruleId": "173", "severity": 1, "message": "219", "line": 92, "column": 10, "nodeType": "175", "messageId": "176", "endLine": 92, "endColumn": 19}, {"ruleId": "173", "severity": 1, "message": "220", "line": 92, "column": 21, "nodeType": "175", "messageId": "176", "endLine": 92, "endColumn": 33}, {"ruleId": "173", "severity": 1, "message": "221", "line": 290, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 290, "endColumn": 29}, {"ruleId": "173", "severity": 1, "message": "222", "line": 555, "column": 15, "nodeType": "175", "messageId": "176", "endLine": 555, "endColumn": 19}, {"ruleId": "177", "severity": 1, "message": "223", "line": 41, "column": 6, "nodeType": "179", "endLine": 41, "endColumn": 12, "suggestions": "224"}, {"ruleId": "177", "severity": 1, "message": "225", "line": 52, "column": 6, "nodeType": "179", "endLine": 52, "endColumn": 24, "suggestions": "226"}, {"ruleId": "177", "severity": 1, "message": "227", "line": 21, "column": 6, "nodeType": "179", "endLine": 21, "endColumn": 22, "suggestions": "228"}, "no-unused-vars", "'useCallback' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'onLogout'. Either include it or remove the dependency array. If 'onLogout' changes too often, find the parent component that defines it and wrap that definition in useCallback.", "ArrayExpression", ["229"], "'location' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'baseUrl'. Either include it or remove the dependency array.", ["230"], "'toast' is defined but never used.", "React Hook useEffect has a missing dependency: 'contextLocations'. Either include it or remove the dependency array.", ["231"], "'setMapCenter' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'baseUrl'. Either include it or remove the dependency array.", ["232"], "'jwtDecode' is defined but never used.", "'StatCard' is defined but never used.", "'allowedTypes' is assigned a value but never used.", "'lastUpdated' is assigned a value but never used.", "'setLastUpdated' is assigned a value but never used.", "'matchedLocationData' is assigned a value but never used.", "'getResponderStatusData' is assigned a value but never used.", "react-hooks/rules-of-hooks", "React Hook \"useCallback\" is called conditionally. React Hooks must be called in the exact same order in every component render.", ["233"], "React Hook \"useEffect\" is called conditionally. React Hooks must be called in the exact same order in every component render.", ["234"], ["235"], ["236"], ["237"], ["238"], ["239"], "'showMapModal' is assigned a value but never used.", "'setShowMapModal' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchData' and 'geocode'. Either include them or remove the dependency array.", ["240"], "'newModule' is assigned a value but never used.", "'setNewModule' is assigned a value but never used.", "'moduleInfo' is assigned a value but never used.", "'showAddModule' is assigned a value but never used.", "'setShowAddModule' is assigned a value but never used.", "'companyCode' is assigned a value but never used.", "'setCompanyCode' is assigned a value but never used.", "'companyId' is assigned a value but never used.", "'setCompanyId' is assigned a value but never used.", "'handleFontSizeChange' is assigned a value but never used.", "'data' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchCommonLocations' and 'fetchUsers'. Either include them or remove the dependency array.", ["241"], "React Hook React.useEffect has a missing dependency: 'setFormData'. Either include it or remove the dependency array. If 'setFormData' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["242"], "React Hook useEffect has missing dependencies: 'loadActionItems' and 'loadUsers'. Either include them or remove the dependency array.", ["243"], {"desc": "244", "fix": "245"}, {"desc": "246", "fix": "247"}, {"desc": "248", "fix": "249"}, {"desc": "250", "fix": "251"}, {"kind": "252", "justification": "253"}, {"kind": "252", "justification": "253"}, {"kind": "252", "justification": "253"}, {"kind": "252", "justification": "253"}, {"kind": "252", "justification": "253"}, {"kind": "252", "justification": "253"}, {"kind": "252", "justification": "253"}, {"desc": "254", "fix": "255"}, {"desc": "256", "fix": "257"}, {"desc": "258", "fix": "259"}, {"desc": "260", "fix": "261"}, "Update the dependencies array to be: [onLogout, token]", {"range": "262", "text": "263"}, "Update the dependencies array to be: [token, baseUrl, onLogout]", {"range": "264", "text": "265"}, "Update the dependencies array to be: [contextLocations, token]", {"range": "266", "text": "267"}, "Update the dependencies array to be: [baseUrl, token]", {"range": "268", "text": "269"}, "directive", "", "Update the dependencies array to be: [token, eventId, isLoaded, fetchData, geocode]", {"range": "270", "text": "271"}, "Update the dependencies array to be: [fetchCommonLocations, fetchUsers, role]", {"range": "272", "text": "273"}, "Update the dependencies array to be: [role, responders, setFormData]", {"range": "274", "text": "275"}, "Update the dependencies array to be: [eventId, loadActionItems, loadUsers, token]", {"range": "276", "text": "277"}, [18690, 18697], "[on<PERSON><PERSON><PERSON>, token]", [2684, 2701], "[token, baseUrl, onLogout]", [5334, 5341], "[contextLocations, token]", [3922, 3929], "[baseUrl, token]", [7996, 8022], "[token, eventId, isLoaded, fetchData, geocode]", [1328, 1334], "[fetchCommonLocations, fetchUsers, role]", [1610, 1628], "[role, responders, setFormData]", [634, 650], "[eventId, loadActionItems, loadUsers, token]"]