import React, { useState, useEffect } from "react";
import "./styles.css";
import config from './config';
const { baseUrl } = config;

function Reporting({ token, onLogout }) {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch(
          `${baseUrl}/reporting-stats`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        if (!response.ok) {
          // Handle 403 Forbidden (Invalid token) by logging out
          if (response.status === 403) {
            console.error("Invalid token detected, logging out user");
            if (onLogout) {
              onLogout();
            }
            return;
          }
          const errorText = await response.text();
          throw new Error(
            `Failed to fetch stats: ${response.status} - ${errorText}`
          );
        }
        const data = await response.json();
        console.log("Fetched reporting stats:", data);
        setStats(data);
        setLoading(false);
      } catch (err) {
        console.error("Error fetching stats:", err);
        setError(err.message);
        setLoading(false);
      }
    };

    if (token) fetchStats();
  }, [token, onLogout]);

  if (loading)
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        Loading reporting data...
      </div>
    );
  if (error)
    return (
      <div style={{ padding: "20px", color: "#d32f2f", textAlign: "center" }}>
        Error: {error}
      </div>
    );
  if (!stats)
    return (
      <div style={{ padding: "20px", textAlign: "center" }}>
        No reporting data available.
      </div>
    );

  return (
    <div
      className="container"
      style={{
        padding: "20px",
        fontFamily: "Arial, sans-serif",
        backgroundColor: "#f5f5f5",
        minHeight: "100vh",
      }}
    >
      <h2
        style={{
          color: "#1a73e8",
          marginBottom: "30px",
          fontSize: "2em",
          textAlign: "center",
        }}
      >
        Reporting Dashboard
      </h2>

      {/* Overview Section */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "20px",
          marginBottom: "40px",
        }}
      >
        <StatCard
          title="Total Events"
          value={stats.totalEvents}
          color="#1a73e8"
        />
        <StatCard
          title="Active Events"
          value={stats.activeEvents}
          color="#388e3c"
        />
        <StatCard
          title="Resolved Events"
          value={stats.resolvedEvents}
          color="#d32f2f"
        />
        <StatCard
          title="Notified Responders"
          value={stats.notifiedResponders}
          color="#1a73e8"
        />
      </div>

      {/* Response Metrics */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
          gap: "20px",
          marginBottom: "40px",
        }}
      >
        <StatCard
          title="Response Rate"
          value={`${stats.responsePercentage}%`}
          color="#388e3c"
        />
        <StatCard
          title="Avg. Acknowledge Time"
          value={
            typeof stats.avgAcknowledgeTime === "number"
              ? `${stats.avgAcknowledgeTime} min`
              : stats.avgAcknowledgeTime
          }
          color="#1a73e8"
        />
        <StatCard
          title="Avg. Response Time"
          value={
            typeof stats.avgResponseTime === "number"
              ? `${stats.avgResponseTime} min`
              : stats.avgResponseTime
          }
          color="#d32f2f"
        />
        <StatCard
          title="Assigned Locations"
          value={stats.assignedLocations}
          color="#388e3c"
        />
      </div>

      {/* Additional Reports */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
          gap: "20px",
        }}
      >
        {/* Top Responders */}
        <div
          style={{
            backgroundColor: "#fff",
            padding: "20px",
            borderRadius: "8px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          }}
        >
          <h3 style={{ color: "#333", marginBottom: "15px" }}>
            Top 5 Responders by Response Time
          </h3>
          {stats.topResponders && stats.topResponders.length > 0 ? (
            <ul style={{ listStyle: "none", padding: 0 }}>
              {stats.topResponders.map((responder, index) => (
                <li
                  key={index}
                  style={{
                    padding: "10px 0",
                    borderBottom: "1px solid #eee",
                    color: "#333",
                  }}
                >
                  {responder.username}: {responder.avgResponseTime} min
                </li>
              ))}
            </ul>
          ) : (
            <p style={{ color: "#666" }}>No data available.</p>
          )}
        </div>

        {/* Events by Day */}
        <div
          style={{
            backgroundColor: "#fff",
            padding: "20px",
            borderRadius: "8px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          }}
        >
          <h3 style={{ color: "#333", marginBottom: "15px" }}>
            Events Last 7 Days
          </h3>
          {stats.eventsByDay && stats.eventsByDay.length > 0 ? (
            <ul style={{ listStyle: "none", padding: 0 }}>
              {stats.eventsByDay.map((day, index) => (
                <li
                  key={index}
                  style={{
                    padding: "10px 0",
                    borderBottom: "1px solid #eee",
                    color: "#333",
                  }}
                >
                  {day.date}: {day.count} events
                </li>
              ))}
            </ul>
          ) : (
            <p style={{ color: "#666" }}>No events in the last 7 days.</p>
          )}
        </div>

        {/* Responder Status Breakdown */}
        <div
          style={{
            backgroundColor: "#fff",
            padding: "20px",
            borderRadius: "8px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          }}
        >
          <h3 style={{ color: "#333", marginBottom: "15px" }}>
            Responder Status Breakdown
          </h3>
          {stats.responderStatus &&
          Object.keys(stats.responderStatus).length > 0 ? (
            <ul style={{ listStyle: "none", padding: 0 }}>
              {Object.entries(stats.responderStatus).map(([status, count]) => (
                <li
                  key={status}
                  style={{
                    padding: "10px 0",
                    borderBottom: "1px solid #eee",
                    color: "#333",
                  }}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}: {count}
                </li>
              ))}
            </ul>
          ) : (
            <p style={{ color: "#666" }}>No status data available.</p>
          )}
        </div>

        {/* Event Scale Distribution */}
        <div
          style={{
            backgroundColor: "#fff",
            padding: "20px",
            borderRadius: "8px",
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
          }}
        >
          <h3 style={{ color: "#333", marginBottom: "15px" }}>
            Events by Scale
          </h3>
          {stats.eventsByScale &&
          Object.keys(stats.eventsByScale).length > 0 ? (
            <ul style={{ listStyle: "none", padding: 0 }}>
              {Object.entries(stats.eventsByScale).map(([scale, count]) => (
                <li
                  key={scale}
                  style={{
                    padding: "10px 0",
                    borderBottom: "1px solid #eee",
                    color: "#333",
                  }}
                >
                  Scale {scale}: {count}
                </li>
              ))}
            </ul>
          ) : (
            <p style={{ color: "#666" }}>No scale data available.</p>
          )}
        </div>
      </div>
    </div>
  );
}

// Reusable StatCard component
function StatCard({ title, value, color }) {
  return (
    <div
      style={{
        padding: "20px",
        backgroundColor: "#fff",
        borderRadius: "8px",
        boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
        textAlign: "center",
        transition: "transform 0.2s",
        cursor: "default",
      }}
      onMouseEnter={(e) => (e.currentTarget.style.transform = "scale(1.05)")}
      onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
    >
      <h4 style={{ margin: "0 0 10px 0", color: "#333", fontSize: "1.1em" }}>
        {title}
      </h4>
      <p style={{ fontSize: "1.8em", margin: 0, color, fontWeight: "bold" }}>
        {value}
      </p>
    </div>
  );
}

export default Reporting;
