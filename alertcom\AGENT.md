# AGENT.md - AlertComm Frontend Project

## Commands
- **Start dev server**: `npm start` (uses legacy OpenSSL provider)
- **Build**: `npm run build`
- **Test**: `npm test` (interactive watch mode)
- **Test single file**: `npm test -- --testNamePattern="filename"`
- **Lint**: Uses ESLint via react-app config (built into react-scripts)

## Architecture
- **Frontend**: React 18 SPA with Create React App
- **Routing**: React Router DOM v7
- **State**: Context API (UseTypeContext for module/form state)
- **Real-time**: Socket.IO client connecting to backend
- **Maps**: Google Maps API integration (@react-google-maps/api)
- **Auth**: JWT tokens with role-based access (commander/responder roles)
- **Config**: Environment-based config via REACT_APP_BASE_URL

## Code Style
- **Imports**: React first, then libraries, then local files (./config, ./components)
- **Components**: Functional components with hooks, PascalCase naming
- **State**: useState/useEffect hooks, destructured context consumers
- **Props**: Passed as named props, role-based conditional rendering
- **Styling**: CSS files + inline styles, themes.js for theming
- **Notifications**: react-toastify for user feedback, SweetAlert2 for confirmations
- **Error handling**: Try-catch blocks with toast notifications for user feedback
