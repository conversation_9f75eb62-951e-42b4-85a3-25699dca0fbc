-- Add new fields to events table for notification-only events and enhanced features
-- Run this migration to add the missing fields

-- Add event_type column (response or notification_only)
ALTER TABLE events ADD COLUMN IF NOT EXISTS event_type VARCHAR(20) DEFAULT 'response';

-- Add location_update_interval column (in seconds)
ALTER TABLE events ADD COLUMN IF NOT EXISTS location_update_interval INTEGER DEFAULT 60;

-- Add notification_channels column (JSON array of notification methods)
ALTER TABLE events ADD COLUMN IF NOT EXISTS notification_channels JSONB DEFAULT '["web_app"]'::jsonb;

-- Add event_documents column (JSON array of uploaded documents)
ALTER TABLE events ADD COLUMN IF NOT EXISTS event_documents JSONB DEFAULT '[]'::jsonb;

-- Add comments to document the new fields
COMMENT ON COLUMN events.event_type IS 'Type of event: response (traditional) or notification_only (NOE)';
COMMENT ON COLUMN events.location_update_interval IS 'Frequency of location updates in seconds (5, 10, 15, 30, 60, 300, 600)';
COMMENT ON COLUMN events.notification_channels IS 'Array of notification methods: voice_call, sms, web_app, email';
COMMENT ON COLUMN events.event_documents IS 'Array of uploaded documents for AI analysis and responder reference';
