import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { jwtDecode } from "jwt-decode";

function MyActions({ eventInfo, token, baseUrl }) {
  const [actions, setActions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [completedActions, setCompletedActions] = useState(new Set());

  useEffect(() => {
    if (eventInfo && token) {
      loadExistingActions();
    }
  }, [eventInfo, token]);

  const loadExistingActions = async () => {
    try {
      // First try to load existing actions from database
      const response = await fetch(`${baseUrl}/events/${eventInfo.id}/actions`, {
        headers: { 'Authorization': `Bearer ${token}` }
      });

      if (response.ok) {
        const existingActions = await response.json();
        if (existingActions.length > 0) {
          // Load existing actions and their completion status
          setActions(existingActions.map(item => item.action_text));
          const completed = new Set();
          existingActions.forEach((item, index) => {
            if (item.is_completed) {
              completed.add(index);
            }
          });
          setCompletedActions(completed);
          return;
        }
      }

      // If no existing actions, generate new ones
      generateActions();
    } catch (error) {
      console.error('Error loading existing actions:', error);
      generateActions();
    }
  };

  const generateActions = async () => {
    setLoading(true);
    try {
      const decoded = jwtDecode(token);
      const userRole = decoded.role || decoded.job_role || "responder";

      // Try AI API first if documents are available
      if (eventInfo.event_documents && eventInfo.event_documents.length > 0) {
        try {
          const response = await fetch(`${baseUrl}/ai/generate-actions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`,
            },
            body: JSON.stringify({
              eventId: eventInfo.id,
              userRole: userRole,
              eventType: eventInfo.event_type,
              eventTitle: eventInfo.title,
              eventInfo: eventInfo.info,
              documents: eventInfo.event_documents,
            }),
          });

          if (response.ok) {
            const data = await response.json();
            const generatedActions = data.actions || [];
            setActions(generatedActions);
            await saveActionsToDatabase(generatedActions, userRole);
            return;
          }
        } catch (apiError) {
          console.log("AI API not available, using default actions");
        }
      }

      // Fallback to default role-based actions
      const defaultActions = getDefaultActions(userRole, eventInfo.event_type, eventInfo.event_documents);
      setActions(defaultActions);
      await saveActionsToDatabase(defaultActions, userRole);

    } catch (error) {
      console.error("Error generating actions:", error);

      // Final fallback
      const decoded = jwtDecode(token);
      const userRole = decoded.role || decoded.job_role || "responder";
      const fallbackActions = getDefaultActions(userRole, eventInfo.event_type, []);
      setActions(fallbackActions);
      await saveActionsToDatabase(fallbackActions, userRole);
    } finally {
      setLoading(false);
    }
  };

  const saveActionsToDatabase = async (actionsToSave, userRole) => {
    try {
      await fetch(`${baseUrl}/events/${eventInfo.id}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          actions: actionsToSave,
          userRole: userRole
        })
      });
    } catch (error) {
      console.error('Error saving actions to database:', error);
    }
  };

  const getDefaultActions = (role, eventType, documents = []) => {
    const baseActions = [
      "Review event details and location information",
      "Check in with your team or supervisor",
      "Confirm your participation status",
    ];

    const responseActions = [
      "Check equipment and supplies",
      "Review route to event location",
      "Confirm transportation arrangements",
      "Report arrival at designated location",
    ];

    const notificationActions = [
      "Acknowledge receipt of notification",
      "Stay available for updates",
      "Monitor communication channels",
      "Review event information thoroughly",
    ];

    const roleSpecificActions = {
      commander: [
        "Review overall response strategy",
        "Coordinate with other commanders",
        "Monitor resource allocation",
        "Prepare briefing materials",
        "Establish communication protocols",
        "Review incident action plan",
      ],
      lead: [
        "Brief team members on their roles",
        "Coordinate with incident command",
        "Monitor team safety protocols",
        "Prepare status reports",
        "Assign specific tasks to team members",
        "Maintain team accountability",
      ],
      staff: [
        "Follow supervisor instructions",
        "Report any issues or concerns",
        "Maintain situational awareness",
        "Be ready to assist as directed",
        "Complete assigned tasks promptly",
        "Stay in communication with team lead",
      ],
    };

    let actions = [...baseActions];
    
    if (eventType === "response") {
      actions = [...actions, ...responseActions];
    } else {
      actions = [...actions, ...notificationActions];
    }

    if (roleSpecificActions[role]) {
      actions = [...actions, ...roleSpecificActions[role]];
    }

    // Add document-specific actions if documents are present
    if (documents && documents.length > 0) {
      actions.push("Review uploaded event documents");
      actions.push("Follow procedures outlined in event documentation");
    }

    return actions;
  };

  const toggleAction = async (index) => {
    const newCompleted = new Set(completedActions);
    const isCompleted = !newCompleted.has(index);

    if (newCompleted.has(index)) {
      newCompleted.delete(index);
    } else {
      newCompleted.add(index);
    }

    setCompletedActions(newCompleted);

    // Save to database
    try {
      await fetch(`${baseUrl}/events/${eventInfo.id}/actions/${index}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          isCompleted: isCompleted
        })
      });
    } catch (error) {
      console.error('Error updating action completion:', error);
      // Revert the change if database update fails
      const revertCompleted = new Set(completedActions);
      if (isCompleted) {
        revertCompleted.delete(index);
      } else {
        revertCompleted.add(index);
      }
      setCompletedActions(revertCompleted);
      toast.error('Failed to save action completion status');
    }
  };

  if (!eventInfo || actions.length === 0) {
    return null;
  }

  return (
    <div
      style={{
        backgroundColor: "#fff",
        border: "1px solid #ddd",
        borderRadius: "8px",
        padding: "20px",
        marginBottom: "20px",
        boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
      }}
    >
      <div
        style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: "15px",
        }}
      >
        <h3
          style={{
            color: "#1a73e8",
            margin: 0,
            fontSize: "var(--font-size-large)",
            fontWeight: "bold",
          }}
        >
          My Actions
        </h3>
        {loading && (
          <div style={{ fontSize: "var(--font-size-small)", color: "#666" }}>
            Generating actions...
          </div>
        )}
        {!loading && (
          <div style={{ fontSize: "var(--font-size-small)", color: "#666" }}>
            {completedActions.size} of {actions.length} completed
          </div>
        )}
      </div>

      <div style={{ fontSize: "var(--font-size-small)", color: "#666", marginBottom: "15px" }}>
        {eventInfo.event_type === "notification_only" 
          ? "AI-generated checklist for this notification event:"
          : "AI-generated action items based on your role and event documents:"}
      </div>

      <div style={{ display: "flex", flexDirection: "column", gap: "8px" }}>
        {actions.map((action, index) => (
          <label
            key={index}
            style={{
              display: "flex",
              alignItems: "flex-start",
              gap: "8px",
              padding: "8px",
              backgroundColor: completedActions.has(index) ? "#d4edda" : "#f8f9fa",
              borderRadius: "4px",
              cursor: "pointer",
              transition: "background-color 0.2s ease",
            }}
          >
            <input
              type="checkbox"
              checked={completedActions.has(index)}
              onChange={() => toggleAction(index)}
              style={{
                marginTop: "2px",
                transform: "scale(1.1)",
              }}
            />
            <span
              style={{
                textDecoration: completedActions.has(index) ? "line-through" : "none",
                color: completedActions.has(index) ? "#6c757d" : "#212529",
                fontSize: "var(--font-size-base)",
                flex: 1,
                lineHeight: "1.4",
              }}
            >
              {action}
            </span>
          </label>
        ))}
      </div>

      {eventInfo.event_documents && eventInfo.event_documents.length > 0 && (
        <div style={{ marginTop: "15px", paddingTop: "15px", borderTop: "1px solid #e9ecef" }}>
          <h4 style={{ marginBottom: "8px", fontSize: "var(--font-size-small)", color: "#666" }}>
            Event Documents:
          </h4>
          <div style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}>
            {eventInfo.event_documents.map((doc, index) => (
              <span
                key={index}
                style={{
                  fontSize: "var(--font-size-xs)",
                  color: "#495057",
                  backgroundColor: "#e9ecef",
                  padding: "4px 8px",
                  borderRadius: "4px",
                }}
              >
                {doc.name}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default MyActions;
