// src/Settings.js
import React, { useState, useEffect } from 'react';
import { useUseType } from './context/UseTypeContext';
import { themes } from './themes';
function Settings({ role, token }) {
const { activeModules, selectedModule, updateSelectedModule, formConfig, updateFormConfig, locations, updateLocations } = useUseType();
const [newField, setNewField] = useState({ name: '', label: '', type: 'text', required: false, options: [] });
const [newLocation, setNewLocation] = useState({ address: '', commonName: '' });
if (role !== 'commander') {
return <div>Access Denied</div>;
}
const handleModuleChange = (e) => {
updateSelectedModule(e.target.value);
};
const handleAddField = () => {
const updatedConfig = [...formConfig, newField];
updateFormConfig(updatedConfig);
setNewField({ name: '', label: '', type: 'text', required: false, options: [] });
};
const handleRemoveField = (index) => {
const updatedConfig = formConfig.filter((_, i) => i !== index);
updateFormConfig(updatedConfig);
};
const handleFieldChange = (index, key, value) => {
const updatedConfig = [...formConfig];
updatedConfig[index][key] = value;
updateFormConfig(updatedConfig);
};
const handleAddLocation = () => {
const updatedLocations = [...locations, newLocation];
updateLocations(updatedLocations);
setNewLocation({ address: '', commonName: '' });
};
const handleRemoveLocation = (index) => {
const updatedLocations = locations.filter((_, i) => i !== index);
updateLocations(updatedLocations);
};
return (
<div style={{ padding: '20px' }}>
<h1>Settings</h1>
{/* Module Selection */}
<section style={{ marginBottom: '40px' }}>
<h2>Select Active Module</h2>
<select value={selectedModule} onChange={handleModuleChange}>
{activeModules.map(module => (
<option key={module} value={module}>{module}</option>
))}
</select>
</section>
{/* Customize Launch Form */}
<section style={{ marginBottom: '40px' }}>
<h2>Customize Launch Form for {selectedModule}</h2>
<div style={{ marginBottom: '20px' }}>
<h3>Current Fields</h3>
{formConfig.length > 0 ? (
<ul>
{formConfig.map((field, index) => (
<li key={index} style={{ marginBottom: '10px' }}>
<input
type="text"
value={field.label}
onChange={(e) => handleFieldChange(index, 'label', e.target.value)}
placeholder="Field Label"
style={{ marginRight: '10px' }}
/>
<select
value={field.type}
onChange={(e) => handleFieldChange(index, 'type', e.target.value)}
style={{ marginRight: '10px' }}
>
<option value="text">Text</option>
<option value="textarea">Textarea</option>
<option value="select">Select</option>
<option value="checkbox">Checkbox</option>
<option value="autocomplete">Autocomplete</option>
</select>
{field.type === 'select' && (
<input
type="text"
value={(field.options || []).join(',')}
//value={field.options.join(',')}
onChange={(e) => handleFieldChange(index, 'options', e.target.value.split(',').map(opt => opt.trim()))}
placeholder="Options (comma-separated)"
style={{ marginRight: '10px' }}
/>
)}
<label>
Required:
<input
type="checkbox"
checked={field.required}
onChange={(e) => handleFieldChange(index, 'required', e.target.checked)}
style={{ marginRight: '10px' }}
/>
</label>
<button onClick={() => handleRemoveField(index)}>Remove</button>
</li>
))}
</ul>
) : (
<p>No custom fields defined.</p>
)}
</div>
<div>
<h3>Add New Field</h3>
<input
type="text"
value={newField.name}
onChange={(e) => setNewField({ ...newField, name: e.target.value })}
placeholder="Field Name (e.g., custom_field_1)"
style={{ marginRight: '10px' }}
/>
<input
type="text"
value={newField.label}
onChange={(e) => setNewField({ ...newField, label: e.target.value })}
placeholder="Field Label"
style={{ marginRight: '10px' }}
/>
<select
value={newField.type}
onChange={(e) => setNewField({ ...newField, type: e.target.value })}
style={{ marginRight: '10px' }}
>
<option value="text">Text</option>
<option value="textarea">Textarea</option>
<option value="select">Select</option>
<option value="checkbox">Checkbox</option>
<option value="autocomplete">Autocomplete</option>
</select>
{newField.type === 'select' && (
<input
type="text"
value={newField.options.join(',')}
onChange={(e) => setNewField({ ...newField, options: e.target.value.split(',').map(opt => opt.trim()) })}
placeholder="Options (comma-separated)"
style={{ marginRight: '10px' }}
/>
)}
<label>
Required:
<input
type="checkbox"
checked={newField.required}
onChange={(e) => setNewField({ ...newField, required: e.target.checked })}
style={{ marginRight: '10px' }}
/>
</label>
<button onClick={handleAddField}>Add Field</button>
</div>
</section>
{/* Manage Main Locations */}
<section>
<h2>Manage Main Locations</h2>
<div style={{ marginBottom: '20px' }}>
<h3>Current Locations</h3>
{locations.length > 0 ? (
<ul>
{locations.map((location, index) => (
<li key={index} style={{ marginBottom: '10px' }}>
<span>{location.commonName} - {location.address}</span>
<button onClick={() => handleRemoveLocation(index)} style={{ marginLeft: '10px' }}>Remove</button>
</li>
))}
</ul>
) : (
<p>No locations defined.</p>
)}
</div>
<div>
<h3>Add New Location</h3>
<input
type="text"
value={newLocation.commonName}
onChange={(e) => setNewLocation({ ...newLocation, commonName: e.target.value })}
placeholder="Common Name (e.g., Main Hospital)"
style={{ marginRight: '10px' }}
/>
<input
type="text"
value={newLocation.address}
onChange={(e) => setNewLocation({ ...newLocation, address: e.target.value })}
placeholder="Address"
style={{ marginRight: '10px' }}
/>
<button onClick={handleAddLocation}>Add Location</button>
</div>
</section>
</div>
);
}
export default Settings;