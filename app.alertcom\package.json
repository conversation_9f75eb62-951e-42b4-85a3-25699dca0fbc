{"name": "alertcomm1-backend", "version": "1.0.0", "description": "Backend server for AlertComm1 Emergency Response Communication System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["emergency", "response", "communication", "alerts", "first-responders"], "author": "AlertComm1 Team", "license": "ISC", "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.1", "bcrypt": "^6.0.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.6.0", "nodemailer": "^7.0.3", "openai": "^5.8.2", "pg": "^8.16.1", "socket.io": "^4.7.2", "twilio": "^5.7.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}