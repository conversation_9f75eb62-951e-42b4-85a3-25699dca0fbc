// src/ResetVerification.js
import React, { useState, useEffect } from "react";
import { useNavigate, useSearchParams, useLocation, useParams } from "react-router-dom";
import Footer from "./Footer";
import "./styles.css";
import config from './config';
const { baseUrl } = config;

function ResetVerification({ token }) {
  
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [message, setMessage] = useState(null);
  const [error, setError] = useState(null);
  const [invalidToken, setInvalidToken] = useState(false);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const { pathname } = location;
  const params = useParams();
  
  // Get token from either query parameter or URL path parameter
  let resetToken = searchParams.get('token');
  
  // If token is not in query params, check if it's in the URL path
  if (!resetToken && pathname.includes('/reset-password/')) {
    resetToken = params.token;
  }

  console.log("Reset token:", resetToken);
  
  // Validate token on component mount - only show error if token is missing
  // but don't redirect - the API will handle invalid tokens
  useEffect(() => {
    
    // Only set invalid token if there's no token at all
    if (!resetToken) {
      setError("Invalid or missing reset token");
      setInvalidToken(true);
    }
  }, [resetToken]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Don't proceed if token is invalid
    if (invalidToken) {
      return;
    }
    
    // Validate passwords match
    if (newPassword !== confirmPassword) {
      setError("Passwords do not match");
      setTimeout(() => setError(null), 3000);
      return;
    }

    try {
      const response = await fetch(`${baseUrl}/reset-password`, {
        method: "POST",
        headers: { 
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        credentials: 'include', // Include cookies if your API uses session-based auth
        body: JSON.stringify({
          token: resetToken,
          newPassword
        }),
      });
      
      console.log('Reset password response:', response);
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || data.message || "Password reset failed");
      }
      
      setMessage(data.message || "Password reset successful");
      setTimeout(() => {
        navigate("/login");
      }, 2000);
    } catch (err) {
      setError(err.message);
      setTimeout(() => setError(null), 3000);
    }
  };

  return (
    <div
      style={{ display: "flex", flexDirection: "column", minHeight: "100vh" }}
    >
      <div style={{ flex: "1" }}>
        <div className="container">
          <div className="card">
            <h1>Reset Your Password</h1>
            {error && (
              <div
                style={{
                  backgroundColor: "var(--danger)",
                  color: "#fff",
                  padding: "10px",
                  borderRadius: "5px",
                  marginBottom: "10px",
                }}
              >
                {error}
              </div>
            )}
            {message && (
              <div
                style={{
                  backgroundColor: "var(--success, #28a745)",
                  color: "#fff",
                  padding: "10px",
                  borderRadius: "5px",
                  marginBottom: "10px",
                }}
              >
                {message}
              </div>
            )}
            <form onSubmit={handleSubmit} style={{ display: invalidToken ? 'none' : 'block' }}>
              {invalidToken ? (
                <div style={{
                  backgroundColor: "var(--danger)",
                  color: "#fff",
                  padding: "10px",
                  borderRadius: "5px",
                  marginBottom: "20px",
                }}>
                  The reset link is invalid or has expired. Please request a new password reset.
                </div>
              ) : null}
              <label>
                New Password:
                <input
                  type="password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  placeholder="Enter new password"
                  className="form-input"
                  required
                  disabled={invalidToken}
                />
              </label>
              <label>
                Confirm Password:
                <input
                  type="password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder="Confirm new password"
                  className="form-input"
                  required
                  disabled={invalidToken}
                />
              </label>
              <button type="submit" className="btn-primary" disabled={invalidToken}>
                Reset Password
              </button>
              <div style={{ textAlign: 'center', marginTop: '15px' }}>
                <a href="/login" style={{ color: '#4f46e5', fontWeight: 'bold', textDecoration: 'underline' }}>
                  Back to Login
                </a>
              </div>
            </form>
          </div>
        </div>
      </div>
      <Footer token={token} />
    </div>
  );
}

export default ResetVerification;
