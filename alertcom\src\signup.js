import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './styles.css';
import Footer from './Footer';

const baseUrl = process.env.REACT_APP_BASE_URL;

const Signup = ({ setToken, token }) => {
  const [formData, setFormData] = useState({
    email: "",
    username: "",
    password: "",
    confirmPassword: "",
    role: "staff", // Fixed role as staff
    first_name: "",
    last_name: "",
    countryCode: "+1",
    phoneNumber: "",
    job_role: "",
    main_location: "",
    company_code: "",
    home_address: "",
    city: "",
    state: "",
    zip: "",
  });
  const [commonLocations, setCommonLocations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [verificationCode, setVerificationCode] = useState("");
  const navigate = useNavigate();

  // Set default locations
  useEffect(() => {
    setCommonLocations([
      { id: 1, name: 'New York' },
      { id: 2, name: 'London' },
      { id: 3, name: 'Tokyo' },
      { id: 4, name: 'Remote' },
    ]);
    setIsLoading(false);
  }, []);

  // Error display function
  const displayError = (message) => {
    toast.error(message, { position: 'top-right', autoClose: 3000 });
  };


  const validateForm = () => {
    const errors = {};
    const requiredFields = {
      email: 'Email is required',
      username: 'Username is required',
      password: 'Password is required',
      confirmPassword: 'Confirm Password is required',
      phoneNumber: 'Phone number is required',
      main_location: 'Main location is required',
      company_code: 'Company code is required'
    };

    Object.keys(requiredFields).forEach((field) => {
      if (!formData[field]) errors[field] = requiredFields[field];
    });

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Invalid email format';
    }
    if (formData.phoneNumber && !/^\d{10}$/.test(formData.phoneNumber)) {
      errors.phoneNumber = 'Phone number must be 10 digits';
    }
    if (formData.password && formData.confirmPassword && formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    return errors;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSignupSubmit = async (e) => {
    e.preventDefault();
    setFormSubmitting(true);

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      Object.values(validationErrors).forEach((error) => {
        displayError(error);
      });
      setFormSubmitting(false);
      return;
    }

    // Prepare data for API
    const signupData = {
      username: formData.username,
      password: formData.password,
      role: formData.role,
      email: formData.email,
      main_location: formData.main_location,
      phone: `${formData.countryCode}${formData.phoneNumber}`.replace(/\s+/g, ''),
      first_name: formData.first_name,
      last_name: formData.last_name,
      job_role: formData.job_role,
      home_address: formData.home_address,
      city: formData.city,
      state: formData.state,
      zip: formData.zip,
      company_code: formData.company_code,
    };

    try {
      const response = await fetch(`${baseUrl}/user-store`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(signupData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || 'Failed to sign up');
      }

      // Reset form but keep username for verification
      setFormData((prev) => ({
        ...prev,
        email: '',
        password: '',
        confirmPassword: '',
        countryCode: '+1',
        phoneNumber: '',
        main_location: '',
        first_name: '',
        last_name: '',
        job_role: '',
        home_address: '',
        city: '',
        state: '',
        zip: '',
        company_code: '',
      }));
      
      // Show verification UI
      setShowVerification(true);
      toast.success('Signup successful! Please verify your phone number.', { position: 'top-right', autoClose: 3000 });
    } catch (err) {
      console.error('Signup error:', err);
      displayError(err.message || 'An error occurred during signup');
    } finally {
      setFormSubmitting(false);
    }
  };

  const handleVerificationSubmit = async (e) => {
    e.preventDefault();

    // Minimal check to prevent empty OTP submission
    if (!verificationCode.trim()) {
      toast.error('Please enter a verification code', { position: 'top-right', autoClose: 3000 });
      return;
    }

    // Verify username is not empty
    if (!formData.username) {
      console.error('Username is empty during verification');
      toast.error('Username is missing. Please sign up again.', { position: 'top-right', autoClose: 3000 });
      setShowVerification(false);
      return;
    }

    const verificationData = {
      username: formData.username,
      otp: verificationCode,
    };

    try {
      const response = await fetch(`${baseUrl}/verify-otp`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(verificationData),
      });
      const data = await response.json();
      if (!response.ok) throw new Error(data.error || 'Verification failed');

      localStorage.setItem('token', data.token);
      setToken(data.token);
      toast.success('Verification successful!', { position: 'top-right', autoClose: 3000 });

      setShowVerification(false);
      setVerificationCode('');
      navigate('/dashboard');
    } catch (err) {
      console.error('Verification error:', err);
      toast.error(err.message, { position: 'top-right', autoClose: 3000 });
    }
  };

  return (
    <>
      <div className="signup-container">
        <div className="form-container" style={{ display: showVerification ? 'none' : 'block' }}>
          <h2 className="signup-title">Create Your Account</h2>
          <p className="signup-subtitle">Fill in the details below to get started with AlertComm</p>
          
          {isLoading && (
            <div className="loading-indicator">
              <p>Loading form data...</p>
            </div>
          )}
          
          <form onSubmit={handleSignupSubmit} className="signup-form">
            <div className="field-group">
              <div className="form-field">
                <label>Email <span className="required">*</span></label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="input-field"
                  placeholder="<EMAIL>"
                  disabled={formSubmitting}
                />
              </div>
              <div className="form-field">
                <label>Username <span className="required">*</span></label>
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  className="input-field"
                  placeholder="Choose a username"
                  disabled={formSubmitting}
                />
              </div>
            </div>
            
            <div className="field-group">
              <div className="form-field">
                <label>Password <span className="required">*</span></label>
                <input
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="input-field"
                  placeholder="Create a secure password"
                  disabled={formSubmitting}
                />
              </div>
              <div className="form-field">
                <label>Confirm Password <span className="required">*</span></label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                  className="input-field"
                  placeholder="Confirm your password"
                  disabled={formSubmitting}
                />
              </div>
            </div>
            
            <div className="field-group">
              <div className="form-field">
                <label>First Name</label>
                <input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Your first name"
                  disabled={formSubmitting}
                />
              </div>
              <div className="form-field">
                <label>Last Name</label>
                <input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Your last name"
                  disabled={formSubmitting}
                />
              </div>
            </div>

            <div className="field-group">
              <div className="form-field">
                <label>Job Role</label>
                <input
                  type="text"
                  name="job_role"
                  value={formData.job_role}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Your position"
                  disabled={formSubmitting}
                />
              </div>
              <div className="form-field">
                <label>Home Address</label>
                <input
                  type="text"
                  name="home_address"
                  value={formData.home_address}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Street address"
                  disabled={formSubmitting}
                />
              </div>
            </div>

            <div className="field-group">
              <div className="form-field">
                <label>Phone Number <span className="required">*</span></label>
                <div className="phone-input-wrapper">
                  <select
                    name="countryCode"
                    value={formData.countryCode}
                    onChange={handleInputChange}
                    className="country-code-select"
                    disabled={formSubmitting}
                  >
                    <option value="+1">+1 (US)</option>
                    <option value="+44">+44 (UK)</option>
                    <option value="+91">+91 (IN)</option>
                    <option value="+61">+61 (AU)</option>
                    <option value="+86">+86 (CN)</option>
                  </select>
                  <input
                    type="tel"
                    name="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={handleInputChange}
                    placeholder="10-digit number"
                    required
                    className="phone-input"
                    disabled={formSubmitting}
                  />
                </div>
              </div>
              <div className="form-field">
                <label>Main Location <span className="required">*</span></label>
                <select
                  name="main_location"
                  value={formData.main_location}
                  onChange={handleInputChange}
                  required
                  className="input-field"
                  disabled={formSubmitting || isLoading}
                >
                  <option value="">Select a location</option>
                  {commonLocations.map((location) => (
                    <option key={location.id} value={location.name}>
                      {location.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            
            <div className="field-group">
              <div className="form-field">
                <label>Company Code <span className="required">*</span></label>
                <input
                  type="text"
                  name="company_code"
                  value={formData.company_code}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Company identifier code"
                  required
                  disabled={formSubmitting}
                />
              </div>
              <div className="form-field">
                <label>Home Address</label>
                <input
                  type="text"
                  name="home_address"
                  value={formData.home_address}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Street address"
                  disabled={formSubmitting}
                />
              </div>
            </div>

            <div className="field-group">
              <div className="form-field">
                <label>City</label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="City"
                  disabled={formSubmitting}
                />
              </div>
              <div className="form-field">
                <label>State</label>
                <input
                  type="text"
                  name="state"
                  value={formData.state}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="State/Province"
                  disabled={formSubmitting}
                />
              </div>
            </div>

            <div className="field-group">
              <div className="form-field">
                <label>ZIP Code</label>
                <input
                  type="text"
                  name="zip"
                  value={formData.zip}
                  onChange={handleInputChange}
                  className="input-field"
                  placeholder="Postal code"
                  disabled={formSubmitting}
                />
              </div>
            </div>

            <div className="button-container">
              <button type="submit" className="submit-button" disabled={formSubmitting || isLoading}>
                {formSubmitting ? 'Creating Account...' : 'Create Account'}
              </button>
              <Link to="/login" className="login-link">
                Already have an account? Login
              </Link>
            </div>
          </form>
        </div>

        <div className="form-container verification-container" style={{ display: showVerification ? 'block' : 'none' }}>
          <div className="verification-header">
            <h2>Verify Your Phone Number</h2>
            <p className="verification-subtitle">We've sent a verification code to your phone number.</p>
          </div>
          
          <form onSubmit={handleVerificationSubmit} className="verification-form">
            <div className="verification-code-container">
              <label className="verification-label">Enter Verification Code</label>
              <input
                type="text"
                placeholder="Enter 6-digit code"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                className="input-field verification-input"
                maxLength="6"
              />
            </div>
            
            {/* <div className="verification-info">
              <p>Didn't receive the code? <span className="resend-link">Resend Code</span></p>
              <p className="verification-timer">Code expires in: 10:00</p>
            </div> */}
            
            <div className="verification-buttons">
              <button type="submit" className="submit-button verification-button">Verify & Continue</button>
            </div>
          </form>
        </div>

      </div>
      <ToastContainer />
      <Footer token={token} />
    </>
  );
};

export default Signup;