{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\Alertcome\\\\alertcom\\\\src\\\\Settings.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\nimport { useUseType } from \"./context/UseTypeContext\";\nimport { useFontSize } from \"./context/FontSizeContext\";\nimport { FaPlus } from \"react-icons/fa\";\nimport { GiCycle } from \"react-icons/gi\";\nimport { toast } from \"react-toastify\";\nimport Swal from \"sweetalert2\";\n\n// Cookie utility function to invalidate company settings cache\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst deleteCookie = name => {\n  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;\n};\nconst COMPANY_SETTINGS_COOKIE = 'alertcomm_company_settings';\nfunction Settings({\n  role,\n  token,\n  userId,\n  baseUrl\n}) {\n  _s();\n  var _aiSettings$ai_prompt, _aiSettings$ai_precon, _aiSettings$ai_precon2;\n  // console.log(role, token, userId);\n\n  const {\n    activeModules,\n    selectedModule,\n    updateSelectedModule,\n    formConfig,\n    updateFormConfig,\n    locations,\n    updateLocations\n  } = useUseType();\n\n  // Ensure formConfig always includes default fields - wrapped in useMemo to avoid dependency issues\n  const defaultFields = useMemo(() => [{\n    name: \"title\",\n    label: \"Title\",\n    type: \"text\",\n    required: true\n  }, {\n    name: \"info\",\n    label: \"Info\",\n    type: \"textarea\",\n    required: false\n  }, {\n    name: \"description\",\n    label: \"Description\",\n    type: \"textarea\",\n    required: false\n  }, {\n    name: \"scale\",\n    label: \"Scale\",\n    type: \"select\",\n    options: [\"Small\", \"Medium\", \"Large\"],\n    required: true\n  }, {\n    name: \"urgency\",\n    label: \"Urgency\",\n    type: \"select\",\n    options: [\"Low\", \"Medium\", \"High\", \"Immediate\"],\n    required: true\n  }], []);\n\n  // Use useMemo to properly handle the form config for each module\n  const allFormConfig = useMemo(() => {\n    console.log('Selected Module:', selectedModule);\n    console.log('Form Config for module:', formConfig);\n\n    // The formConfig from context already contains the complete config for the selected module\n    // including default fields, so we don't need to combine them again\n    const moduleConfig = formConfig || defaultFields;\n    console.log('Module Form Config:', moduleConfig);\n    return moduleConfig;\n  }, [selectedModule, formConfig, defaultFields]);\n  const [newField, setNewField] = useState({\n    name: \"\",\n    label: \"\",\n    type: \"text\",\n    required: false,\n    options: []\n  });\n  const [newLocation, setNewLocation] = useState({\n    commonName: \"\",\n    address: \"\",\n    city: \"\",\n    state: \"\",\n    zip: \"\"\n  });\n\n  // AI Settings state\n  const [aiSettings, setAiSettings] = useState({\n    ai_prompts: {},\n    ai_preconditions: {}\n  });\n  const [loadingAiSettings, setLoadingAiSettings] = useState(false);\n  const [isAiSettingsExpanded, setIsAiSettingsExpanded] = useState(true);\n  const [newModule, setNewModule] = useState(\"\");\n  const [moduleInfo, setModuleInfo] = useState({});\n  const [showAddModule, setShowAddModule] = useState(false);\n  // Use the fontSizeScale from the FontSizeContext\n  const {\n    fontSizeScale,\n    setFontSizeScale\n  } = useFontSize();\n  const [editingLocation, setEditingLocation] = useState(null);\n  const [editLocation, setEditLocation] = useState({\n    commonName: \"\",\n    address: \"\",\n    city: \"\",\n    state: \"\",\n    zip: \"\"\n  });\n  const [newCode, setNewCode] = useState(\"\");\n  const [companyCode, setCompanyCode] = useState([]);\n  // State to store the list of saved company codes\n  const [savedCodes, setSavedCodes] = useState([]);\n  const [name, setName] = useState(\"\"); // State for company name\n  const [description, setDescription] = useState(\"\"); // State for company description\n  const [companyId, setCompanyId] = useState(null); // State for company ID\n\n  // Optimized fetchModuleInfo - only calls API when necessary\n  const fetchModuleInfo = useCallback(async (forceRefresh = false) => {\n    try {\n      console.log('fetchModuleInfo called, forceRefresh:', forceRefresh);\n\n      // Check localStorage first (only if not forcing refresh)\n      if (!forceRefresh) {\n        const localModuleInfo = localStorage.getItem('moduleInfo');\n        if (localModuleInfo) {\n          setModuleInfo(JSON.parse(localModuleInfo));\n          console.log('Using cached moduleInfo from localStorage');\n          return; // Exit early if we have cached data\n        }\n      }\n\n      // Only fetch from backend if no cached data or forcing refresh\n      console.log('Fetching moduleInfo from API...');\n      const response = await fetch(`${baseUrl}/company-settings`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setModuleInfo(data.moduleInfo || {});\n        localStorage.setItem('moduleInfo', JSON.stringify(data.moduleInfo || {}));\n        console.log('ModuleInfo fetched and cached successfully');\n\n        // Note: We don't update locations here because UseTypeContext handles location loading\n        // The Settings page only manages user-added locations, not the global location state\n      }\n    } catch (err) {\n      console.error(\"Error fetching module info:\", err);\n    }\n  }, [baseUrl, token, setModuleInfo]);\n\n  // Add a function to fetch saved company codes\n  const fetchSavedCodes = useCallback(async () => {\n    try {\n      console.log('Fetching saved company codes...');\n      const response = await fetch(`${baseUrl}/user/company?user_id=${userId}`, {\n        headers: {\n          Authorization: `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const codes = await response.json();\n        console.log('Fetched company codes:', codes);\n        setSavedCodes(codes || []);\n      } else {\n        console.log('Failed to fetch company codes:', response.status);\n      }\n    } catch (err) {\n      console.error('Error fetching company codes:', err);\n    }\n  }, [baseUrl, token, userId]);\n\n  // useEffect(() => {\n  //   if (userId) {\n  //     // Only fetch company codes and settings once on mount\n  //     fetchSavedCodes(); // This also sets company ID\n  //     // fetchModuleInfo and fetchCompanySettings are handled by UseTypeContext\n  //   }\n  // }, [userId, fetchSavedCodes]);\n\n  // Only fetch data once on mount when userId is available\n  useEffect(() => {\n    if (userId) {\n      fetchModuleInfo(false); // Changed to false to avoid unnecessary API calls\n      fetchSavedCodes();\n    }\n  }, [userId, fetchModuleInfo, fetchSavedCodes]); // Removed locations from dependencies to prevent infinite loop\n\n  // Apply font size scaling to the document root\n  useEffect(() => {\n    document.documentElement.style.setProperty('--font-size-scale', fontSizeScale);\n    localStorage.setItem('fontSizeScale', fontSizeScale);\n  }, [fontSizeScale]);\n\n  // AI Settings functions\n  const loadAiSettings = useCallback(async () => {\n    setLoadingAiSettings(true);\n    try {\n      const response = await fetch(`${baseUrl}/ai-settings`, {\n        headers: {\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (response.ok) {\n        const data = await response.json();\n        setAiSettings(data);\n        console.log('AI settings loaded successfully:', data);\n      } else if (response.status === 403) {\n        console.log('Access denied to AI settings - user is not Super Admin');\n        // Don't show error toast for access denied, just log it\n      } else {\n        console.error('Failed to load AI settings, status:', response.status);\n        // Only show error for actual failures, not access denied\n        if (response.status !== 403) {\n          toast.error('Failed to load AI settings');\n        }\n      }\n    } catch (error) {\n      console.error('Error loading AI settings:', error);\n      // Only show error toast if it's not a network/access issue\n      if (error.message !== 'Failed to fetch') {\n        toast.error('Failed to load AI settings');\n      }\n    } finally {\n      setLoadingAiSettings(false);\n    }\n  }, [baseUrl, token]);\n\n  // Load AI settings on component mount\n  useEffect(() => {\n    if (userId === 'ttornstrom') {\n      loadAiSettings();\n    }\n  }, [userId, loadAiSettings]);\n  const saveAiSettings = async () => {\n    setLoadingAiSettings(true);\n    try {\n      const response = await fetch(`${baseUrl}/ai-settings`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n        body: JSON.stringify(aiSettings)\n      });\n      if (response.ok) {\n        toast.success('AI settings saved successfully!');\n      } else {\n        throw new Error('Failed to save AI settings');\n      }\n    } catch (error) {\n      console.error('Error saving AI settings:', error);\n      toast.error('Failed to save AI settings');\n    } finally {\n      setLoadingAiSettings(false);\n    }\n  };\n  const updateAiPrompt = (promptType, value) => {\n    setAiSettings(prev => ({\n      ...prev,\n      ai_prompts: {\n        ...prev.ai_prompts,\n        [promptType]: value\n      }\n    }));\n  };\n  const updateAiPrecondition = (preconditionType, value) => {\n    setAiSettings(prev => ({\n      ...prev,\n      ai_preconditions: {\n        ...prev.ai_preconditions,\n        [preconditionType]: value\n      }\n    }));\n  };\n  if (role !== \"commander\") {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        height: \"100vh\",\n        fontSize: \"24px\",\n        color: \"#d32f2f\",\n        fontFamily: \"-apple-system, BlinkMacSystemFont, sans-serif\"\n      },\n      children: \"Access Denied\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this);\n  }\n  const handleModuleChange = e => {\n    const newModule = e.target.value;\n    updateSelectedModule(newModule);\n  };\n\n  // fetchModuleInfo is already defined above using useCallback\n\n  const handleFontSizeChange = change => {\n    setFontSizeScale(prevScale => {\n      const newScale = Math.max(0.8, Math.min(1.4, prevScale + change));\n      return parseFloat(newScale.toFixed(1));\n    });\n  };\n  const handleAddField = () => {\n    if (!newField.name || !newField.label) {\n      toast.error('Please fill in both Name and Label fields', {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n      return;\n    }\n\n    // Check if field name already exists\n    if (allFormConfig.some(field => field.name === newField.name)) {\n      toast.error('Field name already exists', {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n      return;\n    }\n    const updatedConfig = [...allFormConfig, {\n      ...newField\n    }];\n    updateFormConfig(updatedConfig);\n    setNewField({\n      name: \"\",\n      label: \"\",\n      type: \"text\",\n      required: false,\n      options: []\n    });\n    toast.success('Field added successfully!', {\n      position: \"top-right\",\n      autoClose: 3000\n    });\n  };\n  const handleRemoveField = index => {\n    const field = allFormConfig[index];\n    // Don't allow removing default fields\n    if (defaultFields.some(defaultField => defaultField.name === field.name)) {\n      toast.error('Cannot remove default fields (Title, Info, Description, Scale, Urgency)', {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n      return;\n    }\n\n    // Remove the field from the config\n    const updatedConfig = allFormConfig.filter((_, i) => i !== index);\n    updateFormConfig(updatedConfig);\n    toast.success('Field removed successfully!', {\n      position: \"top-right\",\n      autoClose: 3000\n    });\n  };\n  const handleFieldChange = (index, key, value) => {\n    // Create a copy of the current form config and update the specific field\n    const updatedConfig = [...allFormConfig];\n    updatedConfig[index] = {\n      ...updatedConfig[index],\n      [key]: value\n    };\n\n    // Update the form config for the current module\n    updateFormConfig(updatedConfig);\n  };\n\n  // Add a function to save the form configuration to the backend\n  const handleSaveFormConfig = async () => {\n    try {\n      console.log('Saving form config for module:', selectedModule);\n      console.log('Form config data:', allFormConfig);\n      const response = await fetch(`${baseUrl}/company-settings/form-config`, {\n        method: \"POST\",\n        headers: {\n          Authorization: `Bearer ${token}`,\n          \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n          module: selectedModule,\n          formConfig: allFormConfig\n        })\n      });\n      if (response.ok) {\n        toast.success('Form configuration saved successfully!', {\n          position: \"top-right\",\n          autoClose: 3000\n        });\n\n        // Form config is handled by UseTypeContext, no need to refresh moduleInfo\n      } else {\n        const errorData = await response.json();\n        console.error('Save failed:', errorData);\n        throw new Error(errorData.error || 'Failed to save form configuration');\n      }\n    } catch (err) {\n      console.error(\"Error saving form config:\", err);\n      toast.error('Failed to save form configuration: ' + err.message, {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n    }\n  };\n  const handleRemoveLocation = async index => {\n    const location = locations[index];\n    const confirmDelete = window.confirm(`Are you sure you want to delete \"${location.commonName}\"?\\n\\nAddress: ${location.address}\\nCity: ${location.city}, ${location.state} ${location.zip}`);\n    if (confirmDelete) {\n      const updatedLocations = locations.filter((_, i) => i !== index);\n      updateLocations(updatedLocations);\n\n      // Refresh module info after deleting location\n      await fetchModuleInfo(true);\n      console.log('Location deleted successfully');\n    }\n  };\n  const handleEditLocation = index => {\n    setEditingLocation(index);\n    setEditLocation({\n      ...locations[index]\n    });\n    // Clear the add form when editing\n    setNewLocation({\n      commonName: \"\",\n      address: \"\",\n      city: \"\",\n      state: \"\",\n      zip: \"\"\n    });\n  };\n  const handleSaveEdit = async () => {\n    if (!editLocation.commonName || !editLocation.address) {\n      alert('Please fill in both Common Name and Address fields');\n      return;\n    }\n    const updatedLocations = [...locations];\n    updatedLocations[editingLocation] = {\n      ...editLocation\n    };\n    updateLocations(updatedLocations);\n    setEditingLocation(null);\n    setEditLocation({\n      commonName: \"\",\n      address: \"\",\n      city: \"\",\n      state: \"\",\n      zip: \"\"\n    });\n\n    // Refresh module info after editing location\n    await fetchModuleInfo(true);\n    console.log('Location updated successfully');\n  };\n  const handleCancelEdit = () => {\n    setEditingLocation(null);\n    setEditLocation({\n      commonName: \"\",\n      address: \"\",\n      city: \"\",\n      state: \"\",\n      zip: \"\"\n    });\n  };\n  const handleAddLocation = async () => {\n    if (!newLocation.commonName || !newLocation.address) {\n      alert('Please fill in both Common Name and Address fields');\n      return;\n    }\n    console.log('Adding new location:', newLocation);\n    const updatedLocations = [newLocation, ...locations]; // Add to beginning for latest first\n    updateLocations(updatedLocations);\n    setNewLocation({\n      commonName: \"\",\n      address: \"\",\n      city: \"\",\n      state: \"\",\n      zip: \"\"\n    });\n\n    // Refresh module info after adding location\n    await fetchModuleInfo(true);\n    console.log('Location added successfully');\n  };\n  const generateRandomCode = () => {\n    const randomCode = Math.floor(100000 + Math.random() * 900000).toString(); // Generate a 6-digit random code\n    setNewCode(randomCode); // Set the 6-digit random code\n  };\n\n  //validations\n  const validations = () => {\n    // console.log(name, newCode, description);\n\n    // Validate company name\n    if (!name) {\n      toast.error(\"Please enter a valid company name.\", {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n      return false;\n    }\n    if (!newCode || newCode.length < 4) {\n      toast.error(\"Please enter a valid company code (at least 6 characters).\", {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n      return false;\n    }\n\n    // if (!description ) {\n    //   toast.error('Please enter a description .', {\n    //     position: 'top-right',\n    //     autoClose: 3000,\n    //   });\n    //   return false;\n    // }\n\n    if (!newCode.trim()) {\n      toast.error(\"Please enter or generate a valid code before saving.\", {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n      return false;\n    }\n    return true;\n  };\n\n  // Save the company code\n  const saveCode = async () => {\n    if (!validations()) {\n      return;\n    }\n    try {\n      const response = await fetch(`${baseUrl}/user/store-company`, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          Authorization: `Bearer ${token}`\n        },\n        body: JSON.stringify({\n          name,\n          description,\n          code: newCode,\n          user_id: userId\n        })\n      });\n      if (response.ok) {\n        const data = await response.json();\n        // console.log(\"Response from server:\", data);\n\n        setSavedCodes(prevCodes => [...prevCodes, {\n          name,\n          description,\n          code: newCode\n        }]);\n        setNewCode(\"\"); // Clear the input field\n        setName(\"\"); // Clear the name field\n        setDescription(\"\"); // Clear the description field\n\n        // Invalidate company settings cache when new company is saved\n        deleteCookie(COMPANY_SETTINGS_COOKIE);\n        console.log(\"Company settings cache invalidated due to new company save\");\n        toast.success(\"Code saved successfully!\", {\n          position: \"top-right\",\n          autoClose: 3000\n        });\n      } else {\n        const errorData = await response.json();\n        console.error(\"Error response:\", errorData);\n        const errorMessage = errorData.error || errorData.message || \"Failed to save the code. Please try again.\";\n        toast.error(errorMessage, {\n          position: \"top-right\",\n          autoClose: 3000\n        });\n      }\n    } catch (error) {\n      console.error(\"Error saving company data:\", error);\n      toast.error(\"An unexpected error occurred. Please try again.\", {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n    }\n  };\n\n  // fetchCompanyId is already defined above\n\n  //delete company code\n  const handleDeleteCode = async (index, id) => {\n    try {\n      // Display confirmation dialog using SweetAlert2\n      const result = await Swal.fire({\n        title: \"Are you sure?\",\n        text: \"You won't be able to revert this!\",\n        icon: \"warning\",\n        showCancelButton: true,\n        confirmButtonText: \"Yes, delete it!\",\n        cancelButtonText: \"Cancel\",\n        confirmButtonColor: \"#d32f2f\",\n        cancelButtonColor: \"#777\"\n      });\n      if (result.isConfirmed) {\n        const response = await fetch(`${baseUrl}/user/delete-company/${id}?user_id=${userId}`, {\n          method: \"DELETE\",\n          headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: `Bearer ${token}`\n          }\n        });\n        if (response.ok) {\n          setSavedCodes(prevCodes => prevCodes.filter((_, i) => i !== index));\n\n          // Invalidate company settings cache when company is deleted\n          deleteCookie(COMPANY_SETTINGS_COOKIE);\n          console.log(\"Company settings cache invalidated due to company deletion\");\n          toast.success(\"Company code deleted successfully!\", {\n            position: \"top-right\",\n            autoClose: 3000\n          });\n        } else {\n          throw new Error(`Failed to delete: ${response.statusText}`);\n        }\n      }\n    } catch (error) {\n      console.error(\"Error deleting company code:\", error);\n      toast.error(\"An unexpected error occurred. Please try again.\", {\n        position: \"top-right\",\n        autoClose: 3000\n      });\n    }\n  };\n  const handleCopyCode = code => {\n    navigator.clipboard.writeText(code);\n    toast.success(\"Code copied to clipboard!\", {\n      position: \"top-right\",\n      autoClose: 3000\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          /* Update the global font-size-scale variable */\n          :root {\n            --font-size-scale: ${fontSizeScale};\n          }\n          \n          /* Font size controls styling */\n          \n          .font-size-controls {\n            position: fixed;\n            bottom: 20px;\n            right: 20px;\n            display: flex;\n            gap: 10px;\n            background-color: white;\n            padding: 10px;\n            border-radius: 8px;\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n            z-index: 1000;\n          }\n          \n          .font-size-btn {\n            width: 36px;\n            height: 36px;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            border-radius: 50%;\n            border: none;\n            background-color: #f0f0f0;\n            color: #333;\n            font-size: 18px;\n            cursor: pointer;\n            transition: background-color 0.2s;\n          }\n          \n          .font-size-btn:hover {\n            background-color: #e0e0e0;\n          }\n          \n          .font-size-value {\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            font-size: 14px;\n            font-weight: 500;\n            min-width: 40px;\n          }\n\n          .locations-container {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 20px;\n            align-items: start;\n          }\n\n          .locations-list-container,\n          .locations-form-container {\n            height: 400px;\n            display: flex;\n            flex-direction: column;\n          }\n\n          .locations-list {\n            flex: 1;\n            overflow-y: auto;\n            border: 1px solid #ddd;\n            border-radius: 8px;\n            background-color: #fff;\n          }\n\n          .locations-form {\n            height: 100%;\n            border: 1px solid #ddd;\n            border-radius: 8px;\n            padding: 20px;\n            background-color: #fff;\n            display: flex;\n            flex-direction: column;\n          }\n\n          @media (max-width: 768px) {\n            .locations-container {\n              grid-template-columns: 1fr;\n              gap: 20px;\n            }\n\n            .locations-list-container,\n            .locations-form-container {\n              height: auto;\n              min-height: 400px;\n            }\n          }\n\n          @media (max-width: 480px) {\n            .locations-container {\n              gap: 15px;\n            }\n          }\n\n          /* Enhanced Settings Page Styles */\n          .settings-section {\n            background: #fff;\n            border-radius: 12px;\n            border: 1px solid #e0e0e0;\n            margin-bottom: 30px;\n            box-shadow: 0 2px 8px rgba(0,0,0,0.05);\n            overflow: hidden;\n          }\n\n          .settings-section-header {\n            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\n            color: white !important;\n            padding: 20px;\n            margin: 0;\n            font-size: 20px;\n            font-weight: 600;\n          }\n\n          .settings-section-content {\n            padding: 25px;\n          }\n\n          .form-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n            gap: 20px;\n            margin-bottom: 20px;\n          }\n\n          .form-field {\n            display: flex;\n            flex-direction: column;\n          }\n\n          .form-label {\n            font-size: 14px;\n            font-weight: 600;\n            color: #333;\n            margin-bottom: 8px;\n          }\n\n          .form-input {\n            padding: 12px;\n            border: 2px solid #e0e0e0;\n            border-radius: 8px;\n            font-size: 14px;\n            transition: all 0.2s ease;\n          }\n\n          .form-input:focus {\n            outline: none;\n            border-color: #1976d2;\n            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);\n          }\n\n          .btn-primary {\n            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\n            color: white;\n            border: none;\n            padding: 12px 24px;\n            border-radius: 8px;\n            font-size: 14px;\n            font-weight: 600;\n            cursor: pointer;\n            transition: all 0.2s ease;\n            display: inline-flex;\n            align-items: center;\n            gap: 8px;\n          }\n\n          .btn-primary:hover {\n            transform: translateY(-1px);\n            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);\n          }\n\n          .btn-secondary {\n            background: #f5f5f5;\n            color: #666;\n            border: 2px solid #e0e0e0;\n            padding: 10px 20px;\n            border-radius: 8px;\n            font-size: 14px;\n            font-weight: 600;\n            cursor: pointer;\n            transition: all 0.2s ease;\n          }\n\n          .btn-secondary:hover {\n            background: #e0e0e0;\n            border-color: #ccc;\n          }\n\n          .btn-danger {\n            background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);\n            color: white;\n            border: none;\n            padding: 8px 16px;\n            border-radius: 6px;\n            font-size: 12px;\n            font-weight: 600;\n            cursor: pointer;\n            transition: all 0.2s ease;\n          }\n\n          .btn-danger:hover {\n            transform: translateY(-1px);\n            box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);\n          }\n\n          .settings-header {\n            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\n            color: white;\n            padding: 30px 20px;\n            margin: -20px -20px 30px -20px;\n            text-align: center;\n          }\n\n          .settings-title {\n            font-size: 28px;\n            font-weight: 700;\n            margin: 0;\n            text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n          }\n\n          .settings-subtitle {\n            font-size: 16px;\n            opacity: 0.9;\n            margin: 8px 0 0 0;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 662,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card\",\n        style: {\n          height: \"100% !important,\",\n          maxHeight: \"100% \"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"settings-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"settings-title\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"settings-subtitle\",\n            children: \"Configure your AlertComm system preferences\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 902,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 900,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          style: {\n            marginBottom: \"40px\",\n            padding: \"20px\",\n            border: \"1px solid #e0e0e0\",\n            borderRadius: \"8px\",\n            backgroundColor: \"#fafafa\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              fontSize: \"20px\",\n              fontWeight: \"500\",\n              color: \"#333\",\n              marginBottom: \"15px\",\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Manage Modules\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 934,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              style: {\n                display: \"block\",\n                fontSize: \"16px\",\n                fontWeight: \"500\",\n                color: \"#555\",\n                marginBottom: \"8px\"\n              },\n              children: \"Select Active Module\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 950,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: selectedModule,\n              onChange: handleModuleChange,\n              style: {\n                width: \"100%\",\n                padding: \"10px\",\n                fontSize: \"16px\",\n                border: \"1px solid #ccc\",\n                borderRadius: \"6px\",\n                backgroundColor: \"#fff\",\n                outline: \"none\",\n                transition: \"border-color 0.2s\"\n              },\n              onFocus: e => e.target.style.borderColor = \"#1976d2\",\n              onBlur: e => e.target.style.borderColor = \"#ccc\",\n              children: (activeModules && activeModules.length > 0 ? activeModules : [\"EMS\", \"Fire\", \"Police\", \"Medical\"]).map(module => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: module,\n                children: module\n              }, module, false, {\n                fileName: _jsxFileName,\n                lineNumber: 978,\n                columnNumber: 17\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 961,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 949,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 925,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-section-header\",\n            children: [\"Customize Launch Form for \", selectedModule]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 990,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: \"20px\",\n                backgroundColor: \"#fff\",\n                padding: \"15px\",\n                borderRadius: \"8px\",\n                border: \"1px solid #e0e0e0\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: \"18px\",\n                  fontWeight: \"500\",\n                  color: \"#555\",\n                  marginBottom: \"15px\",\n                  display: \"flex\",\n                  justifyContent: \"space-between\",\n                  alignItems: \"center\",\n                  borderBottom: \"1px solid #eee\",\n                  paddingBottom: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Current Fields\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1014,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    fontSize: \"14px\",\n                    color: \"#777\"\n                  },\n                  children: [allFormConfig.length, \" field\", allFormConfig.length !== 1 ? 's' : '']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1015,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1001,\n                columnNumber: 13\n              }, this), allFormConfig.length > 0 ? /*#__PURE__*/_jsxDEV(\"ul\", {\n                style: {\n                  listStyle: \"none\",\n                  padding: 0\n                },\n                children: allFormConfig.map((field, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"12px\",\n                    marginBottom: \"15px\",\n                    flexWrap: \"wrap\",\n                    padding: \"10px\",\n                    backgroundColor: index % 2 === 0 ? \"#f9f9f9\" : \"#fff\",\n                    borderRadius: \"6px\",\n                    border: \"1px solid #eee\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: \"1 1 200px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      style: {\n                        display: \"block\",\n                        fontSize: \"12px\",\n                        fontWeight: \"500\",\n                        color: \"#666\",\n                        marginBottom: \"4px\"\n                      },\n                      children: \"Field Label\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1037,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: field.label,\n                      onChange: e => handleFieldChange(index, \"label\", e.target.value),\n                      placeholder: \"Field Label\",\n                      style: {\n                        width: \"100%\",\n                        padding: \"8px 10px\",\n                        fontSize: \"14px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s, box-shadow 0.2s\"\n                      },\n                      onFocus: e => {\n                        e.target.style.borderColor = \"#1976d2\";\n                        e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\n                      },\n                      onBlur: e => {\n                        e.target.style.borderColor = \"#ccc\";\n                        e.target.style.boxShadow = \"none\";\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1048,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1036,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: \"1 1 150px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      style: {\n                        display: \"block\",\n                        fontSize: \"12px\",\n                        fontWeight: \"500\",\n                        color: \"#666\",\n                        marginBottom: \"4px\"\n                      },\n                      children: \"Field Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1076,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                      value: field.type,\n                      onChange: e => handleFieldChange(index, \"type\", e.target.value),\n                      style: {\n                        width: \"100%\",\n                        padding: \"8px 10px\",\n                        fontSize: \"14px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        backgroundColor: \"#fff\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s, box-shadow 0.2s\"\n                      },\n                      onFocus: e => {\n                        e.target.style.borderColor = \"#1976d2\";\n                        e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\n                      },\n                      onBlur: e => {\n                        e.target.style.borderColor = \"#ccc\";\n                        e.target.style.boxShadow = \"none\";\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"text\",\n                        children: \"Text\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1111,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"textarea\",\n                        children: \"Textarea\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1112,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"select\",\n                        children: \"Select\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1113,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"checkbox\",\n                        children: \"Checkbox\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1114,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                        value: \"autocomplete\",\n                        children: \"Autocomplete\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1115,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1087,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1075,\n                    columnNumber: 21\n                  }, this), field.type === \"select\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: \"1 1 200px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      style: {\n                        display: \"block\",\n                        fontSize: \"12px\",\n                        fontWeight: \"500\",\n                        color: \"#666\",\n                        marginBottom: \"4px\"\n                      },\n                      children: \"Options (comma-separated)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: (field.options || []).join(\",\"),\n                      onChange: e => handleFieldChange(index, \"options\", e.target.value.split(\",\").map(opt => opt.trim())),\n                      placeholder: \"Option1, Option2, Option3\",\n                      style: {\n                        width: \"100%\",\n                        padding: \"8px 10px\",\n                        fontSize: \"14px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s, box-shadow 0.2s\"\n                      },\n                      onFocus: e => {\n                        e.target.style.borderColor = \"#1976d2\";\n                        e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\n                      },\n                      onBlur: e => {\n                        e.target.style.borderColor = \"#ccc\";\n                        e.target.style.boxShadow = \"none\";\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1131,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1119,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: \"10px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      style: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        gap: \"5px\",\n                        fontSize: \"14px\",\n                        color: \"#555\",\n                        whiteSpace: \"nowrap\"\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                        type: \"checkbox\",\n                        checked: field.required,\n                        onChange: e => handleFieldChange(index, \"required\", e.target.checked),\n                        style: {\n                          width: \"16px\",\n                          height: \"16px\",\n                          cursor: \"pointer\",\n                          accentColor: \"#1976d2\"\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1173,\n                        columnNumber: 25\n                      }, this), \"Required\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1163,\n                      columnNumber: 23\n                    }, this), defaultFields.some(defaultField => defaultField.name === field.name) ? /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: \"4px 8px\",\n                        fontSize: \"12px\",\n                        color: \"#666\",\n                        backgroundColor: \"#f5f5f5\",\n                        border: \"1px solid #ddd\",\n                        borderRadius: \"4px\",\n                        fontStyle: \"italic\"\n                      },\n                      children: \"Default Field\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1190,\n                      columnNumber: 25\n                    }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleRemoveField(index),\n                      style: {\n                        padding: \"4px 10px\",\n                        fontSize: \"13px\",\n                        color: \"#fff\",\n                        backgroundColor: \"#d32f2f\",\n                        border: \"none\",\n                        borderRadius: \"4px\",\n                        cursor: \"pointer\",\n                        transition: \"background-color 0.2s\"\n                      },\n                      onMouseOver: e => e.target.style.backgroundColor = \"#b71c1c\",\n                      onMouseOut: e => e.target.style.backgroundColor = \"#d32f2f\",\n                      children: \"Remove\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1204,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1162,\n                    columnNumber: 21\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1022,\n                  columnNumber: 19\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1020,\n                columnNumber: 15\n              }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: \"#777\",\n                  fontSize: \"16px\"\n                },\n                children: \"No custom fields defined.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1231,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 994,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: \"25px\",\n                backgroundColor: \"#fff\",\n                padding: \"15px\",\n                borderRadius: \"8px\",\n                border: \"1px solid #e0e0e0\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: \"18px\",\n                  fontWeight: \"500\",\n                  color: \"#555\",\n                  marginBottom: \"15px\",\n                  borderBottom: \"1px solid #eee\",\n                  paddingBottom: \"10px\"\n                },\n                children: \"Add New Field\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1243,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  flexWrap: \"wrap\",\n                  gap: \"15px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: \"1 1 200px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: \"block\",\n                      fontSize: \"12px\",\n                      fontWeight: \"500\",\n                      color: \"#666\",\n                      marginBottom: \"4px\"\n                    },\n                    children: \"Field Name\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1257,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: newField.name,\n                    onChange: e => setNewField({\n                      ...newField,\n                      name: e.target.value\n                    }),\n                    placeholder: \"Field Name (e.g., custom_field_1)\",\n                    style: {\n                      width: \"100%\",\n                      padding: \"8px 10px\",\n                      fontSize: \"14px\",\n                      border: \"1px solid #ccc\",\n                      borderRadius: \"6px\",\n                      outline: \"none\",\n                      transition: \"border-color 0.2s, box-shadow 0.2s\"\n                    },\n                    onFocus: e => {\n                      e.target.style.borderColor = \"#1976d2\";\n                      e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\n                    },\n                    onBlur: e => {\n                      e.target.style.borderColor = \"#ccc\";\n                      e.target.style.boxShadow = \"none\";\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1268,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1256,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: \"1 1 200px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: \"block\",\n                      fontSize: \"12px\",\n                      fontWeight: \"500\",\n                      color: \"#666\",\n                      marginBottom: \"4px\"\n                    },\n                    children: \"Field Label\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1296,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: newField.label,\n                    onChange: e => setNewField({\n                      ...newField,\n                      label: e.target.value\n                    }),\n                    placeholder: \"Field Label\",\n                    style: {\n                      width: \"100%\",\n                      padding: \"8px 10px\",\n                      fontSize: \"14px\",\n                      border: \"1px solid #ccc\",\n                      borderRadius: \"6px\",\n                      outline: \"none\",\n                      transition: \"border-color 0.2s, box-shadow 0.2s\"\n                    },\n                    onFocus: e => {\n                      e.target.style.borderColor = \"#1976d2\";\n                      e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\n                    },\n                    onBlur: e => {\n                      e.target.style.borderColor = \"#ccc\";\n                      e.target.style.boxShadow = \"none\";\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1307,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1295,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: \"1 1 150px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: \"block\",\n                      fontSize: \"12px\",\n                      fontWeight: \"500\",\n                      color: \"#666\",\n                      marginBottom: \"4px\"\n                    },\n                    children: \"Field Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1335,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: newField.type,\n                    onChange: e => setNewField({\n                      ...newField,\n                      type: e.target.value\n                    }),\n                    style: {\n                      width: \"100%\",\n                      padding: \"8px 10px\",\n                      fontSize: \"14px\",\n                      border: \"1px solid #ccc\",\n                      borderRadius: \"6px\",\n                      backgroundColor: \"#fff\",\n                      outline: \"none\",\n                      transition: \"border-color 0.2s, box-shadow 0.2s\"\n                    },\n                    onFocus: e => {\n                      e.target.style.borderColor = \"#1976d2\";\n                      e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\n                    },\n                    onBlur: e => {\n                      e.target.style.borderColor = \"#ccc\";\n                      e.target.style.boxShadow = \"none\";\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"text\",\n                      children: \"Text\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1370,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"textarea\",\n                      children: \"Textarea\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1371,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"select\",\n                      children: \"Select\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1372,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"checkbox\",\n                      children: \"Checkbox\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1373,\n                      columnNumber: 17\n                    }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"autocomplete\",\n                      children: \"Autocomplete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1374,\n                      columnNumber: 17\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1346,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1334,\n                  columnNumber: 15\n                }, this), newField.type === \"select\" && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: \"1 1 200px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: \"block\",\n                      fontSize: \"12px\",\n                      fontWeight: \"500\",\n                      color: \"#666\",\n                      marginBottom: \"4px\"\n                    },\n                    children: \"Options (comma-separated)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1380,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: newField.options.join(\",\"),\n                    onChange: e => setNewField({\n                      ...newField,\n                      options: e.target.value.split(\",\").map(opt => opt.trim())\n                    }),\n                    placeholder: \"Option1, Option2, Option3\",\n                    style: {\n                      width: \"100%\",\n                      padding: \"8px 10px\",\n                      fontSize: \"14px\",\n                      border: \"1px solid #ccc\",\n                      borderRadius: \"6px\",\n                      outline: \"none\",\n                      transition: \"border-color 0.2s, box-shadow 0.2s\"\n                    },\n                    onFocus: e => {\n                      e.target.style.borderColor = \"#1976d2\";\n                      e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\n                    },\n                    onBlur: e => {\n                      e.target.style.borderColor = \"#ccc\";\n                      e.target.style.boxShadow = \"none\";\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1391,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1379,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    gap: \"10px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    style: {\n                      display: \"flex\",\n                      alignItems: \"center\",\n                      gap: \"5px\",\n                      fontSize: \"14px\",\n                      color: \"#555\",\n                      whiteSpace: \"nowrap\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      checked: newField.required,\n                      onChange: e => setNewField({\n                        ...newField,\n                        required: e.target.checked\n                      }),\n                      style: {\n                        width: \"16px\",\n                        height: \"16px\",\n                        cursor: \"pointer\",\n                        accentColor: \"#1976d2\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1435,\n                      columnNumber: 19\n                    }, this), \"Required\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1425,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: handleAddField,\n                    className: \"btn-primary\",\n                    style: {\n                      marginLeft: \"auto\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(FaPlus, {\n                      size: 12\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1456,\n                      columnNumber: 19\n                    }, this), \"Add Field\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1451,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1424,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1255,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: \"20px\",\n                  paddingTop: \"20px\",\n                  borderTop: \"1px solid #e0e0e0\",\n                  display: \"flex\",\n                  justifyContent: \"center\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleSaveFormConfig,\n                  className: \"btn-primary\",\n                  style: {\n                    padding: \"12px 30px\",\n                    fontSize: \"16px\",\n                    fontWeight: \"600\"\n                  },\n                  children: \"\\uD83D\\uDCBE Save Form Configuration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1470,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1463,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1236,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 993,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 989,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-section-header\",\n            children: \"Manage Main Locations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1487,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section-content\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"locations-container\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"locations-list-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    fontSize: \"18px\",\n                    fontWeight: \"500\",\n                    color: \"#555\",\n                    marginBottom: \"15px\"\n                  },\n                  children: [\"Current Locations (\", (locations === null || locations === void 0 ? void 0 : locations.length) || 0, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1497,\n                  columnNumber: 15\n                }, this), console.log(\"locations:\", locations), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"locations-list\",\n                  style: {\n                    border: \"1px solid #ddd\",\n                    borderRadius: \"8px\",\n                    backgroundColor: \"#fff\",\n                    maxHeight: \"400px\",\n                    overflowY: \"auto\"\n                  },\n                  children: locations && locations.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [...locations].map((location, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: \"15px\",\n                        borderBottom: index < locations.length - 1 ? \"1px solid #eee\" : \"none\",\n                        backgroundColor: \"#fff\",\n                        transition: \"background-color 0.2s\"\n                      },\n                      onMouseEnter: e => e.currentTarget.style.backgroundColor = \"#f8f9fa\",\n                      onMouseLeave: e => e.currentTarget.style.backgroundColor = \"#fff\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"flex-start\",\n                            marginBottom: \"8px\"\n                          },\n                          children: /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              flex: 1\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                fontSize: \"16px\",\n                                color: \"#333\",\n                                fontWeight: \"600\",\n                                marginBottom: \"4px\"\n                              },\n                              children: location.commonName\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1533,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                              style: {\n                                fontSize: \"14px\",\n                                color: \"#666\",\n                                lineHeight: \"1.4\"\n                              },\n                              children: [location.address, location.city && /*#__PURE__*/_jsxDEV(_Fragment, {\n                                children: [/*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1538,\n                                  columnNumber: 55\n                                }, this), location.city, location.state && `, ${location.state}`, location.zip && ` ${location.zip}`]\n                              }, void 0, true)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1536,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1532,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1531,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: \"flex\",\n                            gap: \"8px\",\n                            flexWrap: \"wrap\"\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => handleEditLocation(index),\n                            style: {\n                              padding: \"6px 12px\",\n                              fontSize: \"12px\",\n                              color: \"#1976d2\",\n                              backgroundColor: \"#e3f2fd\",\n                              border: \"1px solid #1976d2\",\n                              borderRadius: \"4px\",\n                              cursor: \"pointer\",\n                              transition: \"all 0.2s\"\n                            },\n                            onMouseOver: e => {\n                              e.target.style.backgroundColor = \"#1976d2\";\n                              e.target.style.color = \"#fff\";\n                            },\n                            onMouseOut: e => {\n                              e.target.style.backgroundColor = \"#e3f2fd\";\n                              e.target.style.color = \"#1976d2\";\n                            },\n                            children: \"Edit\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1543,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                            onClick: () => handleRemoveLocation(index),\n                            style: {\n                              padding: \"6px 12px\",\n                              fontSize: \"12px\",\n                              color: \"#d32f2f\",\n                              backgroundColor: \"#ffebee\",\n                              border: \"1px solid #d32f2f\",\n                              borderRadius: \"4px\",\n                              cursor: \"pointer\",\n                              transition: \"all 0.2s\"\n                            },\n                            onMouseOver: e => {\n                              e.target.style.backgroundColor = \"#d32f2f\";\n                              e.target.style.color = \"#fff\";\n                            },\n                            onMouseOut: e => {\n                              e.target.style.backgroundColor = \"#ffebee\";\n                              e.target.style.color = \"#d32f2f\";\n                            },\n                            children: \"Delete\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1566,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1542,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1530,\n                        columnNumber: 25\n                      }, this)\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1519,\n                      columnNumber: 23\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1517,\n                    columnNumber: 19\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      textAlign: \"center\",\n                      color: \"#777\",\n                      fontSize: \"16px\",\n                      padding: \"40px 20px\",\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      justifyContent: \"center\",\n                      alignItems: \"center\",\n                      height: \"100%\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"48px\",\n                        marginBottom: \"10px\"\n                      },\n                      children: \"\\uD83D\\uDCCD\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1606,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"No locations defined yet.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1607,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      style: {\n                        fontSize: \"14px\",\n                        color: \"#999\"\n                      },\n                      children: \"Add your first location using the form on the right.\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1608,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1595,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1509,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1496,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"locations-form-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    fontSize: \"18px\",\n                    fontWeight: \"500\",\n                    color: \"#555\",\n                    marginBottom: \"15px\"\n                  },\n                  children: editingLocation !== null ? 'Edit Location' : 'Add New Location'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1616,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"locations-form\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"grid\",\n                      gridTemplateColumns: \"1fr\",\n                      gap: \"15px\",\n                      marginBottom: \"15px\",\n                      flex: \"1\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingLocation !== null ? editLocation.commonName : newLocation.commonName,\n                      onChange: e => {\n                        if (editingLocation !== null) {\n                          setEditLocation({\n                            ...editLocation,\n                            commonName: e.target.value\n                          });\n                        } else {\n                          setNewLocation({\n                            ...newLocation,\n                            commonName: e.target.value\n                          });\n                        }\n                      },\n                      placeholder: \"Common Name (e.g., Main Hospital)\",\n                      style: {\n                        padding: \"12px\",\n                        fontSize: \"16px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s\"\n                      },\n                      onFocus: e => e.target.style.borderColor = \"#1976d2\",\n                      onBlur: e => e.target.style.borderColor = \"#ccc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1628,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingLocation !== null ? editLocation.address : newLocation.address,\n                      onChange: e => {\n                        if (editingLocation !== null) {\n                          setEditLocation({\n                            ...editLocation,\n                            address: e.target.value\n                          });\n                        } else {\n                          setNewLocation({\n                            ...newLocation,\n                            address: e.target.value\n                          });\n                        }\n                      },\n                      placeholder: \"Street Address\",\n                      style: {\n                        padding: \"12px\",\n                        fontSize: \"16px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s\"\n                      },\n                      onFocus: e => e.target.style.borderColor = \"#1976d2\",\n                      onBlur: e => e.target.style.borderColor = \"#ccc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1650,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1627,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"grid\",\n                      gridTemplateColumns: \"2fr 1fr 1fr\",\n                      gap: \"10px\",\n                      marginBottom: \"20px\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingLocation !== null ? editLocation.city : newLocation.city,\n                      onChange: e => {\n                        if (editingLocation !== null) {\n                          setEditLocation({\n                            ...editLocation,\n                            city: e.target.value\n                          });\n                        } else {\n                          setNewLocation({\n                            ...newLocation,\n                            city: e.target.value\n                          });\n                        }\n                      },\n                      placeholder: \"City\",\n                      style: {\n                        padding: \"12px\",\n                        fontSize: \"16px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s\"\n                      },\n                      onFocus: e => e.target.style.borderColor = \"#1976d2\",\n                      onBlur: e => e.target.style.borderColor = \"#ccc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1674,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingLocation !== null ? editLocation.state : newLocation.state,\n                      onChange: e => {\n                        if (editingLocation !== null) {\n                          setEditLocation({\n                            ...editLocation,\n                            state: e.target.value\n                          });\n                        } else {\n                          setNewLocation({\n                            ...newLocation,\n                            state: e.target.value\n                          });\n                        }\n                      },\n                      placeholder: \"State\",\n                      style: {\n                        padding: \"12px\",\n                        fontSize: \"16px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s\"\n                      },\n                      onFocus: e => e.target.style.borderColor = \"#1976d2\",\n                      onBlur: e => e.target.style.borderColor = \"#ccc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1696,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"text\",\n                      value: editingLocation !== null ? editLocation.zip : newLocation.zip,\n                      onChange: e => {\n                        if (editingLocation !== null) {\n                          setEditLocation({\n                            ...editLocation,\n                            zip: e.target.value\n                          });\n                        } else {\n                          setNewLocation({\n                            ...newLocation,\n                            zip: e.target.value\n                          });\n                        }\n                      },\n                      placeholder: \"ZIP Code\",\n                      style: {\n                        padding: \"12px\",\n                        fontSize: \"16px\",\n                        border: \"1px solid #ccc\",\n                        borderRadius: \"6px\",\n                        outline: \"none\",\n                        transition: \"border-color 0.2s\"\n                      },\n                      onFocus: e => e.target.style.borderColor = \"#1976d2\",\n                      onBlur: e => e.target.style.borderColor = \"#ccc\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1718,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1673,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginTop: \"auto\",\n                      display: \"flex\",\n                      gap: \"10px\"\n                    },\n                    children: editingLocation !== null ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: handleSaveEdit,\n                        style: {\n                          flex: \"1\",\n                          padding: \"12px 20px\",\n                          fontSize: \"16px\",\n                          fontWeight: \"600\",\n                          color: \"#fff\",\n                          backgroundColor: \"#4caf50\",\n                          border: \"none\",\n                          borderRadius: \"6px\",\n                          cursor: \"pointer\",\n                          transition: \"background-color 0.2s\"\n                        },\n                        onMouseOver: e => e.target.style.backgroundColor = \"#45a049\",\n                        onMouseOut: e => e.target.style.backgroundColor = \"#4caf50\",\n                        children: \"Save Changes\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1744,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: handleCancelEdit,\n                        style: {\n                          flex: \"1\",\n                          padding: \"12px 20px\",\n                          fontSize: \"16px\",\n                          fontWeight: \"600\",\n                          color: \"#666\",\n                          backgroundColor: \"#f5f5f5\",\n                          border: \"1px solid #ddd\",\n                          borderRadius: \"6px\",\n                          cursor: \"pointer\",\n                          transition: \"background-color 0.2s\"\n                        },\n                        onMouseOver: e => e.target.style.backgroundColor = \"#e0e0e0\",\n                        onMouseOut: e => e.target.style.backgroundColor = \"#f5f5f5\",\n                        children: \"Cancel\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1763,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true) : /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: handleAddLocation,\n                      style: {\n                        width: \"100%\",\n                        padding: \"12px 20px\",\n                        fontSize: \"16px\",\n                        fontWeight: \"600\",\n                        color: \"#fff\",\n                        backgroundColor: \"#1976d2\",\n                        border: \"none\",\n                        borderRadius: \"6px\",\n                        cursor: \"pointer\",\n                        transition: \"background-color 0.2s\"\n                      },\n                      onMouseOver: e => e.target.style.backgroundColor = \"#1565c0\",\n                      onMouseOut: e => e.target.style.backgroundColor = \"#1976d2\",\n                      children: \"Add Location\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1784,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1741,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1626,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1615,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1494,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1490,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1486,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-section-header\",\n            children: \"Manage Company Code\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1813,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                gap: \"15px\",\n                marginTop: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  gap: \"10px\",\n                  alignItems: \"flex-start\",\n                  marginTop: \"10px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: \"1\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"companyName\",\n                    style: {\n                      display: \"block\",\n                      fontSize: \"16px\",\n                      fontWeight: \"500\",\n                      color: \"#555\",\n                      marginBottom: \"8px\"\n                    },\n                    children: [\"Company Name \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: \"red\"\n                      },\n                      children: \"* \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1849,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1839,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"companyName\",\n                    type: \"text\",\n                    value: name,\n                    onChange: e => setName(e.target.value),\n                    placeholder: \"Enter company name\",\n                    style: {\n                      width: \"100%\",\n                      padding: \"10px\",\n                      fontSize: \"16px\",\n                      border: \"1px solid #ccc\",\n                      borderRadius: \"6px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1851,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1838,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    flex: \"1\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: \"companyCode\",\n                    style: {\n                      display: \"block\",\n                      fontSize: \"16px\",\n                      fontWeight: \"500\",\n                      color: \"#555\",\n                      marginBottom: \"8px\"\n                    },\n                    children: [\"Company Code \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: \"red\"\n                      },\n                      children: \"* \"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1879,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1869,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    id: \"companyCode\",\n                    type: \"text\",\n                    value: newCode,\n                    onChange: e => setNewCode(e.target.value),\n                    placeholder: \"Enter or generate a code\",\n                    style: {\n                      width: \"100%\",\n                      padding: \"10px\",\n                      fontSize: \"16px\",\n                      border: \"1px solid #ccc\",\n                      borderRadius: \"6px\"\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1881,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1868,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: generateRandomCode,\n                  style: {\n                    marginTop: \"30px\",\n                    // Align button with inputs\n                    padding: \"10px 20px\",\n                    fontSize: \"16px\",\n                    color: \"#fff\",\n                    backgroundColor: \"#1976d2\",\n                    border: \"none\",\n                    borderRadius: \"6px\",\n                    cursor: \"pointer\",\n                    whiteSpace: \"nowrap\"\n                  },\n                  children: /*#__PURE__*/_jsxDEV(GiCycle, {\n                    size: 20\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1912,\n                    columnNumber: 17\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1898,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1829,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: \"companyDescription\",\n                  style: {\n                    display: \"block\",\n                    fontSize: \"16px\",\n                    fontWeight: \"500\",\n                    color: \"#555\",\n                    marginBottom: \"8px\"\n                  },\n                  children: [\"Description\", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      fontSize: \"12px\",\n                      color: \"#777\"\n                    },\n                    children: \"(optional)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1929,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1918,\n                  columnNumber: 15\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  id: \"companyDescription\",\n                  value: description,\n                  onChange: e => setDescription(e.target.value),\n                  rows: \"4\",\n                  placeholder: \"Enter company description\",\n                  style: {\n                    width: \"100%\",\n                    padding: \"10px\",\n                    fontSize: \"16px\",\n                    border: \"1px solid #ccc\",\n                    borderRadius: \"6px\",\n                    resize: \"none\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1933,\n                  columnNumber: 15\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1917,\n                columnNumber: 13\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: \"10px\"\n                },\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: saveCode,\n                  style: {\n                    padding: \"10px 20px\",\n                    fontSize: \"16px\",\n                    color: \"#fff\",\n                    backgroundColor: \"#4caf50\",\n                    border: \"none\",\n                    borderRadius: \"6px\",\n                    cursor: \"pointer\"\n                  },\n                  children: \"Save Code\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1952,\n                  columnNumber: 15\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1951,\n                columnNumber: 13\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1820,\n              columnNumber: 11\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginTop: \"20px\",\n                backgroundColor: \"#fff\",\n                padding: \"15px\",\n                borderRadius: \"8px\",\n                border: \"1px solid #e0e0e0\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  fontSize: \"18px\",\n                  fontWeight: \"500\",\n                  color: \"#555\",\n                  marginBottom: \"15px\",\n                  borderBottom: \"1px solid #eee\",\n                  paddingBottom: \"10px\"\n                },\n                children: [\"Saved Company Codes (\", savedCodes.length, \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1979,\n                columnNumber: 13\n              }, this), savedCodes && savedCodes.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  maxHeight: \"300px\",\n                  overflowY: \"auto\"\n                },\n                children: savedCodes.map((code, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: \"8px 12px\",\n                    borderBottom: index < savedCodes.length - 1 ? \"1px solid #eee\" : \"none\",\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\",\n                    backgroundColor: \"#fff\",\n                    transition: \"background-color 0.2s\",\n                    minHeight: \"auto\"\n                  },\n                  onMouseEnter: e => e.currentTarget.style.backgroundColor = \"#f8f9fa\",\n                  onMouseLeave: e => e.currentTarget.style.backgroundColor = \"#fff\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"14px\",\n                        fontWeight: \"500\",\n                        color: \"#333\",\n                        marginBottom: \"2px\"\n                      },\n                      children: code.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2011,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"13px\",\n                        color: \"#666\"\n                      },\n                      children: [\"Code: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          fontWeight: \"500\"\n                        },\n                        children: code.code\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 2015,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2014,\n                      columnNumber: 23\n                    }, this), code.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: \"12px\",\n                        color: \"#777\",\n                        marginTop: \"2px\"\n                      },\n                      children: code.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2018,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2010,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: \"flex\",\n                      gap: \"6px\",\n                      alignItems: \"center\"\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleCopyCode(code.code),\n                      style: {\n                        padding: \"4px 8px\",\n                        fontSize: \"11px\",\n                        color: \"#1976d2\",\n                        backgroundColor: \"#e3f2fd\",\n                        border: \"1px solid #1976d2\",\n                        borderRadius: \"4px\",\n                        cursor: \"pointer\",\n                        transition: \"all 0.2s\"\n                      },\n                      onMouseOver: e => {\n                        e.target.style.backgroundColor = \"#1976d2\";\n                        e.target.style.color = \"#fff\";\n                      },\n                      onMouseOut: e => {\n                        e.target.style.backgroundColor = \"#e3f2fd\";\n                        e.target.style.color = \"#1976d2\";\n                      },\n                      children: \"Copy\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2024,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleDeleteCode(index, code.id),\n                      style: {\n                        padding: \"4px 8px\",\n                        fontSize: \"11px\",\n                        color: \"#d32f2f\",\n                        backgroundColor: \"#ffebee\",\n                        border: \"1px solid #d32f2f\",\n                        borderRadius: \"4px\",\n                        cursor: \"pointer\",\n                        transition: \"all 0.2s\"\n                      },\n                      onMouseOver: e => {\n                        e.target.style.backgroundColor = \"#d32f2f\";\n                        e.target.style.color = \"#fff\";\n                      },\n                      onMouseOut: e => {\n                        e.target.style.backgroundColor = \"#ffebee\";\n                        e.target.style.color = \"#d32f2f\";\n                      },\n                      children: \"Delete\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 2047,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 2023,\n                    columnNumber: 21\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1995,\n                  columnNumber: 19\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1993,\n                columnNumber: 15\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  textAlign: \"center\",\n                  color: \"#777\",\n                  fontSize: \"16px\",\n                  padding: \"30px 20px\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"No company codes saved yet.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2083,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    fontSize: \"14px\",\n                    color: \"#999\"\n                  },\n                  children: \"Add your first company code using the form above.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2084,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2075,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1970,\n              columnNumber: 11\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1816,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1812,\n          columnNumber: 9\n        }, this), userId === 2 && /*#__PURE__*/_jsxDEV(\"section\", {\n          className: \"settings-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"settings-section-header\",\n            style: {\n              cursor: \"pointer\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"space-between\",\n              padding: \"12px 0\",\n              margin: \"0\",\n              fontSize: \"18px\",\n              fontWeight: \"600\",\n              color: \"#fff\",\n              borderBottom: \"1px solid rgba(255, 255, 255, 0.1)\",\n              minHeight: \"auto\"\n            },\n            onClick: () => setIsAiSettingsExpanded(!isAiSettingsExpanded),\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"AI Agent Preconditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                fontSize: \"16px\",\n                transition: \"transform 0.2s ease\",\n                transform: isAiSettingsExpanded ? \"rotate(90deg)\" : \"rotate(0deg)\"\n              },\n              children: \"\\u25B6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2096,\n            columnNumber: 13\n          }, this), isAiSettingsExpanded && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"settings-section-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                backgroundColor: \"#fff3cd\",\n                border: \"1px solid #ffeaa7\",\n                borderRadius: \"8px\",\n                padding: \"15px\",\n                marginBottom: \"20px\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: \"14px\",\n                  color: \"#856404\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Super Admin Access:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2133,\n                  columnNumber: 21\n                }, this), \" Configure AI prompts and behavior for document summarization, question-answering, and action generation.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2132,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: \"25px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: \"block\",\n                  fontSize: \"16px\",\n                  fontWeight: \"500\",\n                  color: \"#fff\",\n                  marginBottom: \"8px\"\n                },\n                children: \"Document Summary Prompt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2139,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: ((_aiSettings$ai_prompt = aiSettings.ai_prompts) === null || _aiSettings$ai_prompt === void 0 ? void 0 : _aiSettings$ai_prompt.document_summary) || \"\",\n                onChange: e => updateAiPrompt('document_summary', e.target.value),\n                placeholder: \"Enter the AI prompt for document summarization...\",\n                rows: 4,\n                style: {\n                  width: \"100%\",\n                  padding: \"12px\",\n                  fontSize: \"14px\",\n                  border: \"1px solid #ccc\",\n                  borderRadius: \"6px\",\n                  outline: \"none\",\n                  resize: \"vertical\",\n                  fontFamily: \"monospace\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2138,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: \"25px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: \"block\",\n                  fontSize: \"16px\",\n                  fontWeight: \"500\",\n                  color: \"#fff\",\n                  marginBottom: \"8px\"\n                },\n                children: \"Document Q&A Prompt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2168,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: ((_aiSettings$ai_precon = aiSettings.ai_preconditions) === null || _aiSettings$ai_precon === void 0 ? void 0 : _aiSettings$ai_precon.document_qa_prompt) || \"\",\n                onChange: e => updateAiPrecondition('document_qa_prompt', e.target.value),\n                placeholder: \"Enter the AI prompt for document question-answering...\",\n                rows: 4,\n                style: {\n                  width: \"100%\",\n                  padding: \"12px\",\n                  fontSize: \"14px\",\n                  border: \"1px solid #ccc\",\n                  borderRadius: \"6px\",\n                  outline: \"none\",\n                  resize: \"vertical\",\n                  fontFamily: \"monospace\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2177,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2167,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: \"25px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                style: {\n                  display: \"block\",\n                  fontSize: \"16px\",\n                  fontWeight: \"500\",\n                  color: \"#fff\",\n                  marginBottom: \"8px\"\n                },\n                children: \"Action Generation Prompt\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                value: ((_aiSettings$ai_precon2 = aiSettings.ai_preconditions) === null || _aiSettings$ai_precon2 === void 0 ? void 0 : _aiSettings$ai_precon2.action_generation_prompt) || \"\",\n                onChange: e => updateAiPrecondition('action_generation_prompt', e.target.value),\n                placeholder: \"Enter the AI prompt for generating action checklists...\",\n                rows: 4,\n                style: {\n                  width: \"100%\",\n                  padding: \"12px\",\n                  fontSize: \"14px\",\n                  border: \"1px solid #ccc\",\n                  borderRadius: \"6px\",\n                  outline: \"none\",\n                  resize: \"vertical\",\n                  fontFamily: \"monospace\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2206,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2196,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: \"right\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: saveAiSettings,\n                disabled: loadingAiSettings,\n                style: {\n                  padding: \"12px 24px\",\n                  fontSize: \"16px\",\n                  fontWeight: \"600\",\n                  color: \"#fff\",\n                  backgroundColor: loadingAiSettings ? \"#ccc\" : \"#1976d2\",\n                  border: \"none\",\n                  borderRadius: \"6px\",\n                  cursor: loadingAiSettings ? \"not-allowed\" : \"pointer\",\n                  transition: \"background-color 0.2s\"\n                },\n                children: loadingAiSettings ? \"Saving...\" : \"Save AI Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2226,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2124,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2095,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 896,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 895,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n}\n_s(Settings, \"BL4yeImFBL/7geNXoRlnia/wD4I=\", false, function () {\n  return [useUseType, useFontSize];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useMemo", "useUseType", "useFontSize", "FaPlus", "GiCycle", "toast", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "deleteC<PERSON>ie", "name", "document", "cookie", "COMPANY_SETTINGS_COOKIE", "Settings", "role", "token", "userId", "baseUrl", "_s", "_aiSettings$ai_prompt", "_aiSettings$ai_precon", "_aiSettings$ai_precon2", "activeModules", "selectedModule", "updateSelectedModule", "formConfig", "updateFormConfig", "locations", "updateLocations", "defaultFields", "label", "type", "required", "options", "allFormConfig", "console", "log", "moduleConfig", "newField", "setNewField", "newLocation", "setNewLocation", "commonName", "address", "city", "state", "zip", "aiSettings", "setAiSettings", "ai_prompts", "ai_preconditions", "loadingAiSettings", "setLoadingAiSettings", "isAiSettingsExpanded", "setIsAiSettingsExpanded", "newModule", "setNewModule", "moduleInfo", "setModuleInfo", "showAddModule", "setShowAddModule", "fontSizeScale", "setFontSizeScale", "editingLocation", "setEditingLocation", "editLocation", "setEditLocation", "newCode", "setNewCode", "companyCode", "setCompanyCode", "savedCodes", "setSavedCodes", "setName", "description", "setDescription", "companyId", "setCompanyId", "fetchModuleInfo", "forceRefresh", "localModuleInfo", "localStorage", "getItem", "JSON", "parse", "response", "fetch", "headers", "Authorization", "ok", "data", "json", "setItem", "stringify", "err", "error", "fetchSavedCodes", "codes", "status", "documentElement", "style", "setProperty", "loadAiSettings", "message", "saveAiSettings", "method", "body", "success", "Error", "updateAiPrompt", "promptType", "value", "prev", "updateAiPrecondition", "preconditionType", "display", "justifyContent", "alignItems", "height", "fontSize", "color", "fontFamily", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleModuleChange", "e", "target", "handleFontSizeChange", "change", "prevScale", "newScale", "Math", "max", "min", "parseFloat", "toFixed", "handleAddField", "position", "autoClose", "some", "field", "updatedConfig", "handleRemoveField", "index", "defaultField", "filter", "_", "i", "handleFieldChange", "key", "handleSaveFormConfig", "module", "errorData", "handleRemoveLocation", "location", "confirmDelete", "window", "confirm", "updatedLocations", "handleEditLocation", "handleSaveEdit", "alert", "handleCancelEdit", "handleAddLocation", "generateRandomCode", "randomCode", "floor", "random", "toString", "validations", "length", "trim", "saveCode", "code", "user_id", "prevCodes", "errorMessage", "handleDeleteCode", "id", "result", "fire", "title", "text", "icon", "showCancelButton", "confirmButtonText", "cancelButtonText", "confirmButtonColor", "cancelButtonColor", "isConfirmed", "statusText", "handleCopyCode", "navigator", "clipboard", "writeText", "className", "maxHeight", "marginBottom", "padding", "border", "borderRadius", "backgroundColor", "fontWeight", "onChange", "width", "outline", "transition", "onFocus", "borderColor", "onBlur", "map", "borderBottom", "paddingBottom", "listStyle", "gap", "flexWrap", "flex", "placeholder", "boxShadow", "join", "split", "opt", "whiteSpace", "checked", "cursor", "accentColor", "fontStyle", "onClick", "onMouseOver", "onMouseOut", "marginTop", "marginLeft", "size", "paddingTop", "borderTop", "overflowY", "onMouseEnter", "currentTarget", "onMouseLeave", "lineHeight", "textAlign", "flexDirection", "gridTemplateColumns", "htmlFor", "rows", "resize", "minHeight", "margin", "transform", "document_summary", "document_qa_prompt", "action_generation_prompt", "disabled", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/Alertcome/alertcom/src/Settings.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useMemo } from \"react\";\r\nimport { useUseType } from \"./context/UseTypeContext\";\r\nimport { useFontSize } from \"./context/FontSizeContext\";\r\nimport { FaPlus } from \"react-icons/fa\";\r\nimport { GiCycle } from \"react-icons/gi\";\r\nimport { toast } from \"react-toastify\";\r\nimport Swal from \"sweetalert2\";\r\n\r\n// Cookie utility function to invalidate company settings cache\r\nconst deleteCookie = (name) => {\r\n  document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;\r\n};\r\n\r\nconst COMPANY_SETTINGS_COOKIE = 'alertcomm_company_settings';\r\n\r\nfunction Settings({ role, token, userId, baseUrl }) {\r\n  // console.log(role, token, userId);\r\n\r\n  const {\r\n    activeModules,\r\n    selectedModule,\r\n    updateSelectedModule,\r\n    formConfig,\r\n    updateFormConfig,\r\n    locations,\r\n    updateLocations,\r\n  } = useUseType();\r\n\r\n  // Ensure formConfig always includes default fields - wrapped in useMemo to avoid dependency issues\r\n  const defaultFields = useMemo(() => [\r\n    { name: \"title\", label: \"Title\", type: \"text\", required: true },\r\n    { name: \"info\", label: \"Info\", type: \"textarea\", required: false },\r\n    { name: \"description\", label: \"Description\", type: \"textarea\", required: false },\r\n    { name: \"scale\", label: \"Scale\", type: \"select\", options: [\"Small\", \"Medium\", \"Large\"], required: true },\r\n    { name: \"urgency\", label: \"Urgency\", type: \"select\", options: [\"Low\", \"Medium\", \"High\", \"Immediate\"], required: true },\r\n  ], []);\r\n\r\n  // Use useMemo to properly handle the form config for each module\r\n  const allFormConfig = useMemo(() => {\r\n    console.log('Selected Module:', selectedModule);\r\n    console.log('Form Config for module:', formConfig);\r\n\r\n    // The formConfig from context already contains the complete config for the selected module\r\n    // including default fields, so we don't need to combine them again\r\n    const moduleConfig = formConfig || defaultFields;\r\n\r\n    console.log('Module Form Config:', moduleConfig);\r\n    return moduleConfig;\r\n  }, [selectedModule, formConfig, defaultFields]);\r\n\r\n  const [newField, setNewField] = useState({\r\n    name: \"\",\r\n    label: \"\",\r\n    type: \"text\",\r\n    required: false,\r\n    options: [],\r\n  });\r\n  const [newLocation, setNewLocation] = useState({\r\n    commonName: \"\",\r\n    address: \"\",\r\n    city: \"\",\r\n    state: \"\",\r\n    zip: \"\",\r\n  });\r\n\r\n  // AI Settings state\r\n  const [aiSettings, setAiSettings] = useState({\r\n    ai_prompts: {},\r\n    ai_preconditions: {}\r\n  });\r\n  const [loadingAiSettings, setLoadingAiSettings] = useState(false);\r\n  const [isAiSettingsExpanded, setIsAiSettingsExpanded] = useState(true);\r\n  const [newModule, setNewModule] = useState(\"\");\r\n  const [moduleInfo, setModuleInfo] = useState({});\r\n  const [showAddModule, setShowAddModule] = useState(false);\r\n  // Use the fontSizeScale from the FontSizeContext\r\n  const { fontSizeScale, setFontSizeScale } = useFontSize();\r\n  const [editingLocation, setEditingLocation] = useState(null);\r\n  const [editLocation, setEditLocation] = useState({\r\n    commonName: \"\",\r\n    address: \"\",\r\n    city: \"\",\r\n    state: \"\",\r\n    zip: \"\",\r\n  });\r\n  const [newCode, setNewCode] = useState(\"\");\r\n  const [companyCode, setCompanyCode] = useState([]);\r\n  // State to store the list of saved company codes\r\n  const [savedCodes, setSavedCodes] = useState([]);\r\n  const [name, setName] = useState(\"\"); // State for company name\r\n  const [description, setDescription] = useState(\"\"); // State for company description\r\n  const [companyId, setCompanyId] = useState(null); // State for company ID\r\n\r\n\r\n  \r\n  // Optimized fetchModuleInfo - only calls API when necessary\r\n  const fetchModuleInfo = useCallback(async (forceRefresh = false) => {\r\n    try {\r\n      console.log('fetchModuleInfo called, forceRefresh:', forceRefresh);\r\n\r\n      // Check localStorage first (only if not forcing refresh)\r\n      if (!forceRefresh) {\r\n        const localModuleInfo = localStorage.getItem('moduleInfo');\r\n        if (localModuleInfo) {\r\n          setModuleInfo(JSON.parse(localModuleInfo));\r\n          console.log('Using cached moduleInfo from localStorage');\r\n          return; // Exit early if we have cached data\r\n        }\r\n      }\r\n\r\n      // Only fetch from backend if no cached data or forcing refresh\r\n      console.log('Fetching moduleInfo from API...');\r\n      const response = await fetch(`${baseUrl}/company-settings`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setModuleInfo(data.moduleInfo || {});\r\n        localStorage.setItem('moduleInfo', JSON.stringify(data.moduleInfo || {}));\r\n        console.log('ModuleInfo fetched and cached successfully');\r\n\r\n        // Note: We don't update locations here because UseTypeContext handles location loading\r\n        // The Settings page only manages user-added locations, not the global location state\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error fetching module info:\", err);\r\n    }\r\n  }, [baseUrl, token, setModuleInfo]);\r\n\r\n\r\n  // Add a function to fetch saved company codes\r\n  const fetchSavedCodes = useCallback(async () => {\r\n    try {\r\n      console.log('Fetching saved company codes...');\r\n      const response = await fetch(`${baseUrl}/user/company?user_id=${userId}`, {\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      });\r\n\r\n      if (response.ok) {\r\n        const codes = await response.json();\r\n        console.log('Fetched company codes:', codes);\r\n        setSavedCodes(codes || []);\r\n      } else {\r\n        console.log('Failed to fetch company codes:', response.status);\r\n      }\r\n    } catch (err) {\r\n      console.error('Error fetching company codes:', err);\r\n    }\r\n  }, [baseUrl, token, userId]);\r\n\r\n  // useEffect(() => {\r\n  //   if (userId) {\r\n  //     // Only fetch company codes and settings once on mount\r\n  //     fetchSavedCodes(); // This also sets company ID\r\n  //     // fetchModuleInfo and fetchCompanySettings are handled by UseTypeContext\r\n  //   }\r\n  // }, [userId, fetchSavedCodes]);\r\n\r\n\r\n  // Only fetch data once on mount when userId is available\r\n  useEffect(() => {\r\n    if (userId) {\r\n      fetchModuleInfo(false); // Changed to false to avoid unnecessary API calls\r\n      fetchSavedCodes();\r\n    }\r\n  }, [userId, fetchModuleInfo, fetchSavedCodes]); // Removed locations from dependencies to prevent infinite loop\r\n\r\n\r\n\r\n  // Apply font size scaling to the document root\r\n  useEffect(() => {\r\n    document.documentElement.style.setProperty('--font-size-scale', fontSizeScale);\r\n    localStorage.setItem('fontSizeScale', fontSizeScale);\r\n  }, [fontSizeScale]);\r\n\r\n  // AI Settings functions\r\n  const loadAiSettings = useCallback(async () => {\r\n    setLoadingAiSettings(true);\r\n    try {\r\n      const response = await fetch(`${baseUrl}/ai-settings`, {\r\n        headers: { 'Authorization': `Bearer ${token}` }\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        setAiSettings(data);\r\n        console.log('AI settings loaded successfully:', data);\r\n      } else if (response.status === 403) {\r\n        console.log('Access denied to AI settings - user is not Super Admin');\r\n        // Don't show error toast for access denied, just log it\r\n      } else {\r\n        console.error('Failed to load AI settings, status:', response.status);\r\n        // Only show error for actual failures, not access denied\r\n        if (response.status !== 403) {\r\n          toast.error('Failed to load AI settings');\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading AI settings:', error);\r\n      // Only show error toast if it's not a network/access issue\r\n      if (error.message !== 'Failed to fetch') {\r\n        toast.error('Failed to load AI settings');\r\n      }\r\n    } finally {\r\n      setLoadingAiSettings(false);\r\n    }\r\n  }, [baseUrl, token]);\r\n\r\n  // Load AI settings on component mount\r\n  useEffect(() => {\r\n    if (userId === 'ttornstrom') {\r\n      loadAiSettings();\r\n    }\r\n  }, [userId, loadAiSettings]);\r\n\r\n  const saveAiSettings = async () => {\r\n    setLoadingAiSettings(true);\r\n    try {\r\n      const response = await fetch(`${baseUrl}/ai-settings`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${token}`\r\n        },\r\n        body: JSON.stringify(aiSettings)\r\n      });\r\n\r\n      if (response.ok) {\r\n        toast.success('AI settings saved successfully!');\r\n      } else {\r\n        throw new Error('Failed to save AI settings');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error saving AI settings:', error);\r\n      toast.error('Failed to save AI settings');\r\n    } finally {\r\n      setLoadingAiSettings(false);\r\n    }\r\n  };\r\n\r\n  const updateAiPrompt = (promptType, value) => {\r\n    setAiSettings(prev => ({\r\n      ...prev,\r\n      ai_prompts: {\r\n        ...prev.ai_prompts,\r\n        [promptType]: value\r\n      }\r\n    }));\r\n  };\r\n\r\n  const updateAiPrecondition = (preconditionType, value) => {\r\n    setAiSettings(prev => ({\r\n      ...prev,\r\n      ai_preconditions: {\r\n        ...prev.ai_preconditions,\r\n        [preconditionType]: value\r\n      }\r\n    }));\r\n  };\r\n\r\n  if (role !== \"commander\") {\r\n    return (\r\n      <div\r\n        style={{\r\n          display: \"flex\",\r\n          justifyContent: \"center\",\r\n          alignItems: \"center\",\r\n          height: \"100vh\",\r\n          fontSize: \"24px\",\r\n          color: \"#d32f2f\",\r\n          fontFamily: \"-apple-system, BlinkMacSystemFont, sans-serif\",\r\n        }}\r\n      >\r\n        Access Denied\r\n      </div>\r\n    );\r\n  }\r\n  const handleModuleChange = (e) => {\r\n    const newModule = e.target.value;\r\n    updateSelectedModule(newModule);\r\n  };\r\n  \r\n  // fetchModuleInfo is already defined above using useCallback\r\n  \r\n  const handleFontSizeChange = (change) => {\r\n    setFontSizeScale(prevScale => {\r\n      const newScale = Math.max(0.8, Math.min(1.4, prevScale + change));\r\n      return parseFloat(newScale.toFixed(1));\r\n    });\r\n  };\r\n  const handleAddField = () => {\r\n    if (!newField.name || !newField.label) {\r\n      toast.error('Please fill in both Name and Label fields', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Check if field name already exists\r\n    if (allFormConfig.some(field => field.name === newField.name)) {\r\n      toast.error('Field name already exists', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    const updatedConfig = [...allFormConfig, { ...newField }];\r\n    updateFormConfig(updatedConfig);\r\n    setNewField({\r\n      name: \"\",\r\n      label: \"\",\r\n      type: \"text\",\r\n      required: false,\r\n      options: [],\r\n    });\r\n\r\n    toast.success('Field added successfully!', {\r\n      position: \"top-right\",\r\n      autoClose: 3000,\r\n    });\r\n  };\r\n\r\n  const handleRemoveField = (index) => {\r\n    const field = allFormConfig[index];\r\n    // Don't allow removing default fields\r\n    if (defaultFields.some(defaultField => defaultField.name === field.name)) {\r\n      toast.error('Cannot remove default fields (Title, Info, Description, Scale, Urgency)', {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n      return;\r\n    }\r\n\r\n    // Remove the field from the config\r\n    const updatedConfig = allFormConfig.filter((_, i) => i !== index);\r\n    updateFormConfig(updatedConfig);\r\n\r\n    toast.success('Field removed successfully!', {\r\n      position: \"top-right\",\r\n      autoClose: 3000,\r\n    });\r\n  };\r\n\r\n  const handleFieldChange = (index, key, value) => {\r\n    // Create a copy of the current form config and update the specific field\r\n    const updatedConfig = [...allFormConfig];\r\n    updatedConfig[index] = { ...updatedConfig[index], [key]: value };\r\n\r\n    // Update the form config for the current module\r\n    updateFormConfig(updatedConfig);\r\n  };\r\n\r\n  // Add a function to save the form configuration to the backend\r\n  const handleSaveFormConfig = async () => {\r\n    try {\r\n      console.log('Saving form config for module:', selectedModule);\r\n      console.log('Form config data:', allFormConfig);\r\n\r\n      const response = await fetch(`${baseUrl}/company-settings/form-config`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n          \"Content-Type\": \"application/json\",\r\n        },\r\n        body: JSON.stringify({\r\n          module: selectedModule,\r\n          formConfig: allFormConfig\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        toast.success('Form configuration saved successfully!', {\r\n          position: \"top-right\",\r\n          autoClose: 3000,\r\n        });\r\n\r\n        // Form config is handled by UseTypeContext, no need to refresh moduleInfo\r\n      } else {\r\n        const errorData = await response.json();\r\n        console.error('Save failed:', errorData);\r\n        throw new Error(errorData.error || 'Failed to save form configuration');\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error saving form config:\", err);\r\n      toast.error('Failed to save form configuration: ' + err.message, {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n\r\n  const handleRemoveLocation = async (index) => {\r\n    const location = locations[index];\r\n    const confirmDelete = window.confirm(\r\n      `Are you sure you want to delete \"${location.commonName}\"?\\n\\nAddress: ${location.address}\\nCity: ${location.city}, ${location.state} ${location.zip}`\r\n    );\r\n\r\n    if (confirmDelete) {\r\n      const updatedLocations = locations.filter((_, i) => i !== index);\r\n      updateLocations(updatedLocations);\r\n\r\n      // Refresh module info after deleting location\r\n      await fetchModuleInfo(true);\r\n      console.log('Location deleted successfully');\r\n    }\r\n  };\r\n\r\n  const handleEditLocation = (index) => {\r\n    setEditingLocation(index);\r\n    setEditLocation({ ...locations[index] });\r\n    // Clear the add form when editing\r\n    setNewLocation({\r\n      commonName: \"\",\r\n      address: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      zip: \"\",\r\n    });\r\n  };\r\n\r\n  const handleSaveEdit = async () => {\r\n    if (!editLocation.commonName || !editLocation.address) {\r\n      alert('Please fill in both Common Name and Address fields');\r\n      return;\r\n    }\r\n\r\n    const updatedLocations = [...locations];\r\n    updatedLocations[editingLocation] = { ...editLocation };\r\n    updateLocations(updatedLocations);\r\n    setEditingLocation(null);\r\n    setEditLocation({\r\n      commonName: \"\",\r\n      address: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      zip: \"\",\r\n    });\r\n\r\n    // Refresh module info after editing location\r\n    await fetchModuleInfo(true);\r\n    console.log('Location updated successfully');\r\n  };\r\n\r\n  const handleCancelEdit = () => {\r\n    setEditingLocation(null);\r\n    setEditLocation({\r\n      commonName: \"\",\r\n      address: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      zip: \"\",\r\n    });\r\n  };\r\n\r\n  const handleAddLocation = async () => {\r\n    if (!newLocation.commonName || !newLocation.address) {\r\n      alert('Please fill in both Common Name and Address fields');\r\n      return;\r\n    }\r\n\r\n    console.log('Adding new location:', newLocation);\r\n    const updatedLocations = [newLocation, ...locations]; // Add to beginning for latest first\r\n    updateLocations(updatedLocations);\r\n    setNewLocation({\r\n      commonName: \"\",\r\n      address: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      zip: \"\"\r\n    });\r\n\r\n    // Refresh module info after adding location\r\n    await fetchModuleInfo(true);\r\n    console.log('Location added successfully');\r\n  };\r\n\r\n  const generateRandomCode = () => {\r\n    const randomCode = Math.floor(100000 + Math.random() * 900000).toString(); // Generate a 6-digit random code\r\n    setNewCode(randomCode); // Set the 6-digit random code\r\n  };\r\n\r\n  //validations\r\n  const validations = () => {\r\n    // console.log(name, newCode, description);\r\n\r\n    // Validate company name\r\n    if (!name) {\r\n      toast.error(\"Please enter a valid company name.\", {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n      return false;\r\n    }\r\n\r\n    if (!newCode || newCode.length < 4) {\r\n      toast.error(\r\n        \"Please enter a valid company code (at least 6 characters).\",\r\n        {\r\n          position: \"top-right\",\r\n          autoClose: 3000,\r\n        }\r\n      );\r\n      return false;\r\n    }\r\n\r\n    // if (!description ) {\r\n    //   toast.error('Please enter a description .', {\r\n    //     position: 'top-right',\r\n    //     autoClose: 3000,\r\n    //   });\r\n    //   return false;\r\n    // }\r\n\r\n    if (!newCode.trim()) {\r\n      toast.error(\"Please enter or generate a valid code before saving.\", {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  // Save the company code\r\n  const saveCode = async () => {\r\n    if (!validations()) {\r\n      return;\r\n    }\r\n    try {\r\n      const response = await fetch(`${baseUrl}/user/store-company`, {\r\n        method: \"POST\",\r\n        headers: {\r\n          \"Content-Type\": \"application/json\",\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n        body: JSON.stringify({\r\n          name,\r\n          description,\r\n          code: newCode,\r\n          user_id: userId,\r\n        }),\r\n      });\r\n\r\n      if (response.ok) {\r\n        const data = await response.json();\r\n        // console.log(\"Response from server:\", data);\r\n\r\n        setSavedCodes((prevCodes) => [\r\n          ...prevCodes,\r\n          { name, description, code: newCode },\r\n        ]);\r\n        setNewCode(\"\"); // Clear the input field\r\n        setName(\"\"); // Clear the name field\r\n        setDescription(\"\"); // Clear the description field\r\n\r\n        // Invalidate company settings cache when new company is saved\r\n        deleteCookie(COMPANY_SETTINGS_COOKIE);\r\n        console.log(\"Company settings cache invalidated due to new company save\");\r\n\r\n        toast.success(\"Code saved successfully!\", {\r\n          position: \"top-right\",\r\n          autoClose: 3000,\r\n        });\r\n      } else {\r\n        const errorData = await response.json();\r\n        console.error(\"Error response:\", errorData);\r\n\r\n        const errorMessage =\r\n          errorData.error ||\r\n          errorData.message ||\r\n          \"Failed to save the code. Please try again.\";\r\n        toast.error(errorMessage, {\r\n          position: \"top-right\",\r\n          autoClose: 3000,\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error saving company data:\", error);\r\n\r\n      toast.error(\"An unexpected error occurred. Please try again.\", {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  };\r\n\r\n  // fetchCompanyId is already defined above\r\n\r\n  //delete company code\r\n  const handleDeleteCode = async (index, id) => {\r\n    try {\r\n      // Display confirmation dialog using SweetAlert2\r\n      const result = await Swal.fire({\r\n        title: \"Are you sure?\",\r\n        text: \"You won't be able to revert this!\",\r\n        icon: \"warning\",\r\n        showCancelButton: true,\r\n        confirmButtonText: \"Yes, delete it!\",\r\n        cancelButtonText: \"Cancel\",\r\n        confirmButtonColor: \"#d32f2f\",\r\n        cancelButtonColor: \"#777\",\r\n      });\r\n\r\n      if (result.isConfirmed) {\r\n        const response = await fetch(\r\n          `${baseUrl}/user/delete-company/${id}?user_id=${userId}`,\r\n          {\r\n            method: \"DELETE\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${token}`,\r\n            },\r\n          }\r\n        );\r\n\r\n        if (response.ok) {\r\n          setSavedCodes((prevCodes) => prevCodes.filter((_, i) => i !== index));\r\n\r\n          // Invalidate company settings cache when company is deleted\r\n          deleteCookie(COMPANY_SETTINGS_COOKIE);\r\n          console.log(\"Company settings cache invalidated due to company deletion\");\r\n\r\n          toast.success(\"Company code deleted successfully!\", {\r\n            position: \"top-right\",\r\n            autoClose: 3000,\r\n          });\r\n\r\n        } else {\r\n          throw new Error(`Failed to delete: ${response.statusText}`);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error deleting company code:\", error);\r\n      toast.error(\"An unexpected error occurred. Please try again.\", {\r\n        position: \"top-right\",\r\n        autoClose: 3000,\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleCopyCode = (code) => {\r\n    navigator.clipboard.writeText(code);\r\n\r\n    toast.success(\"Code copied to clipboard!\", {\r\n      position: \"top-right\",\r\n      autoClose: 3000,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <style>\r\n        {`\r\n          /* Update the global font-size-scale variable */\r\n          :root {\r\n            --font-size-scale: ${fontSizeScale};\r\n          }\r\n          \r\n          /* Font size controls styling */\r\n          \r\n          .font-size-controls {\r\n            position: fixed;\r\n            bottom: 20px;\r\n            right: 20px;\r\n            display: flex;\r\n            gap: 10px;\r\n            background-color: white;\r\n            padding: 10px;\r\n            border-radius: 8px;\r\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n            z-index: 1000;\r\n          }\r\n          \r\n          .font-size-btn {\r\n            width: 36px;\r\n            height: 36px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            border-radius: 50%;\r\n            border: none;\r\n            background-color: #f0f0f0;\r\n            color: #333;\r\n            font-size: 18px;\r\n            cursor: pointer;\r\n            transition: background-color 0.2s;\r\n          }\r\n          \r\n          .font-size-btn:hover {\r\n            background-color: #e0e0e0;\r\n          }\r\n          \r\n          .font-size-value {\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            font-size: 14px;\r\n            font-weight: 500;\r\n            min-width: 40px;\r\n          }\r\n\r\n          .locations-container {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            gap: 20px;\r\n            align-items: start;\r\n          }\r\n\r\n          .locations-list-container,\r\n          .locations-form-container {\r\n            height: 400px;\r\n            display: flex;\r\n            flex-direction: column;\r\n          }\r\n\r\n          .locations-list {\r\n            flex: 1;\r\n            overflow-y: auto;\r\n            border: 1px solid #ddd;\r\n            border-radius: 8px;\r\n            background-color: #fff;\r\n          }\r\n\r\n          .locations-form {\r\n            height: 100%;\r\n            border: 1px solid #ddd;\r\n            border-radius: 8px;\r\n            padding: 20px;\r\n            background-color: #fff;\r\n            display: flex;\r\n            flex-direction: column;\r\n          }\r\n\r\n          @media (max-width: 768px) {\r\n            .locations-container {\r\n              grid-template-columns: 1fr;\r\n              gap: 20px;\r\n            }\r\n\r\n            .locations-list-container,\r\n            .locations-form-container {\r\n              height: auto;\r\n              min-height: 400px;\r\n            }\r\n          }\r\n\r\n          @media (max-width: 480px) {\r\n            .locations-container {\r\n              gap: 15px;\r\n            }\r\n          }\r\n\r\n          /* Enhanced Settings Page Styles */\r\n          .settings-section {\r\n            background: #fff;\r\n            border-radius: 12px;\r\n            border: 1px solid #e0e0e0;\r\n            margin-bottom: 30px;\r\n            box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n            overflow: hidden;\r\n          }\r\n\r\n          .settings-section-header {\r\n            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\r\n            color: white !important;\r\n            padding: 20px;\r\n            margin: 0;\r\n            font-size: 20px;\r\n            font-weight: 600;\r\n          }\r\n\r\n          .settings-section-content {\r\n            padding: 25px;\r\n          }\r\n\r\n          .form-grid {\r\n            display: grid;\r\n            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\r\n            gap: 20px;\r\n            margin-bottom: 20px;\r\n          }\r\n\r\n          .form-field {\r\n            display: flex;\r\n            flex-direction: column;\r\n          }\r\n\r\n          .form-label {\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            color: #333;\r\n            margin-bottom: 8px;\r\n          }\r\n\r\n          .form-input {\r\n            padding: 12px;\r\n            border: 2px solid #e0e0e0;\r\n            border-radius: 8px;\r\n            font-size: 14px;\r\n            transition: all 0.2s ease;\r\n          }\r\n\r\n          .form-input:focus {\r\n            outline: none;\r\n            border-color: #1976d2;\r\n            box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);\r\n          }\r\n\r\n          .btn-primary {\r\n            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\r\n            color: white;\r\n            border: none;\r\n            padding: 12px 24px;\r\n            border-radius: 8px;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            cursor: pointer;\r\n            transition: all 0.2s ease;\r\n            display: inline-flex;\r\n            align-items: center;\r\n            gap: 8px;\r\n          }\r\n\r\n          .btn-primary:hover {\r\n            transform: translateY(-1px);\r\n            box-shadow: 0 4px 12px rgba(25, 118, 210, 0.3);\r\n          }\r\n\r\n          .btn-secondary {\r\n            background: #f5f5f5;\r\n            color: #666;\r\n            border: 2px solid #e0e0e0;\r\n            padding: 10px 20px;\r\n            border-radius: 8px;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            cursor: pointer;\r\n            transition: all 0.2s ease;\r\n          }\r\n\r\n          .btn-secondary:hover {\r\n            background: #e0e0e0;\r\n            border-color: #ccc;\r\n          }\r\n\r\n          .btn-danger {\r\n            background: linear-gradient(135deg, #d32f2f 0%, #b71c1c 100%);\r\n            color: white;\r\n            border: none;\r\n            padding: 8px 16px;\r\n            border-radius: 6px;\r\n            font-size: 12px;\r\n            font-weight: 600;\r\n            cursor: pointer;\r\n            transition: all 0.2s ease;\r\n          }\r\n\r\n          .btn-danger:hover {\r\n            transform: translateY(-1px);\r\n            box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);\r\n          }\r\n\r\n          .settings-header {\r\n            background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\r\n            color: white;\r\n            padding: 30px 20px;\r\n            margin: -20px -20px 30px -20px;\r\n            text-align: center;\r\n          }\r\n\r\n          .settings-title {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            margin: 0;\r\n            text-shadow: 0 2px 4px rgba(0,0,0,0.1);\r\n          }\r\n\r\n          .settings-subtitle {\r\n            font-size: 16px;\r\n            opacity: 0.9;\r\n            margin: 8px 0 0 0;\r\n          }\r\n        `}\r\n      </style>\r\n      <div className=\"container\">\r\n        <div\r\n          className=\"card\"\r\n          style={{ height: \"100% !important,\", maxHeight: \"100% \" }}\r\n        >\r\n        <div className=\"settings-header\">\r\n          <h1 className=\"settings-title\">Settings</h1>\r\n          <p className=\"settings-subtitle\">Configure your AlertComm system preferences</p>\r\n\r\n        </div>\r\n        \r\n        {/* Font Size Controls */}\r\n        {/* <div className=\"font-size-controls\">\r\n          <button \r\n            className=\"font-size-btn\" \r\n            onClick={() => handleFontSizeChange(-0.1)}\r\n            title=\"Decrease font size\"\r\n          >\r\n            -\r\n          </button>\r\n          <span className=\"font-size-value\">{Math.round(fontSizeScale * 100)}%</span>\r\n          <button \r\n            className=\"font-size-btn\" \r\n            onClick={() => handleFontSizeChange(0.1)}\r\n            title=\"Increase font size\"\r\n          >\r\n            +\r\n          </button>\r\n        </div> */}\r\n        {/* Module Management */}\r\n        <section\r\n          style={{\r\n            marginBottom: \"40px\",\r\n            padding: \"20px\",\r\n            border: \"1px solid #e0e0e0\",\r\n            borderRadius: \"8px\",\r\n            backgroundColor: \"#fafafa\",\r\n          }}\r\n        >\r\n          <h2\r\n            style={{\r\n              fontSize: \"20px\",\r\n              fontWeight: \"500\",\r\n              color: \"#333\",\r\n              marginBottom: \"15px\",\r\n              display: \"flex\",\r\n              justifyContent: \"space-between\",\r\n              alignItems: \"center\"\r\n            }}\r\n          >\r\n            <span>Manage Modules</span>\r\n          </h2>\r\n          \r\n          {/* Module Selection */}\r\n          <div style={{ marginBottom: \"20px\" }}>\r\n            <label\r\n              style={{\r\n                display: \"block\",\r\n                fontSize: \"16px\",\r\n                fontWeight: \"500\",\r\n                color: \"#555\",\r\n                marginBottom: \"8px\",\r\n              }}\r\n            >\r\n              Select Active Module\r\n            </label>\r\n            <select\r\n              value={selectedModule}\r\n              onChange={handleModuleChange}\r\n              style={{\r\n                width: \"100%\",\r\n                padding: \"10px\",\r\n                fontSize: \"16px\",\r\n                border: \"1px solid #ccc\",\r\n                borderRadius: \"6px\",\r\n                backgroundColor: \"#fff\",\r\n                outline: \"none\",\r\n                transition: \"border-color 0.2s\",\r\n              }}\r\n              onFocus={(e) => (e.target.style.borderColor = \"#1976d2\")}\r\n              onBlur={(e) => (e.target.style.borderColor = \"#ccc\")}\r\n            >\r\n              {(activeModules && activeModules.length > 0 ? activeModules : [\"EMS\", \"Fire\", \"Police\", \"Medical\"]).map((module) => (\r\n                <option key={module} value={module}>\r\n                  {module}\r\n                </option>\r\n              ))}\r\n            </select>\r\n          </div>\r\n          \r\n\r\n        </section>\r\n\r\n        {/* Customize Launch Form */}\r\n        <section className=\"settings-section\">\r\n          <h2 className=\"settings-section-header\">\r\n            Customize Launch Form for {selectedModule}\r\n          </h2>\r\n          <div className=\"settings-section-content\">\r\n          <div style={{ \r\n            marginBottom: \"20px\",\r\n            backgroundColor: \"#fff\",\r\n            padding: \"15px\",\r\n            borderRadius: \"8px\",\r\n            border: \"1px solid #e0e0e0\"\r\n          }}>\r\n            <h3\r\n              style={{\r\n                fontSize: \"18px\",\r\n                fontWeight: \"500\",\r\n                color: \"#555\",\r\n                marginBottom: \"15px\",\r\n                display: \"flex\",\r\n                justifyContent: \"space-between\",\r\n                alignItems: \"center\",\r\n                borderBottom: \"1px solid #eee\",\r\n                paddingBottom: \"10px\"\r\n              }}\r\n            >\r\n              <span>Current Fields</span>\r\n              <span style={{ fontSize: \"14px\", color: \"#777\" }}>\r\n                {allFormConfig.length} field{allFormConfig.length !== 1 ? 's' : ''}\r\n              </span>\r\n            </h3>\r\n            {allFormConfig.length > 0 ? (\r\n              <ul style={{ listStyle: \"none\", padding: 0 }}>\r\n                {allFormConfig.map((field, index) => (\r\n                  <li\r\n                    key={index}\r\n                    style={{\r\n                      display: \"flex\",\r\n                      alignItems: \"center\",\r\n                      gap: \"12px\",\r\n                      marginBottom: \"15px\",\r\n                      flexWrap: \"wrap\",\r\n                      padding: \"10px\",\r\n                      backgroundColor: index % 2 === 0 ? \"#f9f9f9\" : \"#fff\",\r\n                      borderRadius: \"6px\",\r\n                      border: \"1px solid #eee\"\r\n                    }}\r\n                  >\r\n                    <div style={{ flex: \"1 1 200px\" }}>\r\n                      <label\r\n                        style={{\r\n                          display: \"block\",\r\n                          fontSize: \"12px\",\r\n                          fontWeight: \"500\",\r\n                          color: \"#666\",\r\n                          marginBottom: \"4px\",\r\n                        }}\r\n                      >\r\n                        Field Label\r\n                      </label>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={field.label}\r\n                        onChange={(e) =>\r\n                          handleFieldChange(index, \"label\", e.target.value)\r\n                        }\r\n                        placeholder=\"Field Label\"\r\n                        style={{\r\n                          width: \"100%\",\r\n                          padding: \"8px 10px\",\r\n                          fontSize: \"14px\",\r\n                          border: \"1px solid #ccc\",\r\n                          borderRadius: \"6px\",\r\n                          outline: \"none\",\r\n                          transition: \"border-color 0.2s, box-shadow 0.2s\",\r\n                        }}\r\n                        onFocus={(e) => {\r\n                          e.target.style.borderColor = \"#1976d2\";\r\n                          e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\r\n                        }}\r\n                        onBlur={(e) => {\r\n                          e.target.style.borderColor = \"#ccc\";\r\n                          e.target.style.boxShadow = \"none\";\r\n                        }}\r\n                      />\r\n                    </div>\r\n                    \r\n                    <div style={{ flex: \"1 1 150px\" }}>\r\n                      <label\r\n                        style={{\r\n                          display: \"block\",\r\n                          fontSize: \"12px\",\r\n                          fontWeight: \"500\",\r\n                          color: \"#666\",\r\n                          marginBottom: \"4px\",\r\n                        }}\r\n                      >\r\n                        Field Type\r\n                      </label>\r\n                      <select\r\n                        value={field.type}\r\n                        onChange={(e) =>\r\n                          handleFieldChange(index, \"type\", e.target.value)\r\n                        }\r\n                        style={{\r\n                          width: \"100%\",\r\n                          padding: \"8px 10px\",\r\n                          fontSize: \"14px\",\r\n                          border: \"1px solid #ccc\",\r\n                          borderRadius: \"6px\",\r\n                          backgroundColor: \"#fff\",\r\n                          outline: \"none\",\r\n                          transition: \"border-color 0.2s, box-shadow 0.2s\",\r\n                        }}\r\n                        onFocus={(e) => {\r\n                          e.target.style.borderColor = \"#1976d2\";\r\n                          e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\r\n                        }}\r\n                        onBlur={(e) => {\r\n                          e.target.style.borderColor = \"#ccc\";\r\n                          e.target.style.boxShadow = \"none\";\r\n                        }}\r\n                      >\r\n                        <option value=\"text\">Text</option>\r\n                        <option value=\"textarea\">Textarea</option>\r\n                        <option value=\"select\">Select</option>\r\n                        <option value=\"checkbox\">Checkbox</option>\r\n                        <option value=\"autocomplete\">Autocomplete</option>\r\n                      </select>\r\n                    </div>\r\n                    {field.type === \"select\" && (\r\n                      <div style={{ flex: \"1 1 200px\" }}>\r\n                        <label\r\n                          style={{\r\n                            display: \"block\",\r\n                            fontSize: \"12px\",\r\n                            fontWeight: \"500\",\r\n                            color: \"#666\",\r\n                            marginBottom: \"4px\",\r\n                          }}\r\n                        >\r\n                          Options (comma-separated)\r\n                        </label>\r\n                        <input\r\n                          type=\"text\"\r\n                          value={(field.options || []).join(\",\")}\r\n                          onChange={(e) =>\r\n                            handleFieldChange(\r\n                              index,\r\n                              \"options\",\r\n                              e.target.value.split(\",\").map((opt) => opt.trim())\r\n                            )\r\n                          }\r\n                          placeholder=\"Option1, Option2, Option3\"\r\n                          style={{\r\n                            width: \"100%\",\r\n                            padding: \"8px 10px\",\r\n                            fontSize: \"14px\",\r\n                            border: \"1px solid #ccc\",\r\n                            borderRadius: \"6px\",\r\n                            outline: \"none\",\r\n                            transition: \"border-color 0.2s, box-shadow 0.2s\",\r\n                          }}\r\n                          onFocus={(e) => {\r\n                            e.target.style.borderColor = \"#1976d2\";\r\n                            e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\r\n                          }}\r\n                          onBlur={(e) => {\r\n                            e.target.style.borderColor = \"#ccc\";\r\n                            e.target.style.boxShadow = \"none\";\r\n                          }}\r\n                        />\r\n                      </div>\r\n                    )}\r\n                    <div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}>\r\n                      <label\r\n                        style={{\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                          gap: \"5px\",\r\n                          fontSize: \"14px\",\r\n                          color: \"#555\",\r\n                          whiteSpace: \"nowrap\",\r\n                        }}\r\n                      >\r\n                        <input\r\n                          type=\"checkbox\"\r\n                          checked={field.required}\r\n                          onChange={(e) =>\r\n                            handleFieldChange(index, \"required\", e.target.checked)\r\n                          }\r\n                          style={{\r\n                            width: \"16px\",\r\n                            height: \"16px\",\r\n                            cursor: \"pointer\",\r\n                            accentColor: \"#1976d2\",\r\n                          }}\r\n                        />\r\n                        Required\r\n                      </label>\r\n                      \r\n                      {defaultFields.some(defaultField => defaultField.name === field.name) ? (\r\n                        <span\r\n                          style={{\r\n                            padding: \"4px 8px\",\r\n                            fontSize: \"12px\",\r\n                            color: \"#666\",\r\n                            backgroundColor: \"#f5f5f5\",\r\n                            border: \"1px solid #ddd\",\r\n                            borderRadius: \"4px\",\r\n                            fontStyle: \"italic\",\r\n                          }}\r\n                        >\r\n                          Default Field\r\n                        </span>\r\n                      ) : (\r\n                        <button\r\n                          onClick={() => handleRemoveField(index)}\r\n                          style={{\r\n                            padding: \"4px 10px\",\r\n                            fontSize: \"13px\",\r\n                            color: \"#fff\",\r\n                            backgroundColor: \"#d32f2f\",\r\n                            border: \"none\",\r\n                            borderRadius: \"4px\",\r\n                            cursor: \"pointer\",\r\n                            transition: \"background-color 0.2s\",\r\n                          }}\r\n                          onMouseOver={(e) =>\r\n                            (e.target.style.backgroundColor = \"#b71c1c\")\r\n                          }\r\n                          onMouseOut={(e) =>\r\n                            (e.target.style.backgroundColor = \"#d32f2f\")\r\n                          }\r\n                        >\r\n                          Remove\r\n                        </button>\r\n                      )}\r\n                    </div>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            ) : (\r\n              <p style={{ color: \"#777\", fontSize: \"16px\" }}>\r\n                No custom fields defined.\r\n              </p>\r\n            )}\r\n          </div>\r\n          <div style={{ \r\n            marginTop: \"25px\",\r\n            backgroundColor: \"#fff\",\r\n            padding: \"15px\",\r\n            borderRadius: \"8px\",\r\n            border: \"1px solid #e0e0e0\" \r\n          }}>\r\n            <h3\r\n              style={{\r\n                fontSize: \"18px\",\r\n                fontWeight: \"500\",\r\n                color: \"#555\",\r\n                marginBottom: \"15px\",\r\n                borderBottom: \"1px solid #eee\",\r\n                paddingBottom: \"10px\",\r\n              }}\r\n            >\r\n              Add New Field\r\n            </h3>\r\n            <div style={{ display: \"flex\", flexWrap: \"wrap\", gap: \"15px\" }}>\r\n              <div style={{ flex: \"1 1 200px\" }}>\r\n                <label\r\n                  style={{\r\n                    display: \"block\",\r\n                    fontSize: \"12px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#666\",\r\n                    marginBottom: \"4px\",\r\n                  }}\r\n                >\r\n                  Field Name\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newField.name}\r\n                  onChange={(e) =>\r\n                    setNewField({ ...newField, name: e.target.value })\r\n                  }\r\n                  placeholder=\"Field Name (e.g., custom_field_1)\"\r\n                  style={{\r\n                    width: \"100%\",\r\n                    padding: \"8px 10px\",\r\n                    fontSize: \"14px\",\r\n                    border: \"1px solid #ccc\",\r\n                    borderRadius: \"6px\",\r\n                    outline: \"none\",\r\n                    transition: \"border-color 0.2s, box-shadow 0.2s\",\r\n                  }}\r\n                  onFocus={(e) => {\r\n                    e.target.style.borderColor = \"#1976d2\";\r\n                    e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\r\n                  }}\r\n                  onBlur={(e) => {\r\n                    e.target.style.borderColor = \"#ccc\";\r\n                    e.target.style.boxShadow = \"none\";\r\n                  }}\r\n                />\r\n              </div>\r\n              \r\n              <div style={{ flex: \"1 1 200px\" }}>\r\n                <label\r\n                  style={{\r\n                    display: \"block\",\r\n                    fontSize: \"12px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#666\",\r\n                    marginBottom: \"4px\",\r\n                  }}\r\n                >\r\n                  Field Label\r\n                </label>\r\n                <input\r\n                  type=\"text\"\r\n                  value={newField.label}\r\n                  onChange={(e) =>\r\n                    setNewField({ ...newField, label: e.target.value })\r\n                  }\r\n                  placeholder=\"Field Label\"\r\n                  style={{\r\n                    width: \"100%\",\r\n                    padding: \"8px 10px\",\r\n                    fontSize: \"14px\",\r\n                    border: \"1px solid #ccc\",\r\n                    borderRadius: \"6px\",\r\n                    outline: \"none\",\r\n                    transition: \"border-color 0.2s, box-shadow 0.2s\",\r\n                  }}\r\n                  onFocus={(e) => {\r\n                    e.target.style.borderColor = \"#1976d2\";\r\n                    e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\r\n                  }}\r\n                  onBlur={(e) => {\r\n                    e.target.style.borderColor = \"#ccc\";\r\n                    e.target.style.boxShadow = \"none\";\r\n                  }}\r\n                />\r\n              </div>\r\n              \r\n              <div style={{ flex: \"1 1 150px\" }}>\r\n                <label\r\n                  style={{\r\n                    display: \"block\",\r\n                    fontSize: \"12px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#666\",\r\n                    marginBottom: \"4px\",\r\n                  }}\r\n                >\r\n                  Field Type\r\n                </label>\r\n                <select\r\n                  value={newField.type}\r\n                  onChange={(e) =>\r\n                    setNewField({ ...newField, type: e.target.value })\r\n                  }\r\n                  style={{\r\n                    width: \"100%\",\r\n                    padding: \"8px 10px\",\r\n                    fontSize: \"14px\",\r\n                    border: \"1px solid #ccc\",\r\n                    borderRadius: \"6px\",\r\n                    backgroundColor: \"#fff\",\r\n                    outline: \"none\",\r\n                    transition: \"border-color 0.2s, box-shadow 0.2s\",\r\n                  }}\r\n                  onFocus={(e) => {\r\n                    e.target.style.borderColor = \"#1976d2\";\r\n                    e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\r\n                  }}\r\n                  onBlur={(e) => {\r\n                    e.target.style.borderColor = \"#ccc\";\r\n                    e.target.style.boxShadow = \"none\";\r\n                  }}\r\n                >\r\n                <option value=\"text\">Text</option>\r\n                <option value=\"textarea\">Textarea</option>\r\n                <option value=\"select\">Select</option>\r\n                <option value=\"checkbox\">Checkbox</option>\r\n                <option value=\"autocomplete\">Autocomplete</option>\r\n              </select>\r\n              </div>\r\n              \r\n              {newField.type === \"select\" && (\r\n                <div style={{ flex: \"1 1 200px\" }}>\r\n                  <label\r\n                    style={{\r\n                      display: \"block\",\r\n                      fontSize: \"12px\",\r\n                      fontWeight: \"500\",\r\n                      color: \"#666\",\r\n                      marginBottom: \"4px\",\r\n                    }}\r\n                  >\r\n                    Options (comma-separated)\r\n                  </label>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={newField.options.join(\",\")}\r\n                    onChange={(e) =>\r\n                      setNewField({\r\n                        ...newField,\r\n                        options: e.target.value\r\n                          .split(\",\")\r\n                          .map((opt) => opt.trim()),\r\n                      })\r\n                    }\r\n                    placeholder=\"Option1, Option2, Option3\"\r\n                    style={{\r\n                      width: \"100%\",\r\n                      padding: \"8px 10px\",\r\n                      fontSize: \"14px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      transition: \"border-color 0.2s, box-shadow 0.2s\",\r\n                    }}\r\n                    onFocus={(e) => {\r\n                      e.target.style.borderColor = \"#1976d2\";\r\n                      e.target.style.boxShadow = \"0 0 0 2px rgba(25, 118, 210, 0.1)\";\r\n                    }}\r\n                    onBlur={(e) => {\r\n                      e.target.style.borderColor = \"#ccc\";\r\n                      e.target.style.boxShadow = \"none\";\r\n                    }}\r\n                  />\r\n                </div>\r\n              )}\r\n              \r\n              <div style={{ display: \"flex\", alignItems: \"center\", gap: \"10px\" }}>\r\n                <label\r\n                  style={{\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                    gap: \"5px\",\r\n                    fontSize: \"14px\",\r\n                    color: \"#555\",\r\n                    whiteSpace: \"nowrap\",\r\n                  }}\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={newField.required}\r\n                    onChange={(e) =>\r\n                      setNewField({ ...newField, required: e.target.checked })\r\n                    }\r\n                    style={{ \r\n                      width: \"16px\", \r\n                      height: \"16px\", \r\n                      cursor: \"pointer\",\r\n                      accentColor: \"#1976d2\",\r\n                    }}\r\n                  />\r\n                  Required\r\n                </label>\r\n                \r\n                <button\r\n                  onClick={handleAddField}\r\n                  className=\"btn-primary\"\r\n                  style={{ marginLeft: \"auto\" }}\r\n                >\r\n                  <FaPlus size={12} />\r\n                  Add Field\r\n                </button>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Save Form Configuration Button */}\r\n            <div style={{\r\n              marginTop: \"20px\",\r\n              paddingTop: \"20px\",\r\n              borderTop: \"1px solid #e0e0e0\",\r\n              display: \"flex\",\r\n              justifyContent: \"center\"\r\n            }}>\r\n              <button\r\n                onClick={handleSaveFormConfig}\r\n                className=\"btn-primary\"\r\n                style={{\r\n                  padding: \"12px 30px\",\r\n                  fontSize: \"16px\",\r\n                  fontWeight: \"600\"\r\n                }}\r\n              >\r\n                💾 Save Form Configuration\r\n              </button>\r\n            </div>\r\n          </div>\r\n          </div>\r\n        </section>\r\n        {/* Manage Main Locations */}\r\n        <section className=\"settings-section\">\r\n          <h2 className=\"settings-section-header\">\r\n            Manage Main Locations\r\n          </h2>\r\n          <div className=\"settings-section-content\">\r\n\r\n\r\n          {/* Side-by-side layout */}\r\n          <div className=\"locations-container\">\r\n            {/* Left side - Current Locations List */}\r\n            <div className=\"locations-list-container\">\r\n              <h3\r\n                style={{\r\n                  fontSize: \"18px\",\r\n                  fontWeight: \"500\",\r\n                  color: \"#555\",\r\n                  marginBottom: \"15px\",\r\n                }}\r\n              >\r\n                Current Locations ({locations?.length || 0})\r\n              </h3>\r\n              {console.log(\"locations:\", locations)}\r\n              \r\n              <div className=\"locations-list\" style={{ \r\n                border: \"1px solid #ddd\", \r\n                borderRadius: \"8px\", \r\n                backgroundColor: \"#fff\",\r\n                maxHeight: \"400px\",\r\n                overflowY: \"auto\"\r\n              }}>\r\n                {locations && locations.length > 0 ? (\r\n                  <div>\r\n                    {[...locations].map((location, index) => (\r\n                      <div\r\n                        key={index}\r\n                        style={{\r\n                          padding: \"15px\",\r\n                          borderBottom: index < locations.length - 1 ? \"1px solid #eee\" : \"none\",\r\n                          backgroundColor: \"#fff\",\r\n                          transition: \"background-color 0.2s\",\r\n                        }}\r\n                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = \"#f8f9fa\"}\r\n                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = \"#fff\"}\r\n                      >\r\n                        <div>\r\n                            <div style={{ display: \"flex\", justifyContent: \"space-between\", alignItems: \"flex-start\", marginBottom: \"8px\" }}>\r\n                              <div style={{ flex: 1 }}>\r\n                                <div style={{ fontSize: \"16px\", color: \"#333\", fontWeight: \"600\", marginBottom: \"4px\" }}>\r\n                                  {location.commonName}\r\n                                </div>\r\n                                <div style={{ fontSize: \"14px\", color: \"#666\", lineHeight: \"1.4\" }}>\r\n                                  {location.address}\r\n                                  {location.city && <><br />{location.city}{location.state && `, ${location.state}`}{location.zip && ` ${location.zip}`}</>}\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                            <div style={{ display: \"flex\", gap: \"8px\", flexWrap: \"wrap\" }}>\r\n                              <button\r\n                                onClick={() => handleEditLocation(index)}\r\n                                style={{\r\n                                  padding: \"6px 12px\",\r\n                                  fontSize: \"12px\",\r\n                                  color: \"#1976d2\",\r\n                                  backgroundColor: \"#e3f2fd\",\r\n                                  border: \"1px solid #1976d2\",\r\n                                  borderRadius: \"4px\",\r\n                                  cursor: \"pointer\",\r\n                                  transition: \"all 0.2s\",\r\n                                }}\r\n                                onMouseOver={(e) => {\r\n                                  e.target.style.backgroundColor = \"#1976d2\";\r\n                                  e.target.style.color = \"#fff\";\r\n                                }}\r\n                                onMouseOut={(e) => {\r\n                                  e.target.style.backgroundColor = \"#e3f2fd\";\r\n                                  e.target.style.color = \"#1976d2\";\r\n                                }}\r\n                              >\r\n                                Edit\r\n                              </button>\r\n                              <button\r\n                                onClick={() => handleRemoveLocation(index)}\r\n                                style={{\r\n                                  padding: \"6px 12px\",\r\n                                  fontSize: \"12px\",\r\n                                  color: \"#d32f2f\",\r\n                                  backgroundColor: \"#ffebee\",\r\n                                  border: \"1px solid #d32f2f\",\r\n                                  borderRadius: \"4px\",\r\n                                  cursor: \"pointer\",\r\n                                  transition: \"all 0.2s\",\r\n                                }}\r\n                                onMouseOver={(e) => {\r\n                                  e.target.style.backgroundColor = \"#d32f2f\";\r\n                                  e.target.style.color = \"#fff\";\r\n                                }}\r\n                                onMouseOut={(e) => {\r\n                                  e.target.style.backgroundColor = \"#ffebee\";\r\n                                  e.target.style.color = \"#d32f2f\";\r\n                                }}\r\n                              >\r\n                                Delete\r\n                              </button>\r\n                            </div>\r\n                        </div>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                ) : (\r\n                  <div style={{\r\n                    textAlign: \"center\",\r\n                    color: \"#777\",\r\n                    fontSize: \"16px\",\r\n                    padding: \"40px 20px\",\r\n                    display: \"flex\",\r\n                    flexDirection: \"column\",\r\n                    justifyContent: \"center\",\r\n                    alignItems: \"center\",\r\n                    height: \"100%\"\r\n                  }}>\r\n                    <div style={{ fontSize: \"48px\", marginBottom: \"10px\" }}>📍</div>\r\n                    <p>No locations defined yet.</p>\r\n                    <p style={{ fontSize: \"14px\", color: \"#999\" }}>Add your first location using the form on the right.</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Right side - Add/Edit Location Form */}\r\n            <div className=\"locations-form-container\">\r\n              <h3\r\n                style={{\r\n                  fontSize: \"18px\",\r\n                  fontWeight: \"500\",\r\n                  color: \"#555\",\r\n                  marginBottom: \"15px\",\r\n                }}\r\n              >\r\n                {editingLocation !== null ? 'Edit Location' : 'Add New Location'}\r\n              </h3>\r\n              <div className=\"locations-form\">\r\n                <div style={{ display: \"grid\", gridTemplateColumns: \"1fr\", gap: \"15px\", marginBottom: \"15px\", flex: \"1\" }}>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editingLocation !== null ? editLocation.commonName : newLocation.commonName}\r\n                    onChange={(e) => {\r\n                      if (editingLocation !== null) {\r\n                        setEditLocation({ ...editLocation, commonName: e.target.value });\r\n                      } else {\r\n                        setNewLocation({ ...newLocation, commonName: e.target.value });\r\n                      }\r\n                    }}\r\n                    placeholder=\"Common Name (e.g., Main Hospital)\"\r\n                    style={{\r\n                      padding: \"12px\",\r\n                      fontSize: \"16px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      transition: \"border-color 0.2s\",\r\n                    }}\r\n                    onFocus={(e) => (e.target.style.borderColor = \"#1976d2\")}\r\n                    onBlur={(e) => (e.target.style.borderColor = \"#ccc\")}\r\n                  />\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editingLocation !== null ? editLocation.address : newLocation.address}\r\n                    onChange={(e) => {\r\n                      if (editingLocation !== null) {\r\n                        setEditLocation({ ...editLocation, address: e.target.value });\r\n                      } else {\r\n                        setNewLocation({ ...newLocation, address: e.target.value });\r\n                      }\r\n                    }}\r\n                    placeholder=\"Street Address\"\r\n                    style={{\r\n                      padding: \"12px\",\r\n                      fontSize: \"16px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      transition: \"border-color 0.2s\",\r\n                    }}\r\n                    onFocus={(e) => (e.target.style.borderColor = \"#1976d2\")}\r\n                    onBlur={(e) => (e.target.style.borderColor = \"#ccc\")}\r\n                  />\r\n                </div>\r\n                <div style={{ display: \"grid\", gridTemplateColumns: \"2fr 1fr 1fr\", gap: \"10px\", marginBottom: \"20px\" }}>\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editingLocation !== null ? editLocation.city : newLocation.city}\r\n                    onChange={(e) => {\r\n                      if (editingLocation !== null) {\r\n                        setEditLocation({ ...editLocation, city: e.target.value });\r\n                      } else {\r\n                        setNewLocation({ ...newLocation, city: e.target.value });\r\n                      }\r\n                    }}\r\n                    placeholder=\"City\"\r\n                    style={{\r\n                      padding: \"12px\",\r\n                      fontSize: \"16px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      transition: \"border-color 0.2s\",\r\n                    }}\r\n                    onFocus={(e) => (e.target.style.borderColor = \"#1976d2\")}\r\n                    onBlur={(e) => (e.target.style.borderColor = \"#ccc\")}\r\n                  />\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editingLocation !== null ? editLocation.state : newLocation.state}\r\n                    onChange={(e) => {\r\n                      if (editingLocation !== null) {\r\n                        setEditLocation({ ...editLocation, state: e.target.value });\r\n                      } else {\r\n                        setNewLocation({ ...newLocation, state: e.target.value });\r\n                      }\r\n                    }}\r\n                    placeholder=\"State\"\r\n                    style={{\r\n                      padding: \"12px\",\r\n                      fontSize: \"16px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      transition: \"border-color 0.2s\",\r\n                    }}\r\n                    onFocus={(e) => (e.target.style.borderColor = \"#1976d2\")}\r\n                    onBlur={(e) => (e.target.style.borderColor = \"#ccc\")}\r\n                  />\r\n                  <input\r\n                    type=\"text\"\r\n                    value={editingLocation !== null ? editLocation.zip : newLocation.zip}\r\n                    onChange={(e) => {\r\n                      if (editingLocation !== null) {\r\n                        setEditLocation({ ...editLocation, zip: e.target.value });\r\n                      } else {\r\n                        setNewLocation({ ...newLocation, zip: e.target.value });\r\n                      }\r\n                    }}\r\n                    placeholder=\"ZIP Code\"\r\n                    style={{\r\n                      padding: \"12px\",\r\n                      fontSize: \"16px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      transition: \"border-color 0.2s\",\r\n                    }}\r\n                    onFocus={(e) => (e.target.style.borderColor = \"#1976d2\")}\r\n                    onBlur={(e) => (e.target.style.borderColor = \"#ccc\")}\r\n                  />\r\n                </div>\r\n                <div style={{ marginTop: \"auto\", display: \"flex\", gap: \"10px\" }}>\r\n                  {editingLocation !== null ? (\r\n                    <>\r\n                      <button\r\n                        onClick={handleSaveEdit}\r\n                        style={{\r\n                          flex: \"1\",\r\n                          padding: \"12px 20px\",\r\n                          fontSize: \"16px\",\r\n                          fontWeight: \"600\",\r\n                          color: \"#fff\",\r\n                          backgroundColor: \"#4caf50\",\r\n                          border: \"none\",\r\n                          borderRadius: \"6px\",\r\n                          cursor: \"pointer\",\r\n                          transition: \"background-color 0.2s\",\r\n                        }}\r\n                        onMouseOver={(e) => (e.target.style.backgroundColor = \"#45a049\")}\r\n                        onMouseOut={(e) => (e.target.style.backgroundColor = \"#4caf50\")}\r\n                      >\r\n                        Save Changes\r\n                      </button>\r\n                      <button\r\n                        onClick={handleCancelEdit}\r\n                        style={{\r\n                          flex: \"1\",\r\n                          padding: \"12px 20px\",\r\n                          fontSize: \"16px\",\r\n                          fontWeight: \"600\",\r\n                          color: \"#666\",\r\n                          backgroundColor: \"#f5f5f5\",\r\n                          border: \"1px solid #ddd\",\r\n                          borderRadius: \"6px\",\r\n                          cursor: \"pointer\",\r\n                          transition: \"background-color 0.2s\",\r\n                        }}\r\n                        onMouseOver={(e) => (e.target.style.backgroundColor = \"#e0e0e0\")}\r\n                        onMouseOut={(e) => (e.target.style.backgroundColor = \"#f5f5f5\")}\r\n                      >\r\n                        Cancel\r\n                      </button>\r\n                    </>\r\n                  ) : (\r\n                    <button\r\n                      onClick={handleAddLocation}\r\n                      style={{\r\n                        width: \"100%\",\r\n                        padding: \"12px 20px\",\r\n                        fontSize: \"16px\",\r\n                        fontWeight: \"600\",\r\n                        color: \"#fff\",\r\n                        backgroundColor: \"#1976d2\",\r\n                        border: \"none\",\r\n                        borderRadius: \"6px\",\r\n                        cursor: \"pointer\",\r\n                        transition: \"background-color 0.2s\",\r\n                      }}\r\n                      onMouseOver={(e) => (e.target.style.backgroundColor = \"#1565c0\")}\r\n                      onMouseOut={(e) => (e.target.style.backgroundColor = \"#1976d2\")}\r\n                    >\r\n                      Add Location\r\n                    </button>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* Manage Company Code */}\r\n        <section className=\"settings-section\">\r\n          <h2 className=\"settings-section-header\">\r\n            Manage Company Code\r\n          </h2>\r\n          <div className=\"settings-section-content\">\r\n\r\n\r\n\r\n          <div\r\n            style={{\r\n              display: \"flex\",\r\n              flexDirection: \"column\",\r\n              gap: \"15px\",\r\n              marginTop: \"20px\",\r\n            }}\r\n          >\r\n            {/* Row with Name, Code, and Generate Button */}\r\n            <div\r\n              style={{\r\n                display: \"flex\",\r\n                gap: \"10px\",\r\n                alignItems: \"flex-start\",\r\n                marginTop: \"10px\",\r\n              }}\r\n            >\r\n              {/* Name Field */}\r\n              <div style={{ flex: \"1\" }}>\r\n                <label\r\n                  htmlFor=\"companyName\"\r\n                  style={{\r\n                    display: \"block\",\r\n                    fontSize: \"16px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#555\",\r\n                    marginBottom: \"8px\",\r\n                  }}\r\n                >\r\n                  Company Name <span style={{ color: \"red\" }}>* </span>\r\n                </label>\r\n                <input\r\n                  id=\"companyName\"\r\n                  type=\"text\"\r\n                  value={name}\r\n                  onChange={(e) => setName(e.target.value)}\r\n                  placeholder=\"Enter company name\"\r\n                  style={{\r\n                    width: \"100%\",\r\n                    padding: \"10px\",\r\n                    fontSize: \"16px\",\r\n                    border: \"1px solid #ccc\",\r\n                    borderRadius: \"6px\",\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              {/* Code Input */}\r\n              <div style={{ flex: \"1\" }}>\r\n                <label\r\n                  htmlFor=\"companyCode\"\r\n                  style={{\r\n                    display: \"block\",\r\n                    fontSize: \"16px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#555\",\r\n                    marginBottom: \"8px\",\r\n                  }}\r\n                >\r\n                  Company Code <span style={{ color: \"red\" }}>* </span>\r\n                </label>\r\n                <input\r\n                  id=\"companyCode\"\r\n                  type=\"text\"\r\n                  value={newCode}\r\n                  onChange={(e) => setNewCode(e.target.value)}\r\n                  placeholder=\"Enter or generate a code\"\r\n                  style={{\r\n                    width: \"100%\",\r\n                    padding: \"10px\",\r\n                    fontSize: \"16px\",\r\n                    border: \"1px solid #ccc\",\r\n                    borderRadius: \"6px\",\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              {/* Generate Button */}\r\n              <button\r\n                onClick={generateRandomCode}\r\n                style={{\r\n                  marginTop: \"30px\", // Align button with inputs\r\n                  padding: \"10px 20px\",\r\n                  fontSize: \"16px\",\r\n                  color: \"#fff\",\r\n                  backgroundColor: \"#1976d2\",\r\n                  border: \"none\",\r\n                  borderRadius: \"6px\",\r\n                  cursor: \"pointer\",\r\n                  whiteSpace: \"nowrap\",\r\n                }}\r\n              >\r\n                <GiCycle size={20} />\r\n              </button>\r\n            </div>\r\n\r\n            {/* Description Field */}\r\n            <div>\r\n              <label\r\n                htmlFor=\"companyDescription\"\r\n                style={{\r\n                  display: \"block\",\r\n                  fontSize: \"16px\",\r\n                  fontWeight: \"500\",\r\n                  color: \"#555\",\r\n                  marginBottom: \"8px\",\r\n                }}\r\n              >\r\n                Description\r\n                <span style={{ fontSize: \"12px\", color: \"#777\" }}>\r\n                  (optional)\r\n                </span>\r\n              </label>\r\n              <textarea\r\n                id=\"companyDescription\"\r\n                value={description}\r\n                onChange={(e) => setDescription(e.target.value)}\r\n                rows=\"4\"\r\n                placeholder=\"Enter company description\"\r\n                style={{\r\n                  width: \"100%\",\r\n                  padding: \"10px\",\r\n                  fontSize: \"16px\",\r\n                  border: \"1px solid #ccc\",\r\n                  borderRadius: \"6px\",\r\n                  resize: \"none\",\r\n                }}\r\n              ></textarea>\r\n            </div>\r\n\r\n            {/* Save Button */}\r\n            <div style={{ marginTop: \"10px\" }}>\r\n              <button\r\n                onClick={saveCode}\r\n                style={{\r\n                  padding: \"10px 20px\",\r\n                  fontSize: \"16px\",\r\n                  color: \"#fff\",\r\n                  backgroundColor: \"#4caf50\",\r\n                  border: \"none\",\r\n                  borderRadius: \"6px\",\r\n                  cursor: \"pointer\",\r\n                }}\r\n              >\r\n                Save Code\r\n              </button>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Display Saved Company Codes */}\r\n          <div\r\n            style={{\r\n              marginTop: \"20px\",\r\n              backgroundColor: \"#fff\",\r\n              padding: \"15px\",\r\n              borderRadius: \"8px\",\r\n              border: \"1px solid #e0e0e0\",\r\n            }}\r\n          >\r\n            <h3\r\n              style={{\r\n                fontSize: \"18px\",\r\n                fontWeight: \"500\",\r\n                color: \"#555\",\r\n                marginBottom: \"15px\",\r\n                borderBottom: \"1px solid #eee\",\r\n                paddingBottom: \"10px\",\r\n              }}\r\n            >\r\n              Saved Company Codes ({savedCodes.length})\r\n            </h3>\r\n            \r\n            {savedCodes && savedCodes.length > 0 ? (\r\n              <div style={{ maxHeight: \"300px\", overflowY: \"auto\" }}>\r\n                {savedCodes.map((code, index) => (\r\n                  <div\r\n                    key={index}\r\n                    style={{\r\n                      padding: \"8px 12px\",\r\n                      borderBottom: index < savedCodes.length - 1 ? \"1px solid #eee\" : \"none\",\r\n                      display: \"flex\",\r\n                      justifyContent: \"space-between\",\r\n                      alignItems: \"center\",\r\n                      backgroundColor: \"#fff\",\r\n                      transition: \"background-color 0.2s\",\r\n                      minHeight: \"auto\"\r\n                    }}\r\n                    onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = \"#f8f9fa\")}\r\n                    onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = \"#fff\")}\r\n                  >\r\n                    <div style={{ flex: 1 }}>\r\n                      <div style={{ fontSize: \"14px\", fontWeight: \"500\", color: \"#333\", marginBottom: \"2px\" }}>\r\n                        {code.name}\r\n                      </div>\r\n                      <div style={{ fontSize: \"13px\", color: \"#666\" }}>\r\n                        Code: <span style={{ fontWeight: \"500\" }}>{code.code}</span>\r\n                      </div>\r\n                      {code.description && (\r\n                        <div style={{ fontSize: \"12px\", color: \"#777\", marginTop: \"2px\" }}>\r\n                          {code.description}\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                    <div style={{ display: \"flex\", gap: \"6px\", alignItems: \"center\" }}>\r\n                      <button\r\n                        onClick={() => handleCopyCode(code.code)}\r\n                        style={{\r\n                          padding: \"4px 8px\",\r\n                          fontSize: \"11px\",\r\n                          color: \"#1976d2\",\r\n                          backgroundColor: \"#e3f2fd\",\r\n                          border: \"1px solid #1976d2\",\r\n                          borderRadius: \"4px\",\r\n                          cursor: \"pointer\",\r\n                          transition: \"all 0.2s\",\r\n                        }}\r\n                        onMouseOver={(e) => {\r\n                          e.target.style.backgroundColor = \"#1976d2\";\r\n                          e.target.style.color = \"#fff\";\r\n                        }}\r\n                        onMouseOut={(e) => {\r\n                          e.target.style.backgroundColor = \"#e3f2fd\";\r\n                          e.target.style.color = \"#1976d2\";\r\n                        }}\r\n                      >\r\n                        Copy\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleDeleteCode(index, code.id)}\r\n                        style={{\r\n                          padding: \"4px 8px\",\r\n                          fontSize: \"11px\",\r\n                          color: \"#d32f2f\",\r\n                          backgroundColor: \"#ffebee\",\r\n                          border: \"1px solid #d32f2f\",\r\n                          borderRadius: \"4px\",\r\n                          cursor: \"pointer\",\r\n                          transition: \"all 0.2s\",\r\n                        }}\r\n                        onMouseOver={(e) => {\r\n                          e.target.style.backgroundColor = \"#d32f2f\";\r\n                          e.target.style.color = \"#fff\";\r\n                        }}\r\n                        onMouseOut={(e) => {\r\n                          e.target.style.backgroundColor = \"#ffebee\";\r\n                          e.target.style.color = \"#d32f2f\";\r\n                        }}\r\n                      >\r\n                        Delete\r\n                      </button>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            ) : (\r\n              <div\r\n                style={{\r\n                  textAlign: \"center\",\r\n                  color: \"#777\",\r\n                  fontSize: \"16px\",\r\n                  padding: \"30px 20px\",\r\n                }}\r\n              >\r\n                <p>No company codes saved yet.</p>\r\n                <p style={{ fontSize: \"14px\", color: \"#999\" }}>\r\n                  Add your first company code using the form above.\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          </div>\r\n        </section>\r\n\r\n        {/* AI Agent Preconditions Section - Super Admin Only */}\r\n        {userId === 2 && (\r\n          <section className=\"settings-section\">\r\n            <h2\r\n              className=\"settings-section-header\"\r\n              style={{\r\n                cursor: \"pointer\",\r\n                display: \"flex\",\r\n                alignItems: \"center\",\r\n                justifyContent: \"space-between\",\r\n                padding: \"12px 0\",\r\n                margin: \"0\",\r\n                fontSize: \"18px\",\r\n                fontWeight: \"600\",\r\n                color: \"#fff\",\r\n                borderBottom: \"1px solid rgba(255, 255, 255, 0.1)\",\r\n                minHeight: \"auto\"\r\n              }}\r\n              onClick={() => setIsAiSettingsExpanded(!isAiSettingsExpanded)}\r\n            >\r\n              <span>AI Agent Preconditions</span>\r\n              <span style={{\r\n                fontSize: \"16px\",\r\n                transition: \"transform 0.2s ease\",\r\n                transform: isAiSettingsExpanded ? \"rotate(90deg)\" : \"rotate(0deg)\"\r\n              }}>\r\n                ▶\r\n              </span>\r\n            </h2>\r\n\r\n            {isAiSettingsExpanded && (\r\n              <div className=\"settings-section-content\">\r\n                <div style={{\r\n                  backgroundColor: \"#fff3cd\",\r\n                  border: \"1px solid #ffeaa7\",\r\n                  borderRadius: \"8px\",\r\n                  padding: \"15px\",\r\n                  marginBottom: \"20px\"\r\n                }}>\r\n                  <p style={{ margin: 0, fontSize: \"14px\", color: \"#856404\" }}>\r\n                    <strong>Super Admin Access:</strong> Configure AI prompts and behavior for document summarization, question-answering, and action generation.\r\n                  </p>\r\n                </div>\r\n\r\n                {/* Document Summary Prompt */}\r\n                <div style={{ marginBottom: \"25px\" }}>\r\n                  <label style={{\r\n                    display: \"block\",\r\n                    fontSize: \"16px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#fff\",\r\n                    marginBottom: \"8px\"\r\n                  }}>\r\n                    Document Summary Prompt\r\n                  </label>\r\n                  <textarea\r\n                    value={aiSettings.ai_prompts?.document_summary || \"\"}\r\n                    onChange={(e) => updateAiPrompt('document_summary', e.target.value)}\r\n                    placeholder=\"Enter the AI prompt for document summarization...\"\r\n                    rows={4}\r\n                    style={{\r\n                      width: \"100%\",\r\n                      padding: \"12px\",\r\n                      fontSize: \"14px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      resize: \"vertical\",\r\n                      fontFamily: \"monospace\"\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Document Q&A Prompt */}\r\n                <div style={{ marginBottom: \"25px\" }}>\r\n                  <label style={{\r\n                    display: \"block\",\r\n                    fontSize: \"16px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#fff\",\r\n                    marginBottom: \"8px\"\r\n                  }}>\r\n                    Document Q&A Prompt\r\n                  </label>\r\n                  <textarea\r\n                    value={aiSettings.ai_preconditions?.document_qa_prompt || \"\"}\r\n                    onChange={(e) => updateAiPrecondition('document_qa_prompt', e.target.value)}\r\n                    placeholder=\"Enter the AI prompt for document question-answering...\"\r\n                    rows={4}\r\n                    style={{\r\n                      width: \"100%\",\r\n                      padding: \"12px\",\r\n                      fontSize: \"14px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      resize: \"vertical\",\r\n                      fontFamily: \"monospace\"\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Action Generation Prompt */}\r\n                <div style={{ marginBottom: \"25px\" }}>\r\n                  <label style={{\r\n                    display: \"block\",\r\n                    fontSize: \"16px\",\r\n                    fontWeight: \"500\",\r\n                    color: \"#fff\",\r\n                    marginBottom: \"8px\"\r\n                  }}>\r\n                    Action Generation Prompt\r\n                  </label>\r\n                  <textarea\r\n                    value={aiSettings.ai_preconditions?.action_generation_prompt || \"\"}\r\n                    onChange={(e) => updateAiPrecondition('action_generation_prompt', e.target.value)}\r\n                    placeholder=\"Enter the AI prompt for generating action checklists...\"\r\n                    rows={4}\r\n                    style={{\r\n                      width: \"100%\",\r\n                      padding: \"12px\",\r\n                      fontSize: \"14px\",\r\n                      border: \"1px solid #ccc\",\r\n                      borderRadius: \"6px\",\r\n                      outline: \"none\",\r\n                      resize: \"vertical\",\r\n                      fontFamily: \"monospace\"\r\n                    }}\r\n                  />\r\n                </div>\r\n\r\n                {/* Save Button */}\r\n                <div style={{ textAlign: \"right\" }}>\r\n                  <button\r\n                    onClick={saveAiSettings}\r\n                    disabled={loadingAiSettings}\r\n                    style={{\r\n                      padding: \"12px 24px\",\r\n                      fontSize: \"16px\",\r\n                      fontWeight: \"600\",\r\n                      color: \"#fff\",\r\n                      backgroundColor: loadingAiSettings ? \"#ccc\" : \"#1976d2\",\r\n                      border: \"none\",\r\n                      borderRadius: \"6px\",\r\n                      cursor: loadingAiSettings ? \"not-allowed\" : \"pointer\",\r\n                      transition: \"background-color 0.2s\"\r\n                    }}\r\n                  >\r\n                    {loadingAiSettings ? \"Saving...\" : \"Save AI Settings\"}\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </section>\r\n        )}\r\n\r\n      </div>\r\n    </div>\r\n    </>\r\n  );\r\n}\r\nexport default Settings;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACxE,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,OAAO,QAAQ,gBAAgB;AACxC,SAASC,KAAK,QAAQ,gBAAgB;AACtC,OAAOC,IAAI,MAAM,aAAa;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,YAAY,GAAIC,IAAI,IAAK;EAC7BC,QAAQ,CAACC,MAAM,GAAG,GAAGF,IAAI,iDAAiD;AAC5E,CAAC;AAED,MAAMG,uBAAuB,GAAG,4BAA4B;AAE5D,SAASC,QAAQA,CAAC;EAAEC,IAAI;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAQ,CAAC,EAAE;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;EAClD;;EAEA,MAAM;IACJC,aAAa;IACbC,cAAc;IACdC,oBAAoB;IACpBC,UAAU;IACVC,gBAAgB;IAChBC,SAAS;IACTC;EACF,CAAC,GAAG9B,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM+B,aAAa,GAAGhC,OAAO,CAAC,MAAM,CAClC;IAAEY,IAAI,EAAE,OAAO;IAAEqB,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,MAAM;IAAEC,QAAQ,EAAE;EAAK,CAAC,EAC/D;IAAEvB,IAAI,EAAE,MAAM;IAAEqB,KAAK,EAAE,MAAM;IAAEC,IAAI,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAM,CAAC,EAClE;IAAEvB,IAAI,EAAE,aAAa;IAAEqB,KAAK,EAAE,aAAa;IAAEC,IAAI,EAAE,UAAU;IAAEC,QAAQ,EAAE;EAAM,CAAC,EAChF;IAAEvB,IAAI,EAAE,OAAO;IAAEqB,KAAK,EAAE,OAAO;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAC;IAAED,QAAQ,EAAE;EAAK,CAAC,EACxG;IAAEvB,IAAI,EAAE,SAAS;IAAEqB,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAE,QAAQ;IAAEE,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC;IAAED,QAAQ,EAAE;EAAK,CAAC,CACvH,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,aAAa,GAAGrC,OAAO,CAAC,MAAM;IAClCsC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEb,cAAc,CAAC;IAC/CY,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEX,UAAU,CAAC;;IAElD;IACA;IACA,MAAMY,YAAY,GAAGZ,UAAU,IAAII,aAAa;IAEhDM,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,YAAY,CAAC;IAChD,OAAOA,YAAY;EACrB,CAAC,EAAE,CAACd,cAAc,EAAEE,UAAU,EAAEI,aAAa,CAAC,CAAC;EAE/C,MAAM,CAACS,QAAQ,EAAEC,WAAW,CAAC,GAAG7C,QAAQ,CAAC;IACvCe,IAAI,EAAE,EAAE;IACRqB,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,MAAM;IACZC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE;EACX,CAAC,CAAC;EACF,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAC;IAC7CgD,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE;EACP,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC;IAC3CuD,UAAU,EAAE,CAAC,CAAC;IACdC,gBAAgB,EAAE,CAAC;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2D,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC;EACtE,MAAM,CAAC6D,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+D,UAAU,EAAEC,aAAa,CAAC,GAAGhE,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EACzD;EACA,MAAM;IAAEmE,aAAa;IAAEC;EAAiB,CAAC,GAAG/D,WAAW,CAAC,CAAC;EACzD,MAAM,CAACgE,eAAe,EAAEC,kBAAkB,CAAC,GAAGtE,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACuE,YAAY,EAAEC,eAAe,CAAC,GAAGxE,QAAQ,CAAC;IAC/CgD,UAAU,EAAE,EAAE;IACdC,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,GAAG,EAAE;EACP,CAAC,CAAC;EACF,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAAC6E,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,IAAI,EAAEgE,OAAO,CAAC,GAAG/E,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACtC,MAAM,CAACgF,WAAW,EAAEC,cAAc,CAAC,GAAGjF,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EACpD,MAAM,CAACkF,SAAS,EAAEC,YAAY,CAAC,GAAGnF,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAIlD;EACA,MAAMoF,eAAe,GAAGlF,WAAW,CAAC,OAAOmF,YAAY,GAAG,KAAK,KAAK;IAClE,IAAI;MACF5C,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE2C,YAAY,CAAC;;MAElE;MACA,IAAI,CAACA,YAAY,EAAE;QACjB,MAAMC,eAAe,GAAGC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;QAC1D,IAAIF,eAAe,EAAE;UACnBtB,aAAa,CAACyB,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC,CAAC;UAC1C7C,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;UACxD,OAAO,CAAC;QACV;MACF;;MAEA;MACAD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C,MAAMiD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrE,OAAO,mBAAmB,EAAE;QAC1DsE,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUzE,KAAK;QAChC;MACF,CAAC,CAAC;MAEF,IAAIsE,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCjC,aAAa,CAACgC,IAAI,CAACjC,UAAU,IAAI,CAAC,CAAC,CAAC;QACpCwB,YAAY,CAACW,OAAO,CAAC,YAAY,EAAET,IAAI,CAACU,SAAS,CAACH,IAAI,CAACjC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC;QACzEtB,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;;QAEzD;QACA;MACF;IACF,CAAC,CAAC,OAAO0D,GAAG,EAAE;MACZ3D,OAAO,CAAC4D,KAAK,CAAC,6BAA6B,EAAED,GAAG,CAAC;IACnD;EACF,CAAC,EAAE,CAAC7E,OAAO,EAAEF,KAAK,EAAE2C,aAAa,CAAC,CAAC;;EAGnC;EACA,MAAMsC,eAAe,GAAGpG,WAAW,CAAC,YAAY;IAC9C,IAAI;MACFuC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9C,MAAMiD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrE,OAAO,yBAAyBD,MAAM,EAAE,EAAE;QACxEuE,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUzE,KAAK;QAChC;MACF,CAAC,CAAC;MAEF,IAAIsE,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMQ,KAAK,GAAG,MAAMZ,QAAQ,CAACM,IAAI,CAAC,CAAC;QACnCxD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE6D,KAAK,CAAC;QAC5CzB,aAAa,CAACyB,KAAK,IAAI,EAAE,CAAC;MAC5B,CAAC,MAAM;QACL9D,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiD,QAAQ,CAACa,MAAM,CAAC;MAChE;IACF,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZ3D,OAAO,CAAC4D,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC;IACrD;EACF,CAAC,EAAE,CAAC7E,OAAO,EAAEF,KAAK,EAAEC,MAAM,CAAC,CAAC;;EAE5B;EACA;EACA;EACA;EACA;EACA;EACA;;EAGA;EACArB,SAAS,CAAC,MAAM;IACd,IAAIqB,MAAM,EAAE;MACV8D,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;MACxBkB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAChF,MAAM,EAAE8D,eAAe,EAAEkB,eAAe,CAAC,CAAC,CAAC,CAAC;;EAIhD;EACArG,SAAS,CAAC,MAAM;IACde,QAAQ,CAACyF,eAAe,CAACC,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAExC,aAAa,CAAC;IAC9EoB,YAAY,CAACW,OAAO,CAAC,eAAe,EAAE/B,aAAa,CAAC;EACtD,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMyC,cAAc,GAAG1G,WAAW,CAAC,YAAY;IAC7CwD,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrE,OAAO,cAAc,EAAE;QACrDsE,OAAO,EAAE;UAAE,eAAe,EAAE,UAAUxE,KAAK;QAAG;MAChD,CAAC,CAAC;MAEF,IAAIsE,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClC3C,aAAa,CAAC0C,IAAI,CAAC;QACnBvD,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEsD,IAAI,CAAC;MACvD,CAAC,MAAM,IAAIL,QAAQ,CAACa,MAAM,KAAK,GAAG,EAAE;QAClC/D,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE;MACF,CAAC,MAAM;QACLD,OAAO,CAAC4D,KAAK,CAAC,qCAAqC,EAAEV,QAAQ,CAACa,MAAM,CAAC;QACrE;QACA,IAAIb,QAAQ,CAACa,MAAM,KAAK,GAAG,EAAE;UAC3BhG,KAAK,CAAC6F,KAAK,CAAC,4BAA4B,CAAC;QAC3C;MACF;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD;MACA,IAAIA,KAAK,CAACQ,OAAO,KAAK,iBAAiB,EAAE;QACvCrG,KAAK,CAAC6F,KAAK,CAAC,4BAA4B,CAAC;MAC3C;IACF,CAAC,SAAS;MACR3C,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC,EAAE,CAACnC,OAAO,EAAEF,KAAK,CAAC,CAAC;;EAEpB;EACApB,SAAS,CAAC,MAAM;IACd,IAAIqB,MAAM,KAAK,YAAY,EAAE;MAC3BsF,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACtF,MAAM,EAAEsF,cAAc,CAAC,CAAC;EAE5B,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjCpD,oBAAoB,CAAC,IAAI,CAAC;IAC1B,IAAI;MACF,MAAMiC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrE,OAAO,cAAc,EAAE;QACrDwF,MAAM,EAAE,KAAK;QACblB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUxE,KAAK;QAClC,CAAC;QACD2F,IAAI,EAAEvB,IAAI,CAACU,SAAS,CAAC9C,UAAU;MACjC,CAAC,CAAC;MAEF,IAAIsC,QAAQ,CAACI,EAAE,EAAE;QACfvF,KAAK,CAACyG,OAAO,CAAC,iCAAiC,CAAC;MAClD,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,4BAA4B,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD7F,KAAK,CAAC6F,KAAK,CAAC,4BAA4B,CAAC;IAC3C,CAAC,SAAS;MACR3C,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC;EAED,MAAMyD,cAAc,GAAGA,CAACC,UAAU,EAAEC,KAAK,KAAK;IAC5C/D,aAAa,CAACgE,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP/D,UAAU,EAAE;QACV,GAAG+D,IAAI,CAAC/D,UAAU;QAClB,CAAC6D,UAAU,GAAGC;MAChB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,oBAAoB,GAAGA,CAACC,gBAAgB,EAAEH,KAAK,KAAK;IACxD/D,aAAa,CAACgE,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP9D,gBAAgB,EAAE;QAChB,GAAG8D,IAAI,CAAC9D,gBAAgB;QACxB,CAACgE,gBAAgB,GAAGH;MACtB;IACF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAIjG,IAAI,KAAK,WAAW,EAAE;IACxB,oBACET,OAAA;MACE+F,KAAK,EAAE;QACLe,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE,SAAS;QAChBC,UAAU,EAAE;MACd,CAAE;MAAAC,QAAA,EACH;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EACA,MAAMC,kBAAkB,GAAIC,CAAC,IAAK;IAChC,MAAMzE,SAAS,GAAGyE,CAAC,CAACC,MAAM,CAAClB,KAAK;IAChCvF,oBAAoB,CAAC+B,SAAS,CAAC;EACjC,CAAC;;EAED;;EAEA,MAAM2E,oBAAoB,GAAIC,MAAM,IAAK;IACvCrE,gBAAgB,CAACsE,SAAS,IAAI;MAC5B,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,GAAG,EAAEJ,SAAS,GAAGD,MAAM,CAAC,CAAC;MACjE,OAAOM,UAAU,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC,CAAC;EACJ,CAAC;EACD,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACrG,QAAQ,CAAC7B,IAAI,IAAI,CAAC6B,QAAQ,CAACR,KAAK,EAAE;MACrC5B,KAAK,CAAC6F,KAAK,CAAC,2CAA2C,EAAE;QACvD6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;;IAEA;IACA,IAAI3G,aAAa,CAAC4G,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACtI,IAAI,KAAK6B,QAAQ,CAAC7B,IAAI,CAAC,EAAE;MAC7DP,KAAK,CAAC6F,KAAK,CAAC,2BAA2B,EAAE;QACvC6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;IAEA,MAAMG,aAAa,GAAG,CAAC,GAAG9G,aAAa,EAAE;MAAE,GAAGI;IAAS,CAAC,CAAC;IACzDZ,gBAAgB,CAACsH,aAAa,CAAC;IAC/BzG,WAAW,CAAC;MACV9B,IAAI,EAAE,EAAE;MACRqB,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,KAAK;MACfC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF/B,KAAK,CAACyG,OAAO,CAAC,2BAA2B,EAAE;MACzCiC,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMI,iBAAiB,GAAIC,KAAK,IAAK;IACnC,MAAMH,KAAK,GAAG7G,aAAa,CAACgH,KAAK,CAAC;IAClC;IACA,IAAIrH,aAAa,CAACiH,IAAI,CAACK,YAAY,IAAIA,YAAY,CAAC1I,IAAI,KAAKsI,KAAK,CAACtI,IAAI,CAAC,EAAE;MACxEP,KAAK,CAAC6F,KAAK,CAAC,yEAAyE,EAAE;QACrF6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;MACF;IACF;;IAEA;IACA,MAAMG,aAAa,GAAG9G,aAAa,CAACkH,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IACjExH,gBAAgB,CAACsH,aAAa,CAAC;IAE/B9I,KAAK,CAACyG,OAAO,CAAC,6BAA6B,EAAE;MAC3CiC,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,MAAMU,iBAAiB,GAAGA,CAACL,KAAK,EAAEM,GAAG,EAAEzC,KAAK,KAAK;IAC/C;IACA,MAAMiC,aAAa,GAAG,CAAC,GAAG9G,aAAa,CAAC;IACxC8G,aAAa,CAACE,KAAK,CAAC,GAAG;MAAE,GAAGF,aAAa,CAACE,KAAK,CAAC;MAAE,CAACM,GAAG,GAAGzC;IAAM,CAAC;;IAEhE;IACArF,gBAAgB,CAACsH,aAAa,CAAC;EACjC,CAAC;;EAED;EACA,MAAMS,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFtH,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEb,cAAc,CAAC;MAC7DY,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,aAAa,CAAC;MAE/C,MAAMmD,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrE,OAAO,+BAA+B,EAAE;QACtEwF,MAAM,EAAE,MAAM;QACdlB,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUzE,KAAK,EAAE;UAChC,cAAc,EAAE;QAClB,CAAC;QACD2F,IAAI,EAAEvB,IAAI,CAACU,SAAS,CAAC;UACnB6D,MAAM,EAAEnI,cAAc;UACtBE,UAAU,EAAES;QACd,CAAC;MACH,CAAC,CAAC;MAEF,IAAImD,QAAQ,CAACI,EAAE,EAAE;QACfvF,KAAK,CAACyG,OAAO,CAAC,wCAAwC,EAAE;UACtDiC,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;;QAEF;MACF,CAAC,MAAM;QACL,MAAMc,SAAS,GAAG,MAAMtE,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvCxD,OAAO,CAAC4D,KAAK,CAAC,cAAc,EAAE4D,SAAS,CAAC;QACxC,MAAM,IAAI/C,KAAK,CAAC+C,SAAS,CAAC5D,KAAK,IAAI,mCAAmC,CAAC;MACzE;IACF,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZ3D,OAAO,CAAC4D,KAAK,CAAC,2BAA2B,EAAED,GAAG,CAAC;MAC/C5F,KAAK,CAAC6F,KAAK,CAAC,qCAAqC,GAAGD,GAAG,CAACS,OAAO,EAAE;QAC/DqC,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EAID,MAAMe,oBAAoB,GAAG,MAAOV,KAAK,IAAK;IAC5C,MAAMW,QAAQ,GAAGlI,SAAS,CAACuH,KAAK,CAAC;IACjC,MAAMY,aAAa,GAAGC,MAAM,CAACC,OAAO,CAClC,oCAAoCH,QAAQ,CAACnH,UAAU,kBAAkBmH,QAAQ,CAAClH,OAAO,WAAWkH,QAAQ,CAACjH,IAAI,KAAKiH,QAAQ,CAAChH,KAAK,IAAIgH,QAAQ,CAAC/G,GAAG,EACtJ,CAAC;IAED,IAAIgH,aAAa,EAAE;MACjB,MAAMG,gBAAgB,GAAGtI,SAAS,CAACyH,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;MAChEtH,eAAe,CAACqI,gBAAgB,CAAC;;MAEjC;MACA,MAAMnF,eAAe,CAAC,IAAI,CAAC;MAC3B3C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C;EACF,CAAC;EAED,MAAM8H,kBAAkB,GAAIhB,KAAK,IAAK;IACpClF,kBAAkB,CAACkF,KAAK,CAAC;IACzBhF,eAAe,CAAC;MAAE,GAAGvC,SAAS,CAACuH,KAAK;IAAE,CAAC,CAAC;IACxC;IACAzG,cAAc,CAAC;MACbC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMqH,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAAClG,YAAY,CAACvB,UAAU,IAAI,CAACuB,YAAY,CAACtB,OAAO,EAAE;MACrDyH,KAAK,CAAC,oDAAoD,CAAC;MAC3D;IACF;IAEA,MAAMH,gBAAgB,GAAG,CAAC,GAAGtI,SAAS,CAAC;IACvCsI,gBAAgB,CAAClG,eAAe,CAAC,GAAG;MAAE,GAAGE;IAAa,CAAC;IACvDrC,eAAe,CAACqI,gBAAgB,CAAC;IACjCjG,kBAAkB,CAAC,IAAI,CAAC;IACxBE,eAAe,CAAC;MACdxB,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;IACP,CAAC,CAAC;;IAEF;IACA,MAAMgC,eAAe,CAAC,IAAI,CAAC;IAC3B3C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;EAC9C,CAAC;EAED,MAAMiI,gBAAgB,GAAGA,CAAA,KAAM;IAC7BrG,kBAAkB,CAAC,IAAI,CAAC;IACxBE,eAAe,CAAC;MACdxB,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;IACP,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwH,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAC9H,WAAW,CAACE,UAAU,IAAI,CAACF,WAAW,CAACG,OAAO,EAAE;MACnDyH,KAAK,CAAC,oDAAoD,CAAC;MAC3D;IACF;IAEAjI,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEI,WAAW,CAAC;IAChD,MAAMyH,gBAAgB,GAAG,CAACzH,WAAW,EAAE,GAAGb,SAAS,CAAC,CAAC,CAAC;IACtDC,eAAe,CAACqI,gBAAgB,CAAC;IACjCxH,cAAc,CAAC;MACbC,UAAU,EAAE,EAAE;MACdC,OAAO,EAAE,EAAE;MACXC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,GAAG,EAAE;IACP,CAAC,CAAC;;IAEF;IACA,MAAMgC,eAAe,CAAC,IAAI,CAAC;IAC3B3C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;EAC5C,CAAC;EAED,MAAMmI,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,UAAU,GAAGlC,IAAI,CAACmC,KAAK,CAAC,MAAM,GAAGnC,IAAI,CAACoC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAC3EvG,UAAU,CAACoG,UAAU,CAAC,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB;;IAEA;IACA,IAAI,CAACnK,IAAI,EAAE;MACTP,KAAK,CAAC6F,KAAK,CAAC,oCAAoC,EAAE;QAChD6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;MACF,OAAO,KAAK;IACd;IAEA,IAAI,CAAC1E,OAAO,IAAIA,OAAO,CAAC0G,MAAM,GAAG,CAAC,EAAE;MAClC3K,KAAK,CAAC6F,KAAK,CACT,4DAA4D,EAC5D;QACE6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CACF,CAAC;MACD,OAAO,KAAK;IACd;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI,CAAC1E,OAAO,CAAC2G,IAAI,CAAC,CAAC,EAAE;MACnB5K,KAAK,CAAC6F,KAAK,CAAC,sDAAsD,EAAE;QAClE6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;MACF,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAMkC,QAAQ,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI,CAACH,WAAW,CAAC,CAAC,EAAE;MAClB;IACF;IACA,IAAI;MACF,MAAMvF,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGrE,OAAO,qBAAqB,EAAE;QAC5DwF,MAAM,EAAE,MAAM;QACdlB,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClCC,aAAa,EAAE,UAAUzE,KAAK;QAChC,CAAC;QACD2F,IAAI,EAAEvB,IAAI,CAACU,SAAS,CAAC;UACnBpF,IAAI;UACJiE,WAAW;UACXsG,IAAI,EAAE7G,OAAO;UACb8G,OAAO,EAAEjK;QACX,CAAC;MACH,CAAC,CAAC;MAEF,IAAIqE,QAAQ,CAACI,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClC;;QAEAnB,aAAa,CAAE0G,SAAS,IAAK,CAC3B,GAAGA,SAAS,EACZ;UAAEzK,IAAI;UAAEiE,WAAW;UAAEsG,IAAI,EAAE7G;QAAQ,CAAC,CACrC,CAAC;QACFC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;QAChBK,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC;QACbE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEpB;QACAnE,YAAY,CAACI,uBAAuB,CAAC;QACrCuB,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;QAEzElC,KAAK,CAACyG,OAAO,CAAC,0BAA0B,EAAE;UACxCiC,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAMc,SAAS,GAAG,MAAMtE,QAAQ,CAACM,IAAI,CAAC,CAAC;QACvCxD,OAAO,CAAC4D,KAAK,CAAC,iBAAiB,EAAE4D,SAAS,CAAC;QAE3C,MAAMwB,YAAY,GAChBxB,SAAS,CAAC5D,KAAK,IACf4D,SAAS,CAACpD,OAAO,IACjB,4CAA4C;QAC9CrG,KAAK,CAAC6F,KAAK,CAACoF,YAAY,EAAE;UACxBvC,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;QACb,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAElD7F,KAAK,CAAC6F,KAAK,CAAC,iDAAiD,EAAE;QAC7D6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;;EAEA;EACA,MAAMuC,gBAAgB,GAAG,MAAAA,CAAOlC,KAAK,EAAEmC,EAAE,KAAK;IAC5C,IAAI;MACF;MACA,MAAMC,MAAM,GAAG,MAAMnL,IAAI,CAACoL,IAAI,CAAC;QAC7BC,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,mCAAmC;QACzCC,IAAI,EAAE,SAAS;QACfC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,iBAAiB;QACpCC,gBAAgB,EAAE,QAAQ;QAC1BC,kBAAkB,EAAE,SAAS;QAC7BC,iBAAiB,EAAE;MACrB,CAAC,CAAC;MAEF,IAAIT,MAAM,CAACU,WAAW,EAAE;QACtB,MAAM3G,QAAQ,GAAG,MAAMC,KAAK,CAC1B,GAAGrE,OAAO,wBAAwBoK,EAAE,YAAYrK,MAAM,EAAE,EACxD;UACEyF,MAAM,EAAE,QAAQ;UAChBlB,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClCC,aAAa,EAAE,UAAUzE,KAAK;UAChC;QACF,CACF,CAAC;QAED,IAAIsE,QAAQ,CAACI,EAAE,EAAE;UACfjB,aAAa,CAAE0G,SAAS,IAAKA,SAAS,CAAC9B,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC,CAAC;;UAErE;UACA1I,YAAY,CAACI,uBAAuB,CAAC;UACrCuB,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;UAEzElC,KAAK,CAACyG,OAAO,CAAC,oCAAoC,EAAE;YAClDiC,QAAQ,EAAE,WAAW;YACrBC,SAAS,EAAE;UACb,CAAC,CAAC;QAEJ,CAAC,MAAM;UACL,MAAM,IAAIjC,KAAK,CAAC,qBAAqBvB,QAAQ,CAAC4G,UAAU,EAAE,CAAC;QAC7D;MACF;IACF,CAAC,CAAC,OAAOlG,KAAK,EAAE;MACd5D,OAAO,CAAC4D,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD7F,KAAK,CAAC6F,KAAK,CAAC,iDAAiD,EAAE;QAC7D6C,QAAQ,EAAE,WAAW;QACrBC,SAAS,EAAE;MACb,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMqD,cAAc,GAAIlB,IAAI,IAAK;IAC/BmB,SAAS,CAACC,SAAS,CAACC,SAAS,CAACrB,IAAI,CAAC;IAEnC9K,KAAK,CAACyG,OAAO,CAAC,2BAA2B,EAAE;MACzCiC,QAAQ,EAAE,WAAW;MACrBC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC;EAED,oBACExI,OAAA,CAAAE,SAAA;IAAAmH,QAAA,gBACErH,OAAA;MAAAqH,QAAA,EACG;AACT;AACA;AACA,iCAAiC7D,aAAa;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eACRzH,OAAA;MAAKiM,SAAS,EAAC,WAAW;MAAA5E,QAAA,eACxBrH,OAAA;QACEiM,SAAS,EAAC,MAAM;QAChBlG,KAAK,EAAE;UAAEkB,MAAM,EAAE,kBAAkB;UAAEiF,SAAS,EAAE;QAAQ,CAAE;QAAA7E,QAAA,gBAE5DrH,OAAA;UAAKiM,SAAS,EAAC,iBAAiB;UAAA5E,QAAA,gBAC9BrH,OAAA;YAAIiM,SAAS,EAAC,gBAAgB;YAAA5E,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5CzH,OAAA;YAAGiM,SAAS,EAAC,mBAAmB;YAAA5E,QAAA,EAAC;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAE7E,CAAC,eAqBNzH,OAAA;UACE+F,KAAK,EAAE;YACLoG,YAAY,EAAE,MAAM;YACpBC,OAAO,EAAE,MAAM;YACfC,MAAM,EAAE,mBAAmB;YAC3BC,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE;UACnB,CAAE;UAAAlF,QAAA,gBAEFrH,OAAA;YACE+F,KAAK,EAAE;cACLmB,QAAQ,EAAE,MAAM;cAChBsF,UAAU,EAAE,KAAK;cACjBrF,KAAK,EAAE,MAAM;cACbgF,YAAY,EAAE,MAAM;cACpBrF,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/BC,UAAU,EAAE;YACd,CAAE;YAAAK,QAAA,eAEFrH,OAAA;cAAAqH,QAAA,EAAM;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC,eAGLzH,OAAA;YAAK+F,KAAK,EAAE;cAAEoG,YAAY,EAAE;YAAO,CAAE;YAAA9E,QAAA,gBACnCrH,OAAA;cACE+F,KAAK,EAAE;gBACLe,OAAO,EAAE,OAAO;gBAChBI,QAAQ,EAAE,MAAM;gBAChBsF,UAAU,EAAE,KAAK;gBACjBrF,KAAK,EAAE,MAAM;gBACbgF,YAAY,EAAE;cAChB,CAAE;cAAA9E,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACRzH,OAAA;cACE0G,KAAK,EAAExF,cAAe;cACtBuL,QAAQ,EAAE/E,kBAAmB;cAC7B3B,KAAK,EAAE;gBACL2G,KAAK,EAAE,MAAM;gBACbN,OAAO,EAAE,MAAM;gBACflF,QAAQ,EAAE,MAAM;gBAChBmF,MAAM,EAAE,gBAAgB;gBACxBC,YAAY,EAAE,KAAK;gBACnBC,eAAe,EAAE,MAAM;gBACvBI,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE;cACd,CAAE;cACFC,OAAO,EAAGlF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAW;cACzDC,MAAM,EAAGpF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAQ;cAAAzF,QAAA,EAEpD,CAACpG,aAAa,IAAIA,aAAa,CAACuJ,MAAM,GAAG,CAAC,GAAGvJ,aAAa,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE+L,GAAG,CAAE3D,MAAM,iBAC7GrJ,OAAA;gBAAqB0G,KAAK,EAAE2C,MAAO;gBAAAhC,QAAA,EAChCgC;cAAM,GADIA,MAAM;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEX,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGC,CAAC,eAGVzH,OAAA;UAASiM,SAAS,EAAC,kBAAkB;UAAA5E,QAAA,gBACnCrH,OAAA;YAAIiM,SAAS,EAAC,yBAAyB;YAAA5E,QAAA,GAAC,4BACZ,EAACnG,cAAc;UAAA;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACLzH,OAAA;YAAKiM,SAAS,EAAC,0BAA0B;YAAA5E,QAAA,gBACzCrH,OAAA;cAAK+F,KAAK,EAAE;gBACVoG,YAAY,EAAE,MAAM;gBACpBI,eAAe,EAAE,MAAM;gBACvBH,OAAO,EAAE,MAAM;gBACfE,YAAY,EAAE,KAAK;gBACnBD,MAAM,EAAE;cACV,CAAE;cAAAhF,QAAA,gBACArH,OAAA;gBACE+F,KAAK,EAAE;kBACLmB,QAAQ,EAAE,MAAM;kBAChBsF,UAAU,EAAE,KAAK;kBACjBrF,KAAK,EAAE,MAAM;kBACbgF,YAAY,EAAE,MAAM;kBACpBrF,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE,eAAe;kBAC/BC,UAAU,EAAE,QAAQ;kBACpBiG,YAAY,EAAE,gBAAgB;kBAC9BC,aAAa,EAAE;gBACjB,CAAE;gBAAA7F,QAAA,gBAEFrH,OAAA;kBAAAqH,QAAA,EAAM;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC3BzH,OAAA;kBAAM+F,KAAK,EAAE;oBAAEmB,QAAQ,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAE,QAAA,GAC9CxF,aAAa,CAAC2I,MAAM,EAAC,QAAM,EAAC3I,aAAa,CAAC2I,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE;gBAAA;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,EACJ5F,aAAa,CAAC2I,MAAM,GAAG,CAAC,gBACvBxK,OAAA;gBAAI+F,KAAK,EAAE;kBAAEoH,SAAS,EAAE,MAAM;kBAAEf,OAAO,EAAE;gBAAE,CAAE;gBAAA/E,QAAA,EAC1CxF,aAAa,CAACmL,GAAG,CAAC,CAACtE,KAAK,EAAEG,KAAK,kBAC9B7I,OAAA;kBAEE+F,KAAK,EAAE;oBACLe,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBoG,GAAG,EAAE,MAAM;oBACXjB,YAAY,EAAE,MAAM;oBACpBkB,QAAQ,EAAE,MAAM;oBAChBjB,OAAO,EAAE,MAAM;oBACfG,eAAe,EAAE1D,KAAK,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,MAAM;oBACrDyD,YAAY,EAAE,KAAK;oBACnBD,MAAM,EAAE;kBACV,CAAE;kBAAAhF,QAAA,gBAEFrH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEuH,IAAI,EAAE;oBAAY,CAAE;oBAAAjG,QAAA,gBAChCrH,OAAA;sBACE+F,KAAK,EAAE;wBACLe,OAAO,EAAE,OAAO;wBAChBI,QAAQ,EAAE,MAAM;wBAChBsF,UAAU,EAAE,KAAK;wBACjBrF,KAAK,EAAE,MAAM;wBACbgF,YAAY,EAAE;sBAChB,CAAE;sBAAA9E,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRzH,OAAA;sBACE0B,IAAI,EAAC,MAAM;sBACXgF,KAAK,EAAEgC,KAAK,CAACjH,KAAM;sBACnBgL,QAAQ,EAAG9E,CAAC,IACVuB,iBAAiB,CAACL,KAAK,EAAE,OAAO,EAAElB,CAAC,CAACC,MAAM,CAAClB,KAAK,CACjD;sBACD6G,WAAW,EAAC,aAAa;sBACzBxH,KAAK,EAAE;wBACL2G,KAAK,EAAE,MAAM;wBACbN,OAAO,EAAE,UAAU;wBACnBlF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAS;wBACtCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,mCAAmC;sBAChE,CAAE;sBACFT,MAAM,EAAGpF,CAAC,IAAK;wBACbA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAM;wBACnCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,MAAM;sBACnC;oBAAE;sBAAAlG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eAENzH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEuH,IAAI,EAAE;oBAAY,CAAE;oBAAAjG,QAAA,gBAChCrH,OAAA;sBACE+F,KAAK,EAAE;wBACLe,OAAO,EAAE,OAAO;wBAChBI,QAAQ,EAAE,MAAM;wBAChBsF,UAAU,EAAE,KAAK;wBACjBrF,KAAK,EAAE,MAAM;wBACbgF,YAAY,EAAE;sBAChB,CAAE;sBAAA9E,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRzH,OAAA;sBACE0G,KAAK,EAAEgC,KAAK,CAAChH,IAAK;sBAClB+K,QAAQ,EAAG9E,CAAC,IACVuB,iBAAiB,CAACL,KAAK,EAAE,MAAM,EAAElB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAChD;sBACDX,KAAK,EAAE;wBACL2G,KAAK,EAAE,MAAM;wBACbN,OAAO,EAAE,UAAU;wBACnBlF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBC,eAAe,EAAE,MAAM;wBACvBI,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAS;wBACtCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,mCAAmC;sBAChE,CAAE;sBACFT,MAAM,EAAGpF,CAAC,IAAK;wBACbA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAM;wBACnCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,MAAM;sBACnC,CAAE;sBAAAnG,QAAA,gBAEFrH,OAAA;wBAAQ0G,KAAK,EAAC,MAAM;wBAAAW,QAAA,EAAC;sBAAI;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAClCzH,OAAA;wBAAQ0G,KAAK,EAAC,UAAU;wBAAAW,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC1CzH,OAAA;wBAAQ0G,KAAK,EAAC,QAAQ;wBAAAW,QAAA,EAAC;sBAAM;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACtCzH,OAAA;wBAAQ0G,KAAK,EAAC,UAAU;wBAAAW,QAAA,EAAC;sBAAQ;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eAC1CzH,OAAA;wBAAQ0G,KAAK,EAAC,cAAc;wBAAAW,QAAA,EAAC;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,EACLiB,KAAK,CAAChH,IAAI,KAAK,QAAQ,iBACtB1B,OAAA;oBAAK+F,KAAK,EAAE;sBAAEuH,IAAI,EAAE;oBAAY,CAAE;oBAAAjG,QAAA,gBAChCrH,OAAA;sBACE+F,KAAK,EAAE;wBACLe,OAAO,EAAE,OAAO;wBAChBI,QAAQ,EAAE,MAAM;wBAChBsF,UAAU,EAAE,KAAK;wBACjBrF,KAAK,EAAE,MAAM;wBACbgF,YAAY,EAAE;sBAChB,CAAE;sBAAA9E,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACRzH,OAAA;sBACE0B,IAAI,EAAC,MAAM;sBACXgF,KAAK,EAAE,CAACgC,KAAK,CAAC9G,OAAO,IAAI,EAAE,EAAE6L,IAAI,CAAC,GAAG,CAAE;sBACvChB,QAAQ,EAAG9E,CAAC,IACVuB,iBAAiB,CACfL,KAAK,EACL,SAAS,EACTlB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAACgH,KAAK,CAAC,GAAG,CAAC,CAACV,GAAG,CAAEW,GAAG,IAAKA,GAAG,CAAClD,IAAI,CAAC,CAAC,CACnD,CACD;sBACD8C,WAAW,EAAC,2BAA2B;sBACvCxH,KAAK,EAAE;wBACL2G,KAAK,EAAE,MAAM;wBACbN,OAAO,EAAE,UAAU;wBACnBlF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAS;wBACtCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,mCAAmC;sBAChE,CAAE;sBACFT,MAAM,EAAGpF,CAAC,IAAK;wBACbA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAM;wBACnCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,MAAM;sBACnC;oBAAE;sBAAAlG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CACN,eACDzH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEe,OAAO,EAAE,MAAM;sBAAEE,UAAU,EAAE,QAAQ;sBAAEoG,GAAG,EAAE;oBAAO,CAAE;oBAAA/F,QAAA,gBACjErH,OAAA;sBACE+F,KAAK,EAAE;wBACLe,OAAO,EAAE,MAAM;wBACfE,UAAU,EAAE,QAAQ;wBACpBoG,GAAG,EAAE,KAAK;wBACVlG,QAAQ,EAAE,MAAM;wBAChBC,KAAK,EAAE,MAAM;wBACbyG,UAAU,EAAE;sBACd,CAAE;sBAAAvG,QAAA,gBAEFrH,OAAA;wBACE0B,IAAI,EAAC,UAAU;wBACfmM,OAAO,EAAEnF,KAAK,CAAC/G,QAAS;wBACxB8K,QAAQ,EAAG9E,CAAC,IACVuB,iBAAiB,CAACL,KAAK,EAAE,UAAU,EAAElB,CAAC,CAACC,MAAM,CAACiG,OAAO,CACtD;wBACD9H,KAAK,EAAE;0BACL2G,KAAK,EAAE,MAAM;0BACbzF,MAAM,EAAE,MAAM;0BACd6G,MAAM,EAAE,SAAS;0BACjBC,WAAW,EAAE;wBACf;sBAAE;wBAAAzG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,YAEJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,EAEPjG,aAAa,CAACiH,IAAI,CAACK,YAAY,IAAIA,YAAY,CAAC1I,IAAI,KAAKsI,KAAK,CAACtI,IAAI,CAAC,gBACnEJ,OAAA;sBACE+F,KAAK,EAAE;wBACLqG,OAAO,EAAE,SAAS;wBAClBlF,QAAQ,EAAE,MAAM;wBAChBC,KAAK,EAAE,MAAM;wBACboF,eAAe,EAAE,SAAS;wBAC1BF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnB0B,SAAS,EAAE;sBACb,CAAE;sBAAA3G,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,gBAEPzH,OAAA;sBACEiO,OAAO,EAAEA,CAAA,KAAMrF,iBAAiB,CAACC,KAAK,CAAE;sBACxC9C,KAAK,EAAE;wBACLqG,OAAO,EAAE,UAAU;wBACnBlF,QAAQ,EAAE,MAAM;wBAChBC,KAAK,EAAE,MAAM;wBACboF,eAAe,EAAE,SAAS;wBAC1BF,MAAM,EAAE,MAAM;wBACdC,YAAY,EAAE,KAAK;wBACnBwB,MAAM,EAAE,SAAS;wBACjBlB,UAAU,EAAE;sBACd,CAAE;sBACFsB,WAAW,EAAGvG,CAAC,IACZA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SACnC;sBACD4B,UAAU,EAAGxG,CAAC,IACXA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SACnC;sBAAAlF,QAAA,EACF;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CACT;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GA3MDoB,KAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA4MR,CACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,gBAELzH,OAAA;gBAAG+F,KAAK,EAAE;kBAAEoB,KAAK,EAAE,MAAM;kBAAED,QAAQ,EAAE;gBAAO,CAAE;gBAAAG,QAAA,EAAC;cAE/C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CACJ;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzH,OAAA;cAAK+F,KAAK,EAAE;gBACVqI,SAAS,EAAE,MAAM;gBACjB7B,eAAe,EAAE,MAAM;gBACvBH,OAAO,EAAE,MAAM;gBACfE,YAAY,EAAE,KAAK;gBACnBD,MAAM,EAAE;cACV,CAAE;cAAAhF,QAAA,gBACArH,OAAA;gBACE+F,KAAK,EAAE;kBACLmB,QAAQ,EAAE,MAAM;kBAChBsF,UAAU,EAAE,KAAK;kBACjBrF,KAAK,EAAE,MAAM;kBACbgF,YAAY,EAAE,MAAM;kBACpBc,YAAY,EAAE,gBAAgB;kBAC9BC,aAAa,EAAE;gBACjB,CAAE;gBAAA7F,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzH,OAAA;gBAAK+F,KAAK,EAAE;kBAAEe,OAAO,EAAE,MAAM;kBAAEuG,QAAQ,EAAE,MAAM;kBAAED,GAAG,EAAE;gBAAO,CAAE;gBAAA/F,QAAA,gBAC7DrH,OAAA;kBAAK+F,KAAK,EAAE;oBAAEuH,IAAI,EAAE;kBAAY,CAAE;kBAAAjG,QAAA,gBAChCrH,OAAA;oBACE+F,KAAK,EAAE;sBACLe,OAAO,EAAE,OAAO;sBAChBI,QAAQ,EAAE,MAAM;sBAChBsF,UAAU,EAAE,KAAK;sBACjBrF,KAAK,EAAE,MAAM;sBACbgF,YAAY,EAAE;oBAChB,CAAE;oBAAA9E,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRzH,OAAA;oBACE0B,IAAI,EAAC,MAAM;oBACXgF,KAAK,EAAEzE,QAAQ,CAAC7B,IAAK;oBACrBqM,QAAQ,EAAG9E,CAAC,IACVzF,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAE7B,IAAI,EAAEuH,CAAC,CAACC,MAAM,CAAClB;oBAAM,CAAC,CAClD;oBACD6G,WAAW,EAAC,mCAAmC;oBAC/CxH,KAAK,EAAE;sBACL2G,KAAK,EAAE,MAAM;sBACbN,OAAO,EAAE,UAAU;sBACnBlF,QAAQ,EAAE,MAAM;sBAChBmF,MAAM,EAAE,gBAAgB;sBACxBC,YAAY,EAAE,KAAK;sBACnBK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAE;oBACFC,OAAO,EAAGlF,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAS;sBACtCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,mCAAmC;oBAChE,CAAE;oBACFT,MAAM,EAAGpF,CAAC,IAAK;sBACbA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAM;sBACnCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,MAAM;oBACnC;kBAAE;oBAAAlG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENzH,OAAA;kBAAK+F,KAAK,EAAE;oBAAEuH,IAAI,EAAE;kBAAY,CAAE;kBAAAjG,QAAA,gBAChCrH,OAAA;oBACE+F,KAAK,EAAE;sBACLe,OAAO,EAAE,OAAO;sBAChBI,QAAQ,EAAE,MAAM;sBAChBsF,UAAU,EAAE,KAAK;sBACjBrF,KAAK,EAAE,MAAM;sBACbgF,YAAY,EAAE;oBAChB,CAAE;oBAAA9E,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRzH,OAAA;oBACE0B,IAAI,EAAC,MAAM;oBACXgF,KAAK,EAAEzE,QAAQ,CAACR,KAAM;oBACtBgL,QAAQ,EAAG9E,CAAC,IACVzF,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAER,KAAK,EAAEkG,CAAC,CAACC,MAAM,CAAClB;oBAAM,CAAC,CACnD;oBACD6G,WAAW,EAAC,aAAa;oBACzBxH,KAAK,EAAE;sBACL2G,KAAK,EAAE,MAAM;sBACbN,OAAO,EAAE,UAAU;sBACnBlF,QAAQ,EAAE,MAAM;sBAChBmF,MAAM,EAAE,gBAAgB;sBACxBC,YAAY,EAAE,KAAK;sBACnBK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAE;oBACFC,OAAO,EAAGlF,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAS;sBACtCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,mCAAmC;oBAChE,CAAE;oBACFT,MAAM,EAAGpF,CAAC,IAAK;sBACbA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAM;sBACnCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,MAAM;oBACnC;kBAAE;oBAAAlG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENzH,OAAA;kBAAK+F,KAAK,EAAE;oBAAEuH,IAAI,EAAE;kBAAY,CAAE;kBAAAjG,QAAA,gBAChCrH,OAAA;oBACE+F,KAAK,EAAE;sBACLe,OAAO,EAAE,OAAO;sBAChBI,QAAQ,EAAE,MAAM;sBAChBsF,UAAU,EAAE,KAAK;sBACjBrF,KAAK,EAAE,MAAM;sBACbgF,YAAY,EAAE;oBAChB,CAAE;oBAAA9E,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRzH,OAAA;oBACE0G,KAAK,EAAEzE,QAAQ,CAACP,IAAK;oBACrB+K,QAAQ,EAAG9E,CAAC,IACVzF,WAAW,CAAC;sBAAE,GAAGD,QAAQ;sBAAEP,IAAI,EAAEiG,CAAC,CAACC,MAAM,CAAClB;oBAAM,CAAC,CAClD;oBACDX,KAAK,EAAE;sBACL2G,KAAK,EAAE,MAAM;sBACbN,OAAO,EAAE,UAAU;sBACnBlF,QAAQ,EAAE,MAAM;sBAChBmF,MAAM,EAAE,gBAAgB;sBACxBC,YAAY,EAAE,KAAK;sBACnBC,eAAe,EAAE,MAAM;sBACvBI,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAE;oBACFC,OAAO,EAAGlF,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAS;sBACtCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,mCAAmC;oBAChE,CAAE;oBACFT,MAAM,EAAGpF,CAAC,IAAK;sBACbA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAM;sBACnCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,MAAM;oBACnC,CAAE;oBAAAnG,QAAA,gBAEJrH,OAAA;sBAAQ0G,KAAK,EAAC,MAAM;sBAAAW,QAAA,EAAC;oBAAI;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAClCzH,OAAA;sBAAQ0G,KAAK,EAAC,UAAU;sBAAAW,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CzH,OAAA;sBAAQ0G,KAAK,EAAC,QAAQ;sBAAAW,QAAA,EAAC;oBAAM;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACtCzH,OAAA;sBAAQ0G,KAAK,EAAC,UAAU;sBAAAW,QAAA,EAAC;oBAAQ;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAC1CzH,OAAA;sBAAQ0G,KAAK,EAAC,cAAc;sBAAAW,QAAA,EAAC;oBAAY;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EAELxF,QAAQ,CAACP,IAAI,KAAK,QAAQ,iBACzB1B,OAAA;kBAAK+F,KAAK,EAAE;oBAAEuH,IAAI,EAAE;kBAAY,CAAE;kBAAAjG,QAAA,gBAChCrH,OAAA;oBACE+F,KAAK,EAAE;sBACLe,OAAO,EAAE,OAAO;sBAChBI,QAAQ,EAAE,MAAM;sBAChBsF,UAAU,EAAE,KAAK;sBACjBrF,KAAK,EAAE,MAAM;sBACbgF,YAAY,EAAE;oBAChB,CAAE;oBAAA9E,QAAA,EACH;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACRzH,OAAA;oBACE0B,IAAI,EAAC,MAAM;oBACXgF,KAAK,EAAEzE,QAAQ,CAACL,OAAO,CAAC6L,IAAI,CAAC,GAAG,CAAE;oBAClChB,QAAQ,EAAG9E,CAAC,IACVzF,WAAW,CAAC;sBACV,GAAGD,QAAQ;sBACXL,OAAO,EAAE+F,CAAC,CAACC,MAAM,CAAClB,KAAK,CACpBgH,KAAK,CAAC,GAAG,CAAC,CACVV,GAAG,CAAEW,GAAG,IAAKA,GAAG,CAAClD,IAAI,CAAC,CAAC;oBAC5B,CAAC,CACF;oBACD8C,WAAW,EAAC,2BAA2B;oBACvCxH,KAAK,EAAE;sBACL2G,KAAK,EAAE,MAAM;sBACbN,OAAO,EAAE,UAAU;sBACnBlF,QAAQ,EAAE,MAAM;sBAChBmF,MAAM,EAAE,gBAAgB;sBACxBC,YAAY,EAAE,KAAK;sBACnBK,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE;oBACd,CAAE;oBACFC,OAAO,EAAGlF,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAS;sBACtCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,mCAAmC;oBAChE,CAAE;oBACFT,MAAM,EAAGpF,CAAC,IAAK;sBACbA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,MAAM;sBACnCnF,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACyH,SAAS,GAAG,MAAM;oBACnC;kBAAE;oBAAAlG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN,eAEDzH,OAAA;kBAAK+F,KAAK,EAAE;oBAAEe,OAAO,EAAE,MAAM;oBAAEE,UAAU,EAAE,QAAQ;oBAAEoG,GAAG,EAAE;kBAAO,CAAE;kBAAA/F,QAAA,gBACjErH,OAAA;oBACE+F,KAAK,EAAE;sBACLe,OAAO,EAAE,MAAM;sBACfE,UAAU,EAAE,QAAQ;sBACpBoG,GAAG,EAAE,KAAK;sBACVlG,QAAQ,EAAE,MAAM;sBAChBC,KAAK,EAAE,MAAM;sBACbyG,UAAU,EAAE;oBACd,CAAE;oBAAAvG,QAAA,gBAEFrH,OAAA;sBACE0B,IAAI,EAAC,UAAU;sBACfmM,OAAO,EAAE5L,QAAQ,CAACN,QAAS;sBAC3B8K,QAAQ,EAAG9E,CAAC,IACVzF,WAAW,CAAC;wBAAE,GAAGD,QAAQ;wBAAEN,QAAQ,EAAEgG,CAAC,CAACC,MAAM,CAACiG;sBAAQ,CAAC,CACxD;sBACD9H,KAAK,EAAE;wBACL2G,KAAK,EAAE,MAAM;wBACbzF,MAAM,EAAE,MAAM;wBACd6G,MAAM,EAAE,SAAS;wBACjBC,WAAW,EAAE;sBACf;oBAAE;sBAAAzG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,YAEJ;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAERzH,OAAA;oBACEiO,OAAO,EAAE3F,cAAe;oBACxB2D,SAAS,EAAC,aAAa;oBACvBlG,KAAK,EAAE;sBAAEsI,UAAU,EAAE;oBAAO,CAAE;oBAAAhH,QAAA,gBAE9BrH,OAAA,CAACL,MAAM;sBAAC2O,IAAI,EAAE;oBAAG;sBAAAhH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,aAEtB;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzH,OAAA;gBAAK+F,KAAK,EAAE;kBACVqI,SAAS,EAAE,MAAM;kBACjBG,UAAU,EAAE,MAAM;kBAClBC,SAAS,EAAE,mBAAmB;kBAC9B1H,OAAO,EAAE,MAAM;kBACfC,cAAc,EAAE;gBAClB,CAAE;gBAAAM,QAAA,eACArH,OAAA;kBACEiO,OAAO,EAAE7E,oBAAqB;kBAC9B6C,SAAS,EAAC,aAAa;kBACvBlG,KAAK,EAAE;oBACLqG,OAAO,EAAE,WAAW;oBACpBlF,QAAQ,EAAE,MAAM;oBAChBsF,UAAU,EAAE;kBACd,CAAE;kBAAAnF,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEVzH,OAAA;UAASiM,SAAS,EAAC,kBAAkB;UAAA5E,QAAA,gBACnCrH,OAAA;YAAIiM,SAAS,EAAC,yBAAyB;YAAA5E,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzH,OAAA;YAAKiM,SAAS,EAAC,0BAA0B;YAAA5E,QAAA,eAIzCrH,OAAA;cAAKiM,SAAS,EAAC,qBAAqB;cAAA5E,QAAA,gBAElCrH,OAAA;gBAAKiM,SAAS,EAAC,0BAA0B;gBAAA5E,QAAA,gBACvCrH,OAAA;kBACE+F,KAAK,EAAE;oBACLmB,QAAQ,EAAE,MAAM;oBAChBsF,UAAU,EAAE,KAAK;oBACjBrF,KAAK,EAAE,MAAM;oBACbgF,YAAY,EAAE;kBAChB,CAAE;kBAAA9E,QAAA,GACH,qBACoB,EAAC,CAAA/F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEkJ,MAAM,KAAI,CAAC,EAAC,GAC7C;gBAAA;kBAAAlD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EACJ3F,OAAO,CAACC,GAAG,CAAC,YAAY,EAAET,SAAS,CAAC,eAErCtB,OAAA;kBAAKiM,SAAS,EAAC,gBAAgB;kBAAClG,KAAK,EAAE;oBACrCsG,MAAM,EAAE,gBAAgB;oBACxBC,YAAY,EAAE,KAAK;oBACnBC,eAAe,EAAE,MAAM;oBACvBL,SAAS,EAAE,OAAO;oBAClBuC,SAAS,EAAE;kBACb,CAAE;kBAAApH,QAAA,EACC/F,SAAS,IAAIA,SAAS,CAACkJ,MAAM,GAAG,CAAC,gBAChCxK,OAAA;oBAAAqH,QAAA,EACG,CAAC,GAAG/F,SAAS,CAAC,CAAC0L,GAAG,CAAC,CAACxD,QAAQ,EAAEX,KAAK,kBAClC7I,OAAA;sBAEE+F,KAAK,EAAE;wBACLqG,OAAO,EAAE,MAAM;wBACfa,YAAY,EAAEpE,KAAK,GAAGvH,SAAS,CAACkJ,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,MAAM;wBACtE+B,eAAe,EAAE,MAAM;wBACvBK,UAAU,EAAE;sBACd,CAAE;sBACF8B,YAAY,EAAG/G,CAAC,IAAKA,CAAC,CAACgH,aAAa,CAAC5I,KAAK,CAACwG,eAAe,GAAG,SAAU;sBACvEqC,YAAY,EAAGjH,CAAC,IAAKA,CAAC,CAACgH,aAAa,CAAC5I,KAAK,CAACwG,eAAe,GAAG,MAAO;sBAAAlF,QAAA,eAEpErH,OAAA;wBAAAqH,QAAA,gBACIrH,OAAA;0BAAK+F,KAAK,EAAE;4BAAEe,OAAO,EAAE,MAAM;4BAAEC,cAAc,EAAE,eAAe;4BAAEC,UAAU,EAAE,YAAY;4BAAEmF,YAAY,EAAE;0BAAM,CAAE;0BAAA9E,QAAA,eAC9GrH,OAAA;4BAAK+F,KAAK,EAAE;8BAAEuH,IAAI,EAAE;4BAAE,CAAE;4BAAAjG,QAAA,gBACtBrH,OAAA;8BAAK+F,KAAK,EAAE;gCAAEmB,QAAQ,EAAE,MAAM;gCAAEC,KAAK,EAAE,MAAM;gCAAEqF,UAAU,EAAE,KAAK;gCAAEL,YAAY,EAAE;8BAAM,CAAE;8BAAA9E,QAAA,EACrFmC,QAAQ,CAACnH;4BAAU;8BAAAiF,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACjB,CAAC,eACNzH,OAAA;8BAAK+F,KAAK,EAAE;gCAAEmB,QAAQ,EAAE,MAAM;gCAAEC,KAAK,EAAE,MAAM;gCAAE0H,UAAU,EAAE;8BAAM,CAAE;8BAAAxH,QAAA,GAChEmC,QAAQ,CAAClH,OAAO,EAChBkH,QAAQ,CAACjH,IAAI,iBAAIvC,OAAA,CAAAE,SAAA;gCAAAmH,QAAA,gBAAErH,OAAA;kCAAAsH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAK,CAAC,EAAC+B,QAAQ,CAACjH,IAAI,EAAEiH,QAAQ,CAAChH,KAAK,IAAI,KAAKgH,QAAQ,CAAChH,KAAK,EAAE,EAAEgH,QAAQ,CAAC/G,GAAG,IAAI,IAAI+G,QAAQ,CAAC/G,GAAG,EAAE;8BAAA,eAAG,CAAC;4BAAA;8BAAA6E,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACtH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNzH,OAAA;0BAAK+F,KAAK,EAAE;4BAAEe,OAAO,EAAE,MAAM;4BAAEsG,GAAG,EAAE,KAAK;4BAAEC,QAAQ,EAAE;0BAAO,CAAE;0BAAAhG,QAAA,gBAC5DrH,OAAA;4BACEiO,OAAO,EAAEA,CAAA,KAAMpE,kBAAkB,CAAChB,KAAK,CAAE;4BACzC9C,KAAK,EAAE;8BACLqG,OAAO,EAAE,UAAU;8BACnBlF,QAAQ,EAAE,MAAM;8BAChBC,KAAK,EAAE,SAAS;8BAChBoF,eAAe,EAAE,SAAS;8BAC1BF,MAAM,EAAE,mBAAmB;8BAC3BC,YAAY,EAAE,KAAK;8BACnBwB,MAAM,EAAE,SAAS;8BACjBlB,UAAU,EAAE;4BACd,CAAE;4BACFsB,WAAW,EAAGvG,CAAC,IAAK;8BAClBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;8BAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,MAAM;4BAC/B,CAAE;4BACFgH,UAAU,EAAGxG,CAAC,IAAK;8BACjBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;8BAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,SAAS;4BAClC,CAAE;4BAAAE,QAAA,EACH;0BAED;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC,eACTzH,OAAA;4BACEiO,OAAO,EAAEA,CAAA,KAAM1E,oBAAoB,CAACV,KAAK,CAAE;4BAC3C9C,KAAK,EAAE;8BACLqG,OAAO,EAAE,UAAU;8BACnBlF,QAAQ,EAAE,MAAM;8BAChBC,KAAK,EAAE,SAAS;8BAChBoF,eAAe,EAAE,SAAS;8BAC1BF,MAAM,EAAE,mBAAmB;8BAC3BC,YAAY,EAAE,KAAK;8BACnBwB,MAAM,EAAE,SAAS;8BACjBlB,UAAU,EAAE;4BACd,CAAE;4BACFsB,WAAW,EAAGvG,CAAC,IAAK;8BAClBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;8BAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,MAAM;4BAC/B,CAAE;4BACFgH,UAAU,EAAGxG,CAAC,IAAK;8BACjBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;8BAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,SAAS;4BAClC,CAAE;4BAAAE,QAAA,EACH;0BAED;4BAAAC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAQ,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC,GAtEDoB,KAAK;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuEP,CACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,gBAENzH,OAAA;oBAAK+F,KAAK,EAAE;sBACV+I,SAAS,EAAE,QAAQ;sBACnB3H,KAAK,EAAE,MAAM;sBACbD,QAAQ,EAAE,MAAM;sBAChBkF,OAAO,EAAE,WAAW;sBACpBtF,OAAO,EAAE,MAAM;sBACfiI,aAAa,EAAE,QAAQ;sBACvBhI,cAAc,EAAE,QAAQ;sBACxBC,UAAU,EAAE,QAAQ;sBACpBC,MAAM,EAAE;oBACV,CAAE;oBAAAI,QAAA,gBACArH,OAAA;sBAAK+F,KAAK,EAAE;wBAAEmB,QAAQ,EAAE,MAAM;wBAAEiF,YAAY,EAAE;sBAAO,CAAE;sBAAA9E,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eAChEzH,OAAA;sBAAAqH,QAAA,EAAG;oBAAyB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAChCzH,OAAA;sBAAG+F,KAAK,EAAE;wBAAEmB,QAAQ,EAAE,MAAM;wBAAEC,KAAK,EAAE;sBAAO,CAAE;sBAAAE,QAAA,EAAC;oBAAoD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpG;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNzH,OAAA;gBAAKiM,SAAS,EAAC,0BAA0B;gBAAA5E,QAAA,gBACvCrH,OAAA;kBACE+F,KAAK,EAAE;oBACLmB,QAAQ,EAAE,MAAM;oBAChBsF,UAAU,EAAE,KAAK;oBACjBrF,KAAK,EAAE,MAAM;oBACbgF,YAAY,EAAE;kBAChB,CAAE;kBAAA9E,QAAA,EAED3D,eAAe,KAAK,IAAI,GAAG,eAAe,GAAG;gBAAkB;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9D,CAAC,eACLzH,OAAA;kBAAKiM,SAAS,EAAC,gBAAgB;kBAAA5E,QAAA,gBAC7BrH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEe,OAAO,EAAE,MAAM;sBAAEkI,mBAAmB,EAAE,KAAK;sBAAE5B,GAAG,EAAE,MAAM;sBAAEjB,YAAY,EAAE,MAAM;sBAAEmB,IAAI,EAAE;oBAAI,CAAE;oBAAAjG,QAAA,gBACxGrH,OAAA;sBACE0B,IAAI,EAAC,MAAM;sBACXgF,KAAK,EAAEhD,eAAe,KAAK,IAAI,GAAGE,YAAY,CAACvB,UAAU,GAAGF,WAAW,CAACE,UAAW;sBACnFoK,QAAQ,EAAG9E,CAAC,IAAK;wBACf,IAAIjE,eAAe,KAAK,IAAI,EAAE;0BAC5BG,eAAe,CAAC;4BAAE,GAAGD,YAAY;4BAAEvB,UAAU,EAAEsF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAClE,CAAC,MAAM;0BACLtE,cAAc,CAAC;4BAAE,GAAGD,WAAW;4BAAEE,UAAU,EAAEsF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAChE;sBACF,CAAE;sBACF6G,WAAW,EAAC,mCAAmC;sBAC/CxH,KAAK,EAAE;wBACLqG,OAAO,EAAE,MAAM;wBACflF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAW;sBACzDC,MAAM,EAAGpF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG;oBAAQ;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACFzH,OAAA;sBACE0B,IAAI,EAAC,MAAM;sBACXgF,KAAK,EAAEhD,eAAe,KAAK,IAAI,GAAGE,YAAY,CAACtB,OAAO,GAAGH,WAAW,CAACG,OAAQ;sBAC7EmK,QAAQ,EAAG9E,CAAC,IAAK;wBACf,IAAIjE,eAAe,KAAK,IAAI,EAAE;0BAC5BG,eAAe,CAAC;4BAAE,GAAGD,YAAY;4BAAEtB,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAC/D,CAAC,MAAM;0BACLtE,cAAc,CAAC;4BAAE,GAAGD,WAAW;4BAAEG,OAAO,EAAEqF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAC7D;sBACF,CAAE;sBACF6G,WAAW,EAAC,gBAAgB;sBAC5BxH,KAAK,EAAE;wBACLqG,OAAO,EAAE,MAAM;wBACflF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAW;sBACzDC,MAAM,EAAGpF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG;oBAAQ;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNzH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEe,OAAO,EAAE,MAAM;sBAAEkI,mBAAmB,EAAE,aAAa;sBAAE5B,GAAG,EAAE,MAAM;sBAAEjB,YAAY,EAAE;oBAAO,CAAE;oBAAA9E,QAAA,gBACrGrH,OAAA;sBACE0B,IAAI,EAAC,MAAM;sBACXgF,KAAK,EAAEhD,eAAe,KAAK,IAAI,GAAGE,YAAY,CAACrB,IAAI,GAAGJ,WAAW,CAACI,IAAK;sBACvEkK,QAAQ,EAAG9E,CAAC,IAAK;wBACf,IAAIjE,eAAe,KAAK,IAAI,EAAE;0BAC5BG,eAAe,CAAC;4BAAE,GAAGD,YAAY;4BAAErB,IAAI,EAAEoF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAC5D,CAAC,MAAM;0BACLtE,cAAc,CAAC;4BAAE,GAAGD,WAAW;4BAAEI,IAAI,EAAEoF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAC1D;sBACF,CAAE;sBACF6G,WAAW,EAAC,MAAM;sBAClBxH,KAAK,EAAE;wBACLqG,OAAO,EAAE,MAAM;wBACflF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAW;sBACzDC,MAAM,EAAGpF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG;oBAAQ;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACFzH,OAAA;sBACE0B,IAAI,EAAC,MAAM;sBACXgF,KAAK,EAAEhD,eAAe,KAAK,IAAI,GAAGE,YAAY,CAACpB,KAAK,GAAGL,WAAW,CAACK,KAAM;sBACzEiK,QAAQ,EAAG9E,CAAC,IAAK;wBACf,IAAIjE,eAAe,KAAK,IAAI,EAAE;0BAC5BG,eAAe,CAAC;4BAAE,GAAGD,YAAY;4BAAEpB,KAAK,EAAEmF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAC7D,CAAC,MAAM;0BACLtE,cAAc,CAAC;4BAAE,GAAGD,WAAW;4BAAEK,KAAK,EAAEmF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAC3D;sBACF,CAAE;sBACF6G,WAAW,EAAC,OAAO;sBACnBxH,KAAK,EAAE;wBACLqG,OAAO,EAAE,MAAM;wBACflF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAW;sBACzDC,MAAM,EAAGpF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG;oBAAQ;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACFzH,OAAA;sBACE0B,IAAI,EAAC,MAAM;sBACXgF,KAAK,EAAEhD,eAAe,KAAK,IAAI,GAAGE,YAAY,CAACnB,GAAG,GAAGN,WAAW,CAACM,GAAI;sBACrEgK,QAAQ,EAAG9E,CAAC,IAAK;wBACf,IAAIjE,eAAe,KAAK,IAAI,EAAE;0BAC5BG,eAAe,CAAC;4BAAE,GAAGD,YAAY;4BAAEnB,GAAG,EAAEkF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBAC3D,CAAC,MAAM;0BACLtE,cAAc,CAAC;4BAAE,GAAGD,WAAW;4BAAEM,GAAG,EAAEkF,CAAC,CAACC,MAAM,CAAClB;0BAAM,CAAC,CAAC;wBACzD;sBACF,CAAE;sBACF6G,WAAW,EAAC,UAAU;sBACtBxH,KAAK,EAAE;wBACLqG,OAAO,EAAE,MAAM;wBACflF,QAAQ,EAAE,MAAM;wBAChBmF,MAAM,EAAE,gBAAgB;wBACxBC,YAAY,EAAE,KAAK;wBACnBK,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE;sBACd,CAAE;sBACFC,OAAO,EAAGlF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG,SAAW;sBACzDC,MAAM,EAAGpF,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAAC+G,WAAW,GAAG;oBAAQ;sBAAAxF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNzH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEqI,SAAS,EAAE,MAAM;sBAAEtH,OAAO,EAAE,MAAM;sBAAEsG,GAAG,EAAE;oBAAO,CAAE;oBAAA/F,QAAA,EAC7D3D,eAAe,KAAK,IAAI,gBACvB1D,OAAA,CAAAE,SAAA;sBAAAmH,QAAA,gBACErH,OAAA;wBACEiO,OAAO,EAAEnE,cAAe;wBACxB/D,KAAK,EAAE;0BACLuH,IAAI,EAAE,GAAG;0BACTlB,OAAO,EAAE,WAAW;0BACpBlF,QAAQ,EAAE,MAAM;0BAChBsF,UAAU,EAAE,KAAK;0BACjBrF,KAAK,EAAE,MAAM;0BACboF,eAAe,EAAE,SAAS;0BAC1BF,MAAM,EAAE,MAAM;0BACdC,YAAY,EAAE,KAAK;0BACnBwB,MAAM,EAAE,SAAS;0BACjBlB,UAAU,EAAE;wBACd,CAAE;wBACFsB,WAAW,EAAGvG,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAW;wBACjE4B,UAAU,EAAGxG,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAW;wBAAAlF,QAAA,EACjE;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTzH,OAAA;wBACEiO,OAAO,EAAEjE,gBAAiB;wBAC1BjE,KAAK,EAAE;0BACLuH,IAAI,EAAE,GAAG;0BACTlB,OAAO,EAAE,WAAW;0BACpBlF,QAAQ,EAAE,MAAM;0BAChBsF,UAAU,EAAE,KAAK;0BACjBrF,KAAK,EAAE,MAAM;0BACboF,eAAe,EAAE,SAAS;0BAC1BF,MAAM,EAAE,gBAAgB;0BACxBC,YAAY,EAAE,KAAK;0BACnBwB,MAAM,EAAE,SAAS;0BACjBlB,UAAU,EAAE;wBACd,CAAE;wBACFsB,WAAW,EAAGvG,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAW;wBACjE4B,UAAU,EAAGxG,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAW;wBAAAlF,QAAA,EACjE;sBAED;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA,eACT,CAAC,gBAEHzH,OAAA;sBACEiO,OAAO,EAAEhE,iBAAkB;sBAC3BlE,KAAK,EAAE;wBACL2G,KAAK,EAAE,MAAM;wBACbN,OAAO,EAAE,WAAW;wBACpBlF,QAAQ,EAAE,MAAM;wBAChBsF,UAAU,EAAE,KAAK;wBACjBrF,KAAK,EAAE,MAAM;wBACboF,eAAe,EAAE,SAAS;wBAC1BF,MAAM,EAAE,MAAM;wBACdC,YAAY,EAAE,KAAK;wBACnBwB,MAAM,EAAE,SAAS;wBACjBlB,UAAU,EAAE;sBACd,CAAE;sBACFsB,WAAW,EAAGvG,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAW;sBACjE4B,UAAU,EAAGxG,CAAC,IAAMA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAW;sBAAAlF,QAAA,EACjE;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBACT;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGVzH,OAAA;UAASiM,SAAS,EAAC,kBAAkB;UAAA5E,QAAA,gBACnCrH,OAAA;YAAIiM,SAAS,EAAC,yBAAyB;YAAA5E,QAAA,EAAC;UAExC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzH,OAAA;YAAKiM,SAAS,EAAC,0BAA0B;YAAA5E,QAAA,gBAIzCrH,OAAA;cACE+F,KAAK,EAAE;gBACLe,OAAO,EAAE,MAAM;gBACfiI,aAAa,EAAE,QAAQ;gBACvB3B,GAAG,EAAE,MAAM;gBACXgB,SAAS,EAAE;cACb,CAAE;cAAA/G,QAAA,gBAGFrH,OAAA;gBACE+F,KAAK,EAAE;kBACLe,OAAO,EAAE,MAAM;kBACfsG,GAAG,EAAE,MAAM;kBACXpG,UAAU,EAAE,YAAY;kBACxBoH,SAAS,EAAE;gBACb,CAAE;gBAAA/G,QAAA,gBAGFrH,OAAA;kBAAK+F,KAAK,EAAE;oBAAEuH,IAAI,EAAE;kBAAI,CAAE;kBAAAjG,QAAA,gBACxBrH,OAAA;oBACEiP,OAAO,EAAC,aAAa;oBACrBlJ,KAAK,EAAE;sBACLe,OAAO,EAAE,OAAO;sBAChBI,QAAQ,EAAE,MAAM;sBAChBsF,UAAU,EAAE,KAAK;sBACjBrF,KAAK,EAAE,MAAM;sBACbgF,YAAY,EAAE;oBAChB,CAAE;oBAAA9E,QAAA,GACH,eACc,eAAArH,OAAA;sBAAM+F,KAAK,EAAE;wBAAEoB,KAAK,EAAE;sBAAM,CAAE;sBAAAE,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACRzH,OAAA;oBACEgL,EAAE,EAAC,aAAa;oBAChBtJ,IAAI,EAAC,MAAM;oBACXgF,KAAK,EAAEtG,IAAK;oBACZqM,QAAQ,EAAG9E,CAAC,IAAKvD,OAAO,CAACuD,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;oBACzC6G,WAAW,EAAC,oBAAoB;oBAChCxH,KAAK,EAAE;sBACL2G,KAAK,EAAE,MAAM;sBACbN,OAAO,EAAE,MAAM;sBACflF,QAAQ,EAAE,MAAM;sBAChBmF,MAAM,EAAE,gBAAgB;sBACxBC,YAAY,EAAE;oBAChB;kBAAE;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNzH,OAAA;kBAAK+F,KAAK,EAAE;oBAAEuH,IAAI,EAAE;kBAAI,CAAE;kBAAAjG,QAAA,gBACxBrH,OAAA;oBACEiP,OAAO,EAAC,aAAa;oBACrBlJ,KAAK,EAAE;sBACLe,OAAO,EAAE,OAAO;sBAChBI,QAAQ,EAAE,MAAM;sBAChBsF,UAAU,EAAE,KAAK;sBACjBrF,KAAK,EAAE,MAAM;sBACbgF,YAAY,EAAE;oBAChB,CAAE;oBAAA9E,QAAA,GACH,eACc,eAAArH,OAAA;sBAAM+F,KAAK,EAAE;wBAAEoB,KAAK,EAAE;sBAAM,CAAE;sBAAAE,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACRzH,OAAA;oBACEgL,EAAE,EAAC,aAAa;oBAChBtJ,IAAI,EAAC,MAAM;oBACXgF,KAAK,EAAE5C,OAAQ;oBACf2I,QAAQ,EAAG9E,CAAC,IAAK5D,UAAU,CAAC4D,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;oBAC5C6G,WAAW,EAAC,0BAA0B;oBACtCxH,KAAK,EAAE;sBACL2G,KAAK,EAAE,MAAM;sBACbN,OAAO,EAAE,MAAM;sBACflF,QAAQ,EAAE,MAAM;sBAChBmF,MAAM,EAAE,gBAAgB;sBACxBC,YAAY,EAAE;oBAChB;kBAAE;oBAAAhF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAGNzH,OAAA;kBACEiO,OAAO,EAAE/D,kBAAmB;kBAC5BnE,KAAK,EAAE;oBACLqI,SAAS,EAAE,MAAM;oBAAE;oBACnBhC,OAAO,EAAE,WAAW;oBACpBlF,QAAQ,EAAE,MAAM;oBAChBC,KAAK,EAAE,MAAM;oBACboF,eAAe,EAAE,SAAS;oBAC1BF,MAAM,EAAE,MAAM;oBACdC,YAAY,EAAE,KAAK;oBACnBwB,MAAM,EAAE,SAAS;oBACjBF,UAAU,EAAE;kBACd,CAAE;kBAAAvG,QAAA,eAEFrH,OAAA,CAACJ,OAAO;oBAAC0O,IAAI,EAAE;kBAAG;oBAAAhH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAGNzH,OAAA;gBAAAqH,QAAA,gBACErH,OAAA;kBACEiP,OAAO,EAAC,oBAAoB;kBAC5BlJ,KAAK,EAAE;oBACLe,OAAO,EAAE,OAAO;oBAChBI,QAAQ,EAAE,MAAM;oBAChBsF,UAAU,EAAE,KAAK;oBACjBrF,KAAK,EAAE,MAAM;oBACbgF,YAAY,EAAE;kBAChB,CAAE;kBAAA9E,QAAA,GACH,aAEC,eAAArH,OAAA;oBAAM+F,KAAK,EAAE;sBAAEmB,QAAQ,EAAE,MAAM;sBAAEC,KAAK,EAAE;oBAAO,CAAE;oBAAAE,QAAA,EAAC;kBAElD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACRzH,OAAA;kBACEgL,EAAE,EAAC,oBAAoB;kBACvBtE,KAAK,EAAErC,WAAY;kBACnBoI,QAAQ,EAAG9E,CAAC,IAAKrD,cAAc,CAACqD,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;kBAChDwI,IAAI,EAAC,GAAG;kBACR3B,WAAW,EAAC,2BAA2B;kBACvCxH,KAAK,EAAE;oBACL2G,KAAK,EAAE,MAAM;oBACbN,OAAO,EAAE,MAAM;oBACflF,QAAQ,EAAE,MAAM;oBAChBmF,MAAM,EAAE,gBAAgB;oBACxBC,YAAY,EAAE,KAAK;oBACnB6C,MAAM,EAAE;kBACV;gBAAE;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eAGNzH,OAAA;gBAAK+F,KAAK,EAAE;kBAAEqI,SAAS,EAAE;gBAAO,CAAE;gBAAA/G,QAAA,eAChCrH,OAAA;kBACEiO,OAAO,EAAEvD,QAAS;kBAClB3E,KAAK,EAAE;oBACLqG,OAAO,EAAE,WAAW;oBACpBlF,QAAQ,EAAE,MAAM;oBAChBC,KAAK,EAAE,MAAM;oBACboF,eAAe,EAAE,SAAS;oBAC1BF,MAAM,EAAE,MAAM;oBACdC,YAAY,EAAE,KAAK;oBACnBwB,MAAM,EAAE;kBACV,CAAE;kBAAAzG,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNzH,OAAA;cACE+F,KAAK,EAAE;gBACLqI,SAAS,EAAE,MAAM;gBACjB7B,eAAe,EAAE,MAAM;gBACvBH,OAAO,EAAE,MAAM;gBACfE,YAAY,EAAE,KAAK;gBACnBD,MAAM,EAAE;cACV,CAAE;cAAAhF,QAAA,gBAEFrH,OAAA;gBACE+F,KAAK,EAAE;kBACLmB,QAAQ,EAAE,MAAM;kBAChBsF,UAAU,EAAE,KAAK;kBACjBrF,KAAK,EAAE,MAAM;kBACbgF,YAAY,EAAE,MAAM;kBACpBc,YAAY,EAAE,gBAAgB;kBAC9BC,aAAa,EAAE;gBACjB,CAAE;gBAAA7F,QAAA,GACH,uBACsB,EAACnD,UAAU,CAACsG,MAAM,EAAC,GAC1C;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,EAEJvD,UAAU,IAAIA,UAAU,CAACsG,MAAM,GAAG,CAAC,gBAClCxK,OAAA;gBAAK+F,KAAK,EAAE;kBAAEmG,SAAS,EAAE,OAAO;kBAAEuC,SAAS,EAAE;gBAAO,CAAE;gBAAApH,QAAA,EACnDnD,UAAU,CAAC8I,GAAG,CAAC,CAACrC,IAAI,EAAE9B,KAAK,kBAC1B7I,OAAA;kBAEE+F,KAAK,EAAE;oBACLqG,OAAO,EAAE,UAAU;oBACnBa,YAAY,EAAEpE,KAAK,GAAG3E,UAAU,CAACsG,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,MAAM;oBACvE1D,OAAO,EAAE,MAAM;oBACfC,cAAc,EAAE,eAAe;oBAC/BC,UAAU,EAAE,QAAQ;oBACpBuF,eAAe,EAAE,MAAM;oBACvBK,UAAU,EAAE,uBAAuB;oBACnCwC,SAAS,EAAE;kBACb,CAAE;kBACFV,YAAY,EAAG/G,CAAC,IAAMA,CAAC,CAACgH,aAAa,CAAC5I,KAAK,CAACwG,eAAe,GAAG,SAAW;kBACzEqC,YAAY,EAAGjH,CAAC,IAAMA,CAAC,CAACgH,aAAa,CAAC5I,KAAK,CAACwG,eAAe,GAAG,MAAQ;kBAAAlF,QAAA,gBAEtErH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEuH,IAAI,EAAE;oBAAE,CAAE;oBAAAjG,QAAA,gBACtBrH,OAAA;sBAAK+F,KAAK,EAAE;wBAAEmB,QAAQ,EAAE,MAAM;wBAAEsF,UAAU,EAAE,KAAK;wBAAErF,KAAK,EAAE,MAAM;wBAAEgF,YAAY,EAAE;sBAAM,CAAE;sBAAA9E,QAAA,EACrFsD,IAAI,CAACvK;oBAAI;sBAAAkH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACP,CAAC,eACNzH,OAAA;sBAAK+F,KAAK,EAAE;wBAAEmB,QAAQ,EAAE,MAAM;wBAAEC,KAAK,EAAE;sBAAO,CAAE;sBAAAE,QAAA,GAAC,QACzC,eAAArH,OAAA;wBAAM+F,KAAK,EAAE;0BAAEyG,UAAU,EAAE;wBAAM,CAAE;wBAAAnF,QAAA,EAAEsD,IAAI,CAACA;sBAAI;wBAAArD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,EACLkD,IAAI,CAACtG,WAAW,iBACfrE,OAAA;sBAAK+F,KAAK,EAAE;wBAAEmB,QAAQ,EAAE,MAAM;wBAAEC,KAAK,EAAE,MAAM;wBAAEiH,SAAS,EAAE;sBAAM,CAAE;sBAAA/G,QAAA,EAC/DsD,IAAI,CAACtG;oBAAW;sBAAAiD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACNzH,OAAA;oBAAK+F,KAAK,EAAE;sBAAEe,OAAO,EAAE,MAAM;sBAAEsG,GAAG,EAAE,KAAK;sBAAEpG,UAAU,EAAE;oBAAS,CAAE;oBAAAK,QAAA,gBAChErH,OAAA;sBACEiO,OAAO,EAAEA,CAAA,KAAMpC,cAAc,CAAClB,IAAI,CAACA,IAAI,CAAE;sBACzC5E,KAAK,EAAE;wBACLqG,OAAO,EAAE,SAAS;wBAClBlF,QAAQ,EAAE,MAAM;wBAChBC,KAAK,EAAE,SAAS;wBAChBoF,eAAe,EAAE,SAAS;wBAC1BF,MAAM,EAAE,mBAAmB;wBAC3BC,YAAY,EAAE,KAAK;wBACnBwB,MAAM,EAAE,SAAS;wBACjBlB,UAAU,EAAE;sBACd,CAAE;sBACFsB,WAAW,EAAGvG,CAAC,IAAK;wBAClBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;wBAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,MAAM;sBAC/B,CAAE;sBACFgH,UAAU,EAAGxG,CAAC,IAAK;wBACjBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;wBAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,SAAS;sBAClC,CAAE;sBAAAE,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTzH,OAAA;sBACEiO,OAAO,EAAEA,CAAA,KAAMlD,gBAAgB,CAAClC,KAAK,EAAE8B,IAAI,CAACK,EAAE,CAAE;sBAChDjF,KAAK,EAAE;wBACLqG,OAAO,EAAE,SAAS;wBAClBlF,QAAQ,EAAE,MAAM;wBAChBC,KAAK,EAAE,SAAS;wBAChBoF,eAAe,EAAE,SAAS;wBAC1BF,MAAM,EAAE,mBAAmB;wBAC3BC,YAAY,EAAE,KAAK;wBACnBwB,MAAM,EAAE,SAAS;wBACjBlB,UAAU,EAAE;sBACd,CAAE;sBACFsB,WAAW,EAAGvG,CAAC,IAAK;wBAClBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;wBAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,MAAM;sBAC/B,CAAE;sBACFgH,UAAU,EAAGxG,CAAC,IAAK;wBACjBA,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACwG,eAAe,GAAG,SAAS;wBAC1C5E,CAAC,CAACC,MAAM,CAAC7B,KAAK,CAACoB,KAAK,GAAG,SAAS;sBAClC,CAAE;sBAAAE,QAAA,EACH;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA,GA1EDoB,KAAK;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA2EP,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENzH,OAAA;gBACE+F,KAAK,EAAE;kBACL+I,SAAS,EAAE,QAAQ;kBACnB3H,KAAK,EAAE,MAAM;kBACbD,QAAQ,EAAE,MAAM;kBAChBkF,OAAO,EAAE;gBACX,CAAE;gBAAA/E,QAAA,gBAEFrH,OAAA;kBAAAqH,QAAA,EAAG;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAClCzH,OAAA;kBAAG+F,KAAK,EAAE;oBAAEmB,QAAQ,EAAE,MAAM;oBAAEC,KAAK,EAAE;kBAAO,CAAE;kBAAAE,QAAA,EAAC;gBAE/C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGT9G,MAAM,KAAK,CAAC,iBACXX,OAAA;UAASiM,SAAS,EAAC,kBAAkB;UAAA5E,QAAA,gBACnCrH,OAAA;YACEiM,SAAS,EAAC,yBAAyB;YACnClG,KAAK,EAAE;cACL+H,MAAM,EAAE,SAAS;cACjBhH,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,eAAe;cAC/BqF,OAAO,EAAE,QAAQ;cACjBiD,MAAM,EAAE,GAAG;cACXnI,QAAQ,EAAE,MAAM;cAChBsF,UAAU,EAAE,KAAK;cACjBrF,KAAK,EAAE,MAAM;cACb8F,YAAY,EAAE,oCAAoC;cAClDmC,SAAS,EAAE;YACb,CAAE;YACFnB,OAAO,EAAEA,CAAA,KAAMhL,uBAAuB,CAAC,CAACD,oBAAoB,CAAE;YAAAqE,QAAA,gBAE9DrH,OAAA;cAAAqH,QAAA,EAAM;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnCzH,OAAA;cAAM+F,KAAK,EAAE;gBACXmB,QAAQ,EAAE,MAAM;gBAChB0F,UAAU,EAAE,qBAAqB;gBACjC0C,SAAS,EAAEtM,oBAAoB,GAAG,eAAe,GAAG;cACtD,CAAE;cAAAqE,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,EAEJzE,oBAAoB,iBACnBhD,OAAA;YAAKiM,SAAS,EAAC,0BAA0B;YAAA5E,QAAA,gBACvCrH,OAAA;cAAK+F,KAAK,EAAE;gBACVwG,eAAe,EAAE,SAAS;gBAC1BF,MAAM,EAAE,mBAAmB;gBAC3BC,YAAY,EAAE,KAAK;gBACnBF,OAAO,EAAE,MAAM;gBACfD,YAAY,EAAE;cAChB,CAAE;cAAA9E,QAAA,eACArH,OAAA;gBAAG+F,KAAK,EAAE;kBAAEsJ,MAAM,EAAE,CAAC;kBAAEnI,QAAQ,EAAE,MAAM;kBAAEC,KAAK,EAAE;gBAAU,CAAE;gBAAAE,QAAA,gBAC1DrH,OAAA;kBAAAqH,QAAA,EAAQ;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6GACtC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNzH,OAAA;cAAK+F,KAAK,EAAE;gBAAEoG,YAAY,EAAE;cAAO,CAAE;cAAA9E,QAAA,gBACnCrH,OAAA;gBAAO+F,KAAK,EAAE;kBACZe,OAAO,EAAE,OAAO;kBAChBI,QAAQ,EAAE,MAAM;kBAChBsF,UAAU,EAAE,KAAK;kBACjBrF,KAAK,EAAE,MAAM;kBACbgF,YAAY,EAAE;gBAChB,CAAE;gBAAA9E,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzH,OAAA;gBACE0G,KAAK,EAAE,EAAA5F,qBAAA,GAAA4B,UAAU,CAACE,UAAU,cAAA9B,qBAAA,uBAArBA,qBAAA,CAAuByO,gBAAgB,KAAI,EAAG;gBACrD9C,QAAQ,EAAG9E,CAAC,IAAKnB,cAAc,CAAC,kBAAkB,EAAEmB,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;gBACpE6G,WAAW,EAAC,mDAAmD;gBAC/D2B,IAAI,EAAE,CAAE;gBACRnJ,KAAK,EAAE;kBACL2G,KAAK,EAAE,MAAM;kBACbN,OAAO,EAAE,MAAM;kBACflF,QAAQ,EAAE,MAAM;kBAChBmF,MAAM,EAAE,gBAAgB;kBACxBC,YAAY,EAAE,KAAK;kBACnBK,OAAO,EAAE,MAAM;kBACfwC,MAAM,EAAE,UAAU;kBAClB/H,UAAU,EAAE;gBACd;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNzH,OAAA;cAAK+F,KAAK,EAAE;gBAAEoG,YAAY,EAAE;cAAO,CAAE;cAAA9E,QAAA,gBACnCrH,OAAA;gBAAO+F,KAAK,EAAE;kBACZe,OAAO,EAAE,OAAO;kBAChBI,QAAQ,EAAE,MAAM;kBAChBsF,UAAU,EAAE,KAAK;kBACjBrF,KAAK,EAAE,MAAM;kBACbgF,YAAY,EAAE;gBAChB,CAAE;gBAAA9E,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzH,OAAA;gBACE0G,KAAK,EAAE,EAAA3F,qBAAA,GAAA2B,UAAU,CAACG,gBAAgB,cAAA9B,qBAAA,uBAA3BA,qBAAA,CAA6ByO,kBAAkB,KAAI,EAAG;gBAC7D/C,QAAQ,EAAG9E,CAAC,IAAKf,oBAAoB,CAAC,oBAAoB,EAAEe,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;gBAC5E6G,WAAW,EAAC,wDAAwD;gBACpE2B,IAAI,EAAE,CAAE;gBACRnJ,KAAK,EAAE;kBACL2G,KAAK,EAAE,MAAM;kBACbN,OAAO,EAAE,MAAM;kBACflF,QAAQ,EAAE,MAAM;kBAChBmF,MAAM,EAAE,gBAAgB;kBACxBC,YAAY,EAAE,KAAK;kBACnBK,OAAO,EAAE,MAAM;kBACfwC,MAAM,EAAE,UAAU;kBAClB/H,UAAU,EAAE;gBACd;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNzH,OAAA;cAAK+F,KAAK,EAAE;gBAAEoG,YAAY,EAAE;cAAO,CAAE;cAAA9E,QAAA,gBACnCrH,OAAA;gBAAO+F,KAAK,EAAE;kBACZe,OAAO,EAAE,OAAO;kBAChBI,QAAQ,EAAE,MAAM;kBAChBsF,UAAU,EAAE,KAAK;kBACjBrF,KAAK,EAAE,MAAM;kBACbgF,YAAY,EAAE;gBAChB,CAAE;gBAAA9E,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRzH,OAAA;gBACE0G,KAAK,EAAE,EAAA1F,sBAAA,GAAA0B,UAAU,CAACG,gBAAgB,cAAA7B,sBAAA,uBAA3BA,sBAAA,CAA6ByO,wBAAwB,KAAI,EAAG;gBACnEhD,QAAQ,EAAG9E,CAAC,IAAKf,oBAAoB,CAAC,0BAA0B,EAAEe,CAAC,CAACC,MAAM,CAAClB,KAAK,CAAE;gBAClF6G,WAAW,EAAC,yDAAyD;gBACrE2B,IAAI,EAAE,CAAE;gBACRnJ,KAAK,EAAE;kBACL2G,KAAK,EAAE,MAAM;kBACbN,OAAO,EAAE,MAAM;kBACflF,QAAQ,EAAE,MAAM;kBAChBmF,MAAM,EAAE,gBAAgB;kBACxBC,YAAY,EAAE,KAAK;kBACnBK,OAAO,EAAE,MAAM;kBACfwC,MAAM,EAAE,UAAU;kBAClB/H,UAAU,EAAE;gBACd;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAGNzH,OAAA;cAAK+F,KAAK,EAAE;gBAAE+I,SAAS,EAAE;cAAQ,CAAE;cAAAzH,QAAA,eACjCrH,OAAA;gBACEiO,OAAO,EAAE9H,cAAe;gBACxBuJ,QAAQ,EAAE5M,iBAAkB;gBAC5BiD,KAAK,EAAE;kBACLqG,OAAO,EAAE,WAAW;kBACpBlF,QAAQ,EAAE,MAAM;kBAChBsF,UAAU,EAAE,KAAK;kBACjBrF,KAAK,EAAE,MAAM;kBACboF,eAAe,EAAEzJ,iBAAiB,GAAG,MAAM,GAAG,SAAS;kBACvDuJ,MAAM,EAAE,MAAM;kBACdC,YAAY,EAAE,KAAK;kBACnBwB,MAAM,EAAEhL,iBAAiB,GAAG,aAAa,GAAG,SAAS;kBACrD8J,UAAU,EAAE;gBACd,CAAE;gBAAAvF,QAAA,EAEDvE,iBAAiB,GAAG,WAAW,GAAG;cAAkB;gBAAAwE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP;AAAC5G,EAAA,CA7rEQL,QAAQ;EAAA,QAWXf,UAAU,EAkD8BC,WAAW;AAAA;AAAAiQ,EAAA,GA7DhDnP,QAAQ;AA8rEjB,eAAeA,QAAQ;AAAC,IAAAmP,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}