<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Responder Dashboard</title>
    <link rel="stylesheet" href="/frontend/src/styles.css" />
    <style>
      body {
        margin: 0;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }
      .container {
        max-width: 100%;
        margin: 0 auto;
        flex: 1;
        padding: 10px;
      }
      .card {
        background-color: #fff;
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
      }
      h1,
      h2 {
        color: #1a73e8;
        margin-bottom: 15px;
        text-align: center;
      }
      h1 {
        font-size: 1.8rem;
        font-weight: 700;
      }
      h2 {
        font-size: 1.4rem;
        font-weight: 600;
      }
      p {
        color: #333;
        margin: 8px 0;
        line-height: 1.6;
      }
      .highlight {
        background: linear-gradient(135deg, #e6f0fa, #d1e7dd);
        padding: 15px;
        border-radius: 10px;
        margin: 15px 0;
        font-weight: bold;
        border-left: 4px solid #1a73e8;
      }
      .event-date-time {
        background: linear-gradient(135deg, #fff3cd, #f8d7da);
        padding: 12px;
        border-radius: 8px;
        margin: 10px 0;
        border-left: 4px solid #ff6b35;
        font-weight: 600;
        text-align: center;
      }
      .noe-badge {
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        display: inline-block;
        margin: 10px 0;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .status {
        font-weight: bold;
        margin: 10px 0;
      }
      .status.enabled {
        color: #388e3c;
      }
      .status.disabled {
        color: #d32f2f;
      }
      .status-buttons {
        display: grid;
        gap: 12px;
        margin-top: 1.5rem;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }
      .status-buttons button {
        padding: 15px 24px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
      }
      .status-buttons button:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
      }
      .status-buttons button:hover:before {
        left: 100%;
      }
      .status-buttons button.active {
        opacity: 0.6;
        cursor: default;
      }
      .status-buttons .btn-participate {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: #fff;
      }
      .status-buttons .btn-participate:hover:not(.active) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
      }
      .status-buttons .btn-unable {
        background: linear-gradient(135deg, #ffc107, #fd7e14);
        color: #000;
      }
      .status-buttons .btn-unable:hover:not(.active) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 193, 7, 0.4);
      }
      .status-buttons .btn-remove {
        background: linear-gradient(135deg, #dc3545, #e74c3c);
        color: #fff;
      }
      .status-buttons .btn-remove:hover:not(.active) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220, 53, 69, 0.4);
      }
      .status-buttons .btn-success {
        background: linear-gradient(135deg, #1a73e8, #0d6efd);
        color: #fff;
      }
      .status-buttons .btn-success:hover:not(.active) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(26, 115, 232, 0.4);
      }
      .status-buttons .btn-warning {
        background: linear-gradient(135deg, #388e3c, #198754);
        color: #fff;
      }
      .status-buttons .btn-warning:hover:not(.active) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(56, 142, 60, 0.4);
      }
      .status-buttons .btn-danger {
        background: linear-gradient(135deg, #d32f2f, #dc3545);
        color: #fff;
      }
      .status-buttons .btn-danger:hover:not(.active) {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(211, 47, 47, 0.4);
      }
      .btn-primary {
        width: 100%;
        padding: 16px 24px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        background: linear-gradient(135deg, #1a73e8, #0d6efd);
        color: #fff;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-top: 15px;
        position: relative;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
      }
      .btn-primary:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
      }
      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(26, 115, 232, 0.4);
      }
      .btn-primary:hover:before {
        left: 100%;
      }
      .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
      }
      .route-button-container {
        margin-top: 15px;
        width: 100%;
      }
      #map {
        width: 100%;
        height: 300px;
        margin-top: 1.5rem;
        border-radius: 5px;
      }
      #chat {
        margin-top: 1.5rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .chat-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 20px 20px 0 0;
      }

      .chat-header h2 {
        margin: 0;
        font-size: 1.3rem;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .btn-ai-summary {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .btn-ai-summary:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      #chat-messages {
        list-style: none;
        padding: 20px;
        margin: 0;
        max-height: 400px;
        overflow-y: auto;
        background: white;
        scroll-behavior: smooth;
      }

      #chat-messages::-webkit-scrollbar {
        width: 6px;
      }

      #chat-messages::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
      }

      #chat-messages::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 10px;
      }

      #chat-messages li {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        margin-bottom: 12px;
        padding: 12px 16px;
        border-radius: 18px;
        border-left: 4px solid #667eea;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        animation: slideIn 0.3s ease-out;
        font-size: 14px;
        line-height: 1.4;
      }

      #chat-messages li:before {
        content: '💬';
        position: absolute;
        left: -2px;
        top: -2px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      #chat-form {
        display: flex;
        gap: 12px;
        padding: 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0 0 20px 20px;
        align-items: center;
      }

      #chat-input {
        flex: 1;
        padding: 14px 20px;
        border-radius: 25px;
        border: 2px solid #e9ecef;
        background: white;
        font-size: 14px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      #chat-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        transform: translateY(-1px);
      }

      #chat-form .btn-primary {
        width: auto;
        padding: 14px 24px;
        margin: 0;
        border-radius: 25px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-size: 14px;
        font-weight: 600;
        min-width: 100px;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      }

      #chat-form .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
      }

      #chat-summary {
        margin: 20px;
        padding: 0;
      }

      .ai-summary {
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        border: 2px solid #ffb74d;
        border-radius: 15px;
        padding: 16px;
        margin-bottom: 15px;
        box-shadow: 0 4px 15px rgba(255, 183, 77, 0.2);
      }

      .ai-summary h3 {
        margin: 0 0 10px 0;
        color: #e65100;
        font-size: 1.1rem;
        font-weight: 700;
      }

      .ai-summary p, .ai-summary pre {
        margin: 0;
        color: #bf360c;
        font-size: 13px;
        line-height: 1.5;
      }

      .ai-summary pre {
        white-space: pre-wrap;
        font-family: inherit;
      }
      /* Responsive Design */
      @media (max-width: 768px) {
        .container {
          padding: 8px;
        }
        .card {
          padding: 16px;
          margin-bottom: 16px;
        }
        .status-buttons {
          grid-template-columns: 1fr;
          gap: 10px;
        }
        .status-buttons button {
          padding: 14px 20px;
          font-size: 15px;
        }
        h1 {
          font-size: 1.5rem;
        }
        h2 {
          font-size: 1.2rem;
        }
        #chat-form {
          flex-direction: row;
          gap: 8px;
          padding: 15px;
        }

        #chat-input {
          padding: 12px 16px;
          font-size: 16px;
        }

        #chat-form .btn-primary {
          padding: 12px 20px;
          min-width: 80px;
          font-size: 13px;
        }

        .chat-header {
          padding: 15px;
        }

        .chat-header h2 {
          font-size: 1.1rem;
        }

        .btn-ai-summary {
          padding: 6px 12px;
          font-size: 11px;
        }

        #chat-messages {
          padding: 15px;
          max-height: 300px;
        }

        #chat-messages li {
          padding: 10px 14px;
          font-size: 13px;
        }
        #map {
          height: 250px;
        }
      }
      
      @media (max-width: 480px) {
        .card {
          padding: 12px;
          border-radius: 10px;
        }
        .status-buttons button {
          padding: 12px 16px;
          font-size: 14px;
        }
        .btn-primary {
          padding: 14px 20px;
          font-size: 15px;
          margin-top: 12px;
        }
        h1 {
          font-size: 1.3rem;
        }
        h2 {
          font-size: 1.1rem;
        }
        .responder-list {
          padding: 10px;
        }
        .responder-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 5px;
        }
        .my-actions {
          padding: 10px;
        }
        .action-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 5px;
        }
        .ai-summary {
          padding: 10px;
        }
        .btn-ai-summary {
          padding: 8px 12px;
          font-size: 12px;
        }
      }

      /* New Components Styles */
      .responder-list {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
      }
      .responder-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
      }
      .responder-item:last-child {
        border-bottom: none;
      }
      .my-actions {
        background: #fff3cd;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #ffc107;
      }
      .action-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 5px 0;
      }
      .ai-summary {
        background: linear-gradient(135deg, #e8f4f8, #d4f8d4);
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
        border-left: 4px solid #20c997;
      }
      .btn-ai-summary {
        background: linear-gradient(135deg, #ff6b35, #f7931e);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin: 10px 0;
      }
      .btn-ai-summary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
      }
      footer {
        background-color: #1a73e8;
        color: #fff;
        padding: 20px;
        text-align: center;
        margin-top: auto;
      }
      footer .footer-links {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 10px;
      }
      footer a {
        color: #fff;
        text-decoration: none;
        font-size: 16px;
        transition: color 0.2s;
      }
      footer a:hover {
        color: #e6f0fa;
      }
      footer .copyright {
        font-size: 14px;
        margin-top: 10px;
      }
      footer .logo {
        margin: 10px 0;
      }
      footer .logo img {
        height: 40px;
      }

      /* Documents Section Styles */
      .documents-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }
      .document-card {
        background: white;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
      }
      .document-card:hover {
        transform: translateY(-2px);
      }
      .document-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
      .document-icon {
        font-size: 24px;
        margin-right: 10px;
      }
      .document-info {
        flex: 1;
        min-width: 0;
      }
      .document-info h4 {
        margin: 0;
        font-size: 14px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
      }
      .document-info p {
        margin: 0;
        font-size: 12px;
        color: #666;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .document-download {
        display: inline-block;
        background: #667eea;
        color: white;
        padding: 6px 12px;
        border-radius: 4px;
        text-decoration: none;
        font-size: 12px;
        font-weight: 600;
      }
      .document-download:hover {
        background: #5a6fd8;
      }

      @media (max-width: 600px) {
        footer .footer-links {
          flex-direction: column;
          gap: 10px;
        }
        .documents-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
    <script
      async
      src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBigR0OaTpsLIIFuUJphgtMgWB7PMDDm7k&libraries=places"
    ></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.5.1/socket.io.js"></script>
    <!-- Define all functions before loading jwt-decode -->
    <script>
      let jwtDecodeLoaded = false;
      let userId = null;
      let role = null;
      let username = null;
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get("token");
      const eventId = urlParams.get("eventId");
      const socket = io(window.location.origin);
      let map = null;
      let mapReady = false;
      let responderMarker = null;
      let reportToMarker = null;
      let lastLat = null;
      let lastLng = null;
      let reportToLocation = null;
      let currentStatus = "pending";
      let eventType = "response"; // Default to response, will be updated when event data loads
      let eventData = null;

      function initializeUserId() {
        try {
          if (typeof jwtDecode === "function") {
            const decoded = jwtDecode(token);
            userId = decoded.id;
            role = decoded.role;
            username = decoded.username || decoded.first_name || `User ${decoded.id}`;
          } else {
            // Fallback: manually decode the token
            const payload = JSON.parse(atob(token.split(".")[1]));
            userId = payload.id;
            role = payload.role;
            username = payload.username || payload.first_name || `User ${payload.id}`;
          }
          console.log(
            "User ID from token:",
            userId,
            "Username:",
            username,
            "Event ID:",
            eventId,
            "Role:",
            role
          );
        } catch (e) {
          console.error("Error decoding token:", e);
          alert("Invalid token. Please log in again.");
        }
      }

      function setupSocket() {
        if (!userId) {
          console.error("User ID not initialized");
          return;
        }
        socket.on("connect", () => {
          console.log("Connected to server");
          socket.emit("join", `event-${eventId}`);
          socket.emit("join", `user-${userId}`);
          loadChatMessages(); // Load existing chat messages
        });
      }

      function loadChatMessages() {
        fetch(`${window.location.origin}/events/${eventId}/chat`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })
        .then(response => response.json())
        .then(data => {
          if (data.success && data.messages) {
            const chatContainer = document.getElementById("chat-messages");
            chatContainer.innerHTML = ''; // Clear existing messages
            data.messages.forEach(message => {
              displayChatMessage(message);
            });
          }
        })
        .catch(error => {
          console.error('Error loading chat messages:', error);
        });
      }

      function displayChatMessage(message) {
        // Check for duplicates
        const existingMessages = document.querySelectorAll('#chat-messages li');
        for (let existing of existingMessages) {
          if (existing.dataset.messageId === String(message.id) ||
              (existing.dataset.timestamp === message.timestamp &&
               existing.dataset.text === message.text)) {
            return; // Don't add duplicate
          }
        }

        const li = document.createElement("li");
        const username = message.username || `User ${message.user_id || message.userId}` || 'Unknown';
        const time = new Date(message.timestamp).toLocaleTimeString();

        // Store message data for duplicate checking
        li.dataset.messageId = message.id || '';
        li.dataset.timestamp = message.timestamp || '';
        li.dataset.text = message.text || '';

        li.innerHTML = `
          <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div>
              <strong style="color: #667eea; font-size: 12px;">${username}</strong>
              <div style="margin-top: 2px;">${message.text}</div>
            </div>
            <span style="font-size: 11px; color: #888; margin-left: 10px;">${time}</span>
          </div>
        `;
        document.getElementById("chat-messages").appendChild(li);
        document.getElementById("chat-messages").scrollTop =
          document.getElementById("chat-messages").scrollHeight;
      }

      function onJwtDecodeLoaded() {
        jwtDecodeLoaded = true;
        console.log("jwt-decode loaded successfully");
        initializeUserId();
        setupSocket();
        fetchEventData();
        startGeolocation();
        checkLocationStatus();
        renderFooterLinks();
      }

      function onJwtDecodeError() {
        console.error("Failed to load jwt-decode library, using fallback");
        // Fallback: manually decode the token
        initializeUserId();
        setupSocket();
        fetchEventData();
        startGeolocation();
        checkLocationStatus();
        renderFooterLinks();
      }

      function checkLocationStatus() {
        if (navigator.geolocation) {
          navigator.geolocation.getCurrentPosition(
            () => {
              document.getElementById("location-status").textContent =
                "Enabled";
              document.getElementById("location-status").className =
                "status enabled";
            },
            () => {
              document.getElementById("location-status").textContent =
                "Disabled";
              document.getElementById("location-status").className =
                "status disabled";
            }
          );
        } else {
          document.getElementById("location-status").textContent =
            "Not Supported";
          document.getElementById("location-status").className =
            "status disabled";
        }
      }

      function initMap(lat = 43.8349, lng = -91.3188) {
        if (!window.google || !window.google.maps) {
          console.error("Google Maps API not loaded");
          document.getElementById("map-error").style.display = "block";
          return;
        }
        map = new google.maps.Map(document.getElementById("map"), {
          center: { lat, lng },
          zoom: 10,
        });
        mapReady = true;
        console.log("Map initialized at:", lat, lng);

        // Add any pending markers
        if (lastLat !== null && lastLng !== null) {
          addResponderMarker(lastLat, lastLng);
        }
        if (reportToLocation) {
          geocodeReportToLocation(reportToLocation);
        }
      }

      function addResponderMarker(lat, lng) {
        if (!mapReady || !map) {
          console.log("Map not ready yet, storing coordinates:", lat, lng);
          lastLat = lat;
          lastLng = lng;
          return;
        }
        if (responderMarker) responderMarker.setMap(null);
        responderMarker = new google.maps.Marker({
          position: { lat, lng },
          map: map,
          title: "Your Location",
          icon: "http://maps.google.com/mapfiles/ms/icons/blue-dot.png",
        });
        map.panTo({ lat, lng });
      }

      function geocodeReportToLocation(location) {
        if (!mapReady || !map) {
          console.log(
            "Map not ready yet, storing report-to location:",
            location
          );
          reportToLocation = location;
          return;
        }
        const geocoder = new google.maps.Geocoder();
        geocoder.geocode({ address: location }, (results, status) => {
          if (status === "OK") {
            const { lat, lng } = results[0].geometry.location;
            console.log("Report to location geocoded:", {
              lat: lat(),
              lng: lng(),
            });
            if (reportToMarker) reportToMarker.setMap(null);
            reportToMarker = new google.maps.Marker({
              position: { lat: lat(), lng: lng() },
              map: map,
              title: "Report To Location",
              icon: "http://maps.google.com/mapfiles/ms/icons/red-dot.png",
            });
            map.panTo({ lat: lat(), lng: lng() });
          } else {
            console.error("Geocoding failed for report to location:", status);
            alert("Failed to geocode report-to location: " + status);
          }
        });
      }

      function updateStatus(status) {
        if (!userId) {
          console.error("User ID not initialized");
          alert("User ID not available. Please refresh the page.");
          return;
        }
        if (
          status === "unable" &&
          !confirm(
            "Are you sure you can’t respond? This will end updates for this event."
          )
        ) {
          return;
        }
        fetch(`${window.location.origin}/tasks`, {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            event_id: eventId,
            assigned_to: userId,
            status,
          }),
        })
          .then((res) => {
            if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
            return res.json();
          })
          .then((task) => {
            console.log("Task updated:", task);
            if (task.status === "unable") {
              socket.emit("leave", `event-${eventId}`);
              document.body.innerHTML =
                '<div class="container"><h1>You’ve opted out—contact dispatch if needed.</h1></div>';
            } else {
              currentStatus = task.status;
              updateTaskUI(task);
            }
          })
          .catch((err) => {
            console.error("Error updating task:", err);
            alert("Failed to update status: " + err.message);
          });
      }

      function updateTaskUI(task) {
        const reportTo = task.report_to_location || "Not assigned";
        
        if (eventType === "notification_only") {
          // NOE mode - simplified display
          document.getElementById("task-info").innerHTML = `
            <p><strong>Task ID:</strong> ${task.id}</p>
            <p><strong>Status:</strong> ${getNoeModeStatusLabel(task.status)}</p>
            ${eventData ? `<div class="event-date-time">
              <strong>📅 Event Date & Time:</strong><br>
              ${new Date(eventData.created_at || Date.now()).toLocaleString()}
            </div>` : ''}
          `;
        } else {
          // Regular response mode
          document.getElementById("task-info").innerHTML = `
            <p><strong>Task ID:</strong> ${task.id}</p>
            <p><strong>Status:</strong> ${task.status}</p>
            <p class="highlight"><strong>Report to:</strong> ${reportTo}</p>
          `;
        }
        
        updateButtons(task.status);
        
        if (eventType === "response" && task.report_to_location && task.report_to_location !== reportToLocation) {
          geocodeReportToLocation(task.report_to_location);
          playAlertSound();
        }
        reportToLocation = task.report_to_location;
      }

      function getNoeModeStatusLabel(status) {
        const noeLabels = {
          'acknowledged': 'Participating',
          'enroute': 'Unable to Participate', 
          'delayed': 'Removed from Event',
          'unable': 'No Response',
          'pending': 'Pending Response'
        };
        return noeLabels[status] || status;
      }

      function updateButtons(activeStatus) {
        const buttons = document.querySelectorAll(".status-buttons button");
        buttons.forEach((button) => {
          button.classList.remove("active");
          if (button.textContent.toLowerCase() === activeStatus) {
            button.classList.add("active");
          }
        });
      }

      function openDirections() {
        if (navigator.geolocation && lastLat && lastLng) {
          const taskInfo = document.getElementById("task-info").innerText;
          const locationMatch = taskInfo.match(/Report to: (.+)/);
          if (locationMatch && locationMatch[1] !== "Not assigned") {
            const destination = encodeURIComponent(locationMatch[1]);
            const origin = `${lastLat},${lastLng}`;
            // Use location.href for better iOS compatibility
            window.location.href = `https://www.google.com/maps/dir/?api=1&origin=${origin}&destination=${destination}`;
          } else {
            alert("No valid report-to location available");
          }
        } else {
          alert("Location data unavailable—please enable geolocation");
        }
      }

      function fetchEventData() {
        if (!userId) {
          console.error("User ID not initialized");
          return;
        }
        fetch(`${window.location.origin}/active-events/${eventId}`, {
          headers: { Authorization: `Bearer ${token}` },
        })
          .then((res) => {
            if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
            return res.json();
          })
          .then((data) => {
            eventData = data;
            eventType = data.event_type || "response";
            
            const noeBadge = eventType === "notification_only" ? 
              '<span class="noe-badge">Notification Only Event</span>' : '';
            
            document.getElementById("event-info").innerHTML = `
              <h2>${data.title}</h2>
              ${noeBadge}
              <p>${data.info}</p>
              <p><strong>📍 Location:</strong> ${formatLocation(data.location)}</p>
              ${eventType === "notification_only" ? 
                `<p><strong>🔗 <a href="#" onclick="showEventMap()">Map of Event Location</a></strong></p>` : 
                ''
              }
            `;
            
            // Update UI based on event type
            updateUIForEventType();
            
            const tasks = data.tasks || [];
            const task = tasks.find((t) => t.assigned_to === userId);
            if (task) {
              currentStatus = task.status || "pending";
              updateTaskUI(task);
            } else {
              document.getElementById("task-info").innerHTML =
                "<p>No task assigned yet.</p>";
            }
            
            // Load additional components for NOE
            if (eventType === "notification_only") {
              loadResponderList();
              loadMyActions();
            }

            // Load event documents for all event types
            loadEventDocuments();
          })
          .catch((err) => {
            console.error("Error fetching event:", err);
            document.getElementById("event-info").innerHTML =
              "<p>Failed to load event data: " + err.message + "</p>";
          });
      }

      function startGeolocation() {
        if (!userId) {
          console.error("User ID not initialized");
          return;
        }
        if (navigator.geolocation) {
          navigator.geolocation.watchPosition(
            (position) => {
              const { latitude, longitude } = position.coords;
              lastLat = latitude;
              lastLng = longitude;
              fetch(`${window.location.origin}/responder/location`, {
                method: "POST",
                headers: {
                  Authorization: `Bearer ${token}`,
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ latitude, longitude }),
              })
                .then((res) => {
                  if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
                  return res.json();
                })
                .then((data) => {
                  document.getElementById("location-info").innerHTML = `
                            <p>Location: ${data.latitude}, ${data.longitude}</p>
                        `;
                  addResponderMarker(data.latitude, data.longitude);
                })
                .catch((err) => {
                  console.error("Error updating location:", err);
                  document.getElementById("location-info").innerHTML =
                    "<p>Failed to update location: " + err.message + "</p>";
                });
            },
            (err) => {
              console.error("Geolocation error:", err);
              document.getElementById("location-info").innerHTML =
                "<p>Unable to fetch location: " + err.message + "</p>";
            },
            { enableHighAccuracy: true }
          );
        } else {
          console.error("Geolocation is not supported by this browser.");
          document.getElementById("location-info").innerHTML =
            "<p>Geolocation not supported</p>";
        }
      }

      function formatLocation(location) {
        if (!location || typeof location !== "object") {
          return "Unknown";
        }
        const { commonName, address, city, state, zip } = location;
        return commonName
          ? `${commonName}, ${address || ""}, ${city || ""}, ${state || ""} ${
              zip || ""
            }`
              .trim()
              .replace(/,\s*,/g, ",")
              .replace(/,\s*$/, "")
          : `${address || ""}, ${city || ""}, ${state || ""} ${zip || ""}`
              .trim()
              .replace(/,\s*,/g, ",")
              .replace(/,\s*$/, "") || "Unknown";
      }

      function playAlertSound() {
        const alertSound = document.getElementById("alertSound");
        alertSound
          .play()
          .catch((err) => console.error("Error playing sound:", err));
      }

      function updateUIForEventType() {
        const statusButtonsContainer = document.getElementById("status-buttons");
        const routeButton = document.querySelector('.btn-primary');
        const mapContainer = document.getElementById("map");
        const locationTracking = document.getElementById("location-info");
        
        if (eventType === "notification_only") {
          // Update buttons for NOE mode
          statusButtonsContainer.innerHTML = `
            <button class="btn-participate" onclick="updateStatus('acknowledged')">
              ✅ Acknowledge and Participate
            </button>
            <button class="btn-unable" onclick="updateStatus('enroute')">
              ⚠️ Acknowledge, but unable to join
            </button>
            <button class="btn-remove" onclick="updateStatus('unable')">
              ❌ Remove me from event
            </button>
          `;
          
          // Hide route button and location tracking
          if (routeButton) routeButton.style.display = 'none';
          if (mapContainer) mapContainer.style.display = 'none';
          if (locationTracking) locationTracking.style.display = 'none';
          
        } else {
          // Regular response mode buttons
          statusButtonsContainer.innerHTML = `
            <button class="btn-success" onclick="updateStatus('enroute')">
              🚗 Enroute – Moving Toward Location
            </button>
            <button class="btn-warning" onclick="updateStatus('delayed')">
              ⏰ Delayed
            </button>
            <button class="btn-danger" onclick="updateStatus('unable')">
              ❌ Unable to Respond
            </button>
          `;
        }
      }

      function showEventMap() {
        if (eventData && eventData.location) {
          const location = formatLocation(eventData.location);
          const encodedLocation = encodeURIComponent(location);
          // Use location.href for better iOS compatibility
          window.location.href = `https://www.google.com/maps/search/?api=1&query=${encodedLocation}`;
        }
      }

      function loadResponderList() {
        fetch(`${window.location.origin}/events/${eventId}/responders`, {
          headers: { Authorization: `Bearer ${token}` },
        })
        .then(res => res.ok ? res.json() : Promise.reject('Failed to load responders'))
        .then(responders => {
          const responderListHTML = `
            <div class="card">
              <h2>👥 Participating Responders</h2>
              <div class="responder-list">
                ${responders.map(r => `
                  <div class="responder-item">
                    <span><strong>${r.first_name} ${r.last_name}</strong> (${r.job_role || 'Responder'})</span>
                    <span class="badge badge-${r.status === 'acknowledged' ? 'success' : 'warning'}">${getNoeModeStatusLabel(r.status)}</span>
                  </div>
                `).join('')}
              </div>
            </div>
          `;
          document.getElementById('responder-list-container').innerHTML = responderListHTML;
        })
        .catch(err => console.error('Error loading responders:', err));
      }

      function loadMyActions() {
        // First try to load existing actions from database
        fetch(`${window.location.origin}/events/${eventId}/actions`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })
        .then(res => {
          if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
          return res.json();
        })
        .then(existingActions => {
          if (existingActions.length > 0) {
            // Load existing actions and their completion status
            const actions = existingActions.map(item => item.action_text);
            const completedSet = new Set();
            existingActions.forEach((item, index) => {
              if (item.is_completed) {
                completedSet.add(index);
              }
            });
            displayMyActions(actions, completedSet);
            return;
          }

          // If no existing actions, generate new ones
          generateNewActions();
        })
        .catch(err => {
          console.error('Error loading existing actions:', err);
          generateNewActions();
        });
      }

      function generateNewActions() {
        fetch(`${window.location.origin}/ai/generate-actions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            eventInfo: eventData,
            userRole: userRole,
            documents: eventData.event_documents || []
          })
        })
        .then(res => {
          if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
          return res.json();
        })
        .then(data => {
          const actions = data.actions || getDefaultActions();
          displayMyActions(actions);
          saveActionsToDatabase(actions);
        })
        .catch(err => {
          console.log('AI not available, using default actions');
          const actions = getDefaultActions();
          displayMyActions(actions);
          saveActionsToDatabase(actions);
        });
      }

      function saveActionsToDatabase(actions) {
        fetch(`${window.location.origin}/events/${eventId}/actions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            actions: actions,
            userRole: userRole
          })
        })
        .catch(err => {
          console.error('Error saving actions to database:', err);
        });
      }

      function displayMyActions(actions, completedSet = new Set()) {
        const myActionsHTML = `
          <div class="card">
            <h2>📋 My Actions</h2>
            <div class="my-actions">
              ${actions.map((action, index) => `
                <div class="action-item">
                  <input type="checkbox" id="action${index}" ${completedSet.has(index) ? 'checked' : ''}
                         onchange="toggleActionCompletion(${index})">
                  <label for="action${index}">${action}</label>
                </div>
              `).join('')}
            </div>
          </div>
        `;
        document.getElementById('my-actions-container').innerHTML = myActionsHTML;
      }

      function toggleActionCompletion(index) {
        const checkbox = document.getElementById(`action${index}`);
        const isCompleted = checkbox.checked;

        fetch(`${window.location.origin}/events/${eventId}/actions/${index}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            isCompleted: isCompleted
          })
        })
        .catch(err => {
          console.error('Error updating action completion:', err);
          // Revert checkbox state on error
          checkbox.checked = !isCompleted;
        });
      }

      function getDefaultActions() {
        return [
          "Review event details and location information",
          "Acknowledge receipt of notification",
          "Stay available for updates",
          "Monitor communication channels",
          "Review event information thoroughly"
        ];
      }

      function loadEventDocuments() {
        fetch(`${window.location.origin}/events/${eventId}/documents`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })
        .then(res => {
          if (!res.ok) throw new Error(`HTTP error: ${res.status}`);
          return res.json();
        })
        .then(documents => {
          console.log('Loaded documents:', documents);
          displayEventDocuments(documents);
        })
        .catch(err => {
          console.error('Error loading documents:', err);
        });
      }

      function displayEventDocuments(documents) {
        if (!documents || documents.length === 0) {
          document.getElementById('documents-container').innerHTML = '';
          return;
        }

        const documentsHTML = `
          <div class="card">
            <h2>📄 Event Documents</h2>
            <p style="color: #666; font-size: 14px; margin-bottom: 15px;">
              Important documents and resources for this event
            </p>
            <div class="documents-grid">
              ${documents.map(doc => `
                <div class="document-card">
                  <div class="document-header">
                    <span class="document-icon">
                      ${doc.filename.endsWith('.pdf') ? '📄' :
                        doc.filename.match(/\\.(doc|docx)$/i) ? '📝' : '🖼️'}
                    </span>
                    <div class="document-info">
                      <h4 title="${doc.originalName || doc.filename}">${doc.originalName || doc.filename}</h4>
                      <p>${new Date(doc.uploadedAt).toLocaleDateString()} • ${(doc.size / 1024).toFixed(1)}KB</p>
                    </div>
                  </div>
                  <a
                    href="${window.location.origin}/events/${eventId}/documents/${doc.filename}"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="document-download"
                  >
                    📖 View Document
                  </a>
                </div>
              `).join('')}
            </div>
          </div>
        `;
        document.getElementById('documents-container').innerHTML = documentsHTML;
      }

      function summarizeChat() {
        const chatMessages = Array.from(document.querySelectorAll('#chat-messages li')).map(li => li.textContent);
        
        if (chatMessages.length === 0) {
          alert('No chat messages to summarize');
          return;
        }

        // Try AI API first, fallback to basic summary
        fetch(`${window.location.origin}/ai/summarize-chat`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            eventId: eventId,
            chatText: chatMessages.join('\n')
          })
        })
        .then(res => res.ok ? res.json() : Promise.reject('AI API unavailable'))
        .then(data => {
          document.getElementById('chat-summary').innerHTML = `
            <div class="ai-summary">
              <h3>🤖 AI Chat Summary</h3>
              <p>${data.summary}</p>
            </div>
          `;
        })
        .catch(err => {
          // Fallback summary
          const summary = `Chat Summary (${chatMessages.length} messages):\n\nRecent Activity:\n${chatMessages.slice(-3).join('\n')}\n\nNote: AI summarization requires backend setup.`;
          document.getElementById('chat-summary').innerHTML = `
            <div class="ai-summary">
              <h3>📝 Basic Chat Summary</h3>
              <pre>${summary}</pre>
            </div>
          `;
        });
      }

      function renderFooterLinks() {
        const footerLinks = document.getElementById("footer-links");
        let linksHTML = `
                <a href="/">Home</a>
            `;
        if (role === "commander" || role === "lead" || role === "staff") {
          linksHTML += `
                    <a href="/events">Events</a>
                    <a href="/dashboard">Dashboard</a>
                `;
        }
        if (role === "commander") {
          linksHTML += `
                    <a href="/launch-event">Launch Event</a>
                    <a href="/templates">Templates</a>
                    <a href="/user-management">User Management</a>
                    <a href="/reporting">Reporting</a>
                `;
        }
        footerLinks.innerHTML = linksHTML;
      }
    </script>
    <script
      src="https://cdn.jsdelivr.net/npm/jwt-decode@4.0.0/build/jwt-decode.min.js"
      onload="onJwtDecodeLoaded()"
      onerror="onJwtDecodeError()"
    ></script>
  </head>
  <body>
    <div class="container">
      <div class="card">
        <h1>🚨 Responder Dashboard</h1>
        <div id="event-info"></div>
        <div id="task-info"></div>
        <div id="location-info"></div>
        <p>
          <strong>📍 Location Services:</strong>
          <span id="location-status" class="status"></span>
        </p>
        <div id="status-buttons" class="status-buttons">
          <!-- Buttons will be dynamically updated based on event type -->
          <button class="btn-success" onclick="updateStatus('enroute')">
            🚗 Enroute – Moving Toward Location
          </button>
          <button class="btn-warning" onclick="updateStatus('delayed')">
            ⏰ Delayed
          </button>
          <button class="btn-danger" onclick="updateStatus('unable')">
            ❌ Unable to Respond
          </button>
        </div>
        <div class="route-button-container">
          <button class="btn-primary" onclick="openDirections()">
            <span>🗺️</span>
            <span>Route Me</span>
          </button>
        </div>
        <div id="map"></div>
        <div id="map-error" style="color: var(--danger); display: none">
          Map unavailable—Google Maps API failed
        </div>
      </div>

      <!-- Responder List Container (for NOE) -->
      <div id="responder-list-container"></div>

      <!-- My Actions Container (for NOE) -->
      <div id="my-actions-container"></div>

      <!-- Event Documents Section -->
      <div id="documents-container"></div>

      <!-- Enhanced Chat Section -->
      <div id="chat">
        <div class="chat-header">
          <h2>💬 Event Chat</h2>
          <button class="btn-ai-summary" onclick="summarizeChat()">
            🤖 Summarize with AI
          </button>
        </div>
        <div id="chat-summary"></div>
        <ul id="chat-messages"></ul>
        <form id="chat-form">
          <input type="text" id="chat-input" placeholder="Type your message here..." />
          <button type="submit" class="btn-primary">📤 Send</button>
        </form>
      </div>
    </div>

    <audio
      id="alertSound"
      src="https://www.soundjay.com/buttons/beep-01a.mp3"
      preload="auto"
    ></audio>

    <footer>
      <div class="footer-links" id="footer-links"></div>
      <div class="logo">
        <img src="/frontend/public/emsmg.png" alt="EMS Management Group Logo" />
      </div>
      <div class="copyright">
        © 2025 EMS Management Group, LLC. All rights reserved.
      </div>
    </footer>

    <script>
      // Initialize map on page load
      window.addEventListener("load", () => initMap());

      socket.on("task-assigned", ({ task }) => {
        if (task.assigned_to === userId) {
          updateTaskUI(task);
        }
      });

      socket.on("chat", (message) => {
        displayChatMessage(message);
        playAlertSound();
      });

      socket.on("chat-message", (message) => {
        displayChatMessage(message);
        playAlertSound();
      });

      socket.on("documents-updated", (data) => {
        if (data.eventId == eventId) {
          console.log("Documents updated, reloading...");
          loadEventDocuments();
        }
      });

      document.getElementById("chat-form").onsubmit = async (e) => {
        e.preventDefault();
        const input = document.getElementById("chat-input");
        if (input.value.trim()) {
          try {
            // Send message via API (same as dashboard)
            const response = await fetch(`${window.location.origin}/events/${eventId}/chat`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`,
              },
              body: JSON.stringify({
                text: input.value.trim(),
              }),
            });

            if (response.ok) {
              const data = await response.json();
              if (data.success) {
                // Message will be received via Socket.IO broadcast, so just clear input
                input.value = "";
                console.log("Message sent successfully!");
              } else {
                throw new Error(data.error || 'Failed to send message');
              }
            } else {
              throw new Error('Failed to send message');
            }
          } catch (error) {
            console.error('Error sending message:', error);
            alert('Failed to send message: ' + error.message);
          }
        }
      };
    </script>
  </body>
</html>
