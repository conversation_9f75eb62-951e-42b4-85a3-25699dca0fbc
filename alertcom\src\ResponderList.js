import React, { useState, useEffect } from "react";
import io from "socket.io-client";
import { Droppable, Draggable } from "react-beautiful-dnd";
import config from './config'; 
const { baseUrl } = config;

const socket = io(`${baseUrl}`, {
  transports: ["websocket"],
  reconnection: true,
  reconnectionAttempts: 5,
});

function ResponderList({ token }) {
  const [responders, setResponders] = useState([]);
  const statusGroups = [
    "available",
    "pending",
    "acknowledged",
    "enroute",
    "delayed",
    "busy",
  ];

  useEffect(() => {
    console.log(
      "Fetching responders with token:",
      token.substring(0, 10) + "..."
    );
    fetch(`${baseUrl}/responders`, {
      headers: { Authorization: `Bearer ${token}` },
    })
      .then((response) => {
        if (!response.ok)
          throw new Error(
            `Fetch failed: ${response.status} - ${response.statusText}`
          );
        return response.json();
      })
      .then((data) => {
        console.log("Responders fetched:", data);
        setResponders(data);
      })
      .catch((error) =>
        console.error("Fetch responders error:", error.message)
      );

    socket.on("responder-update", (responder) => {
      console.log("Responder update received:", responder);
      setResponders((prev) => {
        const updated = prev.filter((r) => r.id !== responder.id);
        return [...updated, responder];
      });
    });

    socket.on("task-response", (task) => {
      console.log("Task response received:", task);
      setResponders((prev) => {
        const updated = prev.map((r) =>
          r.id === task.assigned_to ? { ...r, status: task.status } : r
        );
        console.log("Updated responders with task status:", updated);
        return updated;
      });
    });

    return () => {
      console.log("ResponderList unmounting");
      socket.off("responder-update");
      socket.off("task-response");
    };
  }, [token]);

  return (
    <div id="responders" style={{ marginBottom: "20px" }}>
      <table style={{ width: "100%", borderCollapse: "collapse" }}>
        <thead>
          <tr>
            {statusGroups.map((status) => {
              const count = responders.filter(
                (r) => r.status === status
              ).length;
              const displayStatus =
                status === "pending" ? "Alerted-Pending" : status;
              return (
                <th
                  key={status}
                  style={{
                    padding: "5px",
                    backgroundColor: "#ddd",
                    textAlign: "center",
                    textTransform: "capitalize",
                  }}
                >
                  {displayStatus} ({count})
                </th>
              );
            })}
          </tr>
        </thead>
        <tbody>
          <tr>
            {statusGroups.map((status) => {
              const group = responders
                .filter((r) => r.status === status)
                .sort((a, b) => {
                  // Handle undefined usernames
                  const nameA = a.username || `Responder_${a.id}`;
                  const nameB = b.username || `Responder_${b.id}`;
                  return nameA.localeCompare(nameB);
                });
              return (
                <td
                  key={status}
                  style={{ verticalAlign: "top", padding: "5px" }}
                >
                  <Droppable droppableId={`responders-${status}`}>
                    {(provided) => (
                      <div ref={provided.innerRef} {...provided.droppableProps}>
                        {group.length === 0 ? (
                          <p>
                            No{" "}
                            {status === "pending" ? "alerted-pending" : status}{" "}
                            responders
                          </p>
                        ) : (
                          group.map((responder, index) => (
                            <Draggable
                              key={responder.id}
                              draggableId={String(responder.id)}
                              index={index}
                            >
                              {(provided) => (
                                <div
                                  ref={provided.innerRef}
                                  {...provided.draggableProps}
                                  {...provided.dragHandleProps}
                                  style={{
                                    padding: "5px",
                                    margin: "5px 0",
                                    border: "1px solid #ccc",
                                    backgroundColor: "#f0f0f0",
                                    ...provided.draggableProps.style,
                                  }}
                                >
                                  {responder.username ||
                                    `Responder_${responder.id}`}
                                </div>
                              )}
                            </Draggable>
                          ))
                        )}
                        {provided.placeholder}
                      </div>
                    )}
                  </Droppable>
                </td>
              );
            })}
          </tr>
        </tbody>
      </table>
    </div>
  );
}

export default ResponderList;
