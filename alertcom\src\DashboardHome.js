// src/DashboardHome.js
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import "./styles.css";

import config from './config'; 
const { baseUrl } = config;

function DashboardHome({ token, mapsLoaded }) {
  const [events, setEvents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    
    const fetchEvents = async () => {
      try {
        const response = await fetch(
          `${baseUrl}/all-active-events`,
          {
            headers: { Authorization: `Bearer ${token}` },
          }
        );
        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(
            `Failed to fetch events: ${response.status} - ${errorText}`
          );
        }
        const data = await response.json();
        
        const sortedEvents = Array.isArray(data)
          ? data.sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
          : [];
        setEvents(sortedEvents);
        setLoading(false);
      } catch (err) {
        // console.error("Error fetching events:", err);
        setError(err.message);
        setLoading(false);
      }
    };

    if (token) fetchEvents();
  }, [token]);

  if (loading)
    return <div style={{ padding: "20px" }}>Loading dashboard...</div>;
  if (error)
    return <div style={{ padding: "20px", color: "red" }}>Error: {error}</div>;
  if (!events.length)
    return <div style={{ padding: "20px" }}>No active events found.</div>;

  const latestEvent = events[0];
  const recentEvents = events.slice(0, 5);
  const totalRespondersAssigned = events.reduce(
    (sum, event) => sum + (event.tasks ? event.tasks.length : 0),
    0
  );

  return (
    <div
      className="container"
      style={{ padding: "20px", fontFamily: "Arial, sans-serif" }}
    >
      <h2 style={{ color: "#1a73e8", marginBottom: "20px" }}>Dashboard Home</h2>

      {/* Summary Stats */}
      <section
        style={{
          marginBottom: "40px",
          display: "flex",
          gap: "20px",
          flexWrap: "wrap",
        }}
      >
        <div
          style={{
            padding: "15px",
            background: "#f5f5f5",
            borderRadius: "8px",
            flex: "1",
            minWidth: "200px",
          }}
        >
          <h4 style={{ margin: "0 0 10px 0", color: "#333" }}>
            Total Active Events
          </h4>
          <p style={{ fontSize: "24px", margin: "0", color: "#1a73e8" }}>
            {events.length}
          </p>
        </div>
        <div
          style={{
            padding: "15px",
            background: "#f5f5f5",
            borderRadius: "8px",
            flex: "1",
            minWidth: "200px",
          }}
        >
          <h4 style={{ margin: "0 0 10px 0", color: "#333" }}>
            Responders Assigned
          </h4>
          <p style={{ fontSize: "24px", margin: "0", color: "#1a73e8" }}>
            {totalRespondersAssigned}
          </p>
        </div>
        <div
          style={{
            padding: "15px",
            background: "#f5f5f5",
            borderRadius: "8px",
            flex: "1",
            minWidth: "200px",
          }}
        >
          <h4 style={{ margin: "0 0 10px 0", color: "#333" }}>
            Avg. Response Time
          </h4>
          <p style={{ fontSize: "24px", margin: "0", color: "#1a73e8" }}>TBD</p>
        </div>
      </section>

      {/* Latest Event Card */}
      <section style={{ marginBottom: "40px" }}>
        <h3 style={{ color: "#1a73e8", marginBottom: "15px" }}>Latest Event</h3>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fill, minmax(250px, 1fr))",
            gap: "20px",
          }}
        >
          <div
            style={{
              padding: "15px",
              border: "1px solid #ddd",
              borderRadius: "8px",
              background: "#fff",
              boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              transition: "transform 0.2s",
              cursor: "pointer",
            }}
            onMouseEnter={(e) =>
              (e.currentTarget.style.transform = "scale(1.02)")
            }
            onMouseLeave={(e) => (e.currentTarget.style.transform = "scale(1)")}
          >
            <h4 style={{ margin: "0 0 10px 0", color: "#333" }}>
              {latestEvent.title}
            </h4>
            <p
              style={{ margin: "0 0 10px 0", color: "#666", fontSize: "14px" }}
            >
              Event ID: {latestEvent.id}
            </p>
            <p
              style={{ margin: "0 0 10px 0", color: "#666", fontSize: "14px" }}
            >
              Created: {new Date(latestEvent.created_at).toLocaleString()}
            </p>
            <p
              style={{ margin: "0 0 10px 0", color: "#666", fontSize: "14px" }}
            >
              {latestEvent.description || "No description available."}
            </p>
            <Link
              to={`/dashboard/${latestEvent.id}`}
              style={{
                display: "inline-block",
                padding: "8px 16px",
                background: "#1a73e8",
                color: "#fff",
                textDecoration: "none",
                borderRadius: "4px",
                textAlign: "center",
              }}
            >
              View Dashboard
            </Link>
          </div>
        </div>
      </section>

      {/* Recent Events Cards */}
      <section>
        <h3 style={{ color: "#1a73e8", marginBottom: "15px" }}>
          Recent Events
        </h3>
        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fill, minmax(250px, 1fr))",
            gap: "20px",
          }}
        >
          {recentEvents.map((event) => (
            <div
              key={event.id}
              style={{
                padding: "15px",
                border: "1px solid #ddd",
                borderRadius: "8px",
                background: "#fff",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
                transition: "transform 0.2s",
                cursor: "pointer",
              }}
              onMouseEnter={(e) =>
                (e.currentTarget.style.transform = "scale(1.02)")
              }
              onMouseLeave={(e) =>
                (e.currentTarget.style.transform = "scale(1)")
              }
            >
              <h4 style={{ margin: "0 0 10px 0", color: "#333" }}>
                {event.title}
              </h4>
              <p
                style={{
                  margin: "0 0 10px 0",
                  color: "#666",
                  fontSize: "14px",
                }}
              >
                Event ID: {event.id}
              </p>
              <p
                style={{
                  margin: "0 0 10px 0",
                  color: "#666",
                  fontSize: "14px",
                }}
              >
                Created: {new Date(event.created_at).toLocaleString()}
              </p>
              <p
                style={{
                  margin: "0 0 10px 0",
                  color: "#666",
                  fontSize: "14px",
                }}
              >
                {event.description || "No description available."}
              </p>
              <Link
                to={`/dashboard/${event.id}`}
                style={{
                  display: "inline-block",
                  padding: "8px 16px",
                  background: "#1a73e8",
                  color: "#fff",
                  textDecoration: "none",
                  borderRadius: "4px",
                  textAlign: "center",
                }}
              >
                View Dashboard
              </Link>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}

export default DashboardHome;
