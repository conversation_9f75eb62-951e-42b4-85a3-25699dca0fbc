-- Complete test script to verify Settings AI configuration integrates with Event Documents & AI Summary
-- This tests the end-to-end integration

BEGIN;

-- 1. Set up test AI prompts in settings
UPDATE company_settings 
SET ai_prompts = '{
    "document_summary": "TEST PROMPT: Create a detailed emergency response summary with numbered action items and clear bullet points for each procedure."
}',
ai_preconditions = '{
    "document_qa_prompt": "TEST PROMPT: Answer emergency questions with specific details and always mention this is a test response.",
    "action_generation_prompt": "TEST PROMPT: Generate emergency actions with test prefix for verification."
}'
WHERE company_id = 1;

-- Verify the test prompts were saved
SELECT 
    'Test prompts saved:' AS status,
    ai_prompts->'document_summary' AS summary_prompt,
    ai_preconditions->'document_qa_prompt' AS qa_prompt
FROM company_settings 
WHERE company_id = 1;

-- 2. Test document summary with custom prompt
-- Insert a test document summary to simulate the API call
INSERT INTO document_summaries (event_id, user_id, document_name, summary, checklist_items)
VALUES (
    1, 1,
    'test-integration-doc.pdf',
    'TEST PROMPT: Create a detailed emergency response summary with numbered action items:
• Action 1: Secure the area and ensure safety
• Action 2: Contact emergency services immediately  
• Action 3: Evacuate personnel if necessary
• Action 4: Report status to command center',
    '["Secure the area", "Contact emergency services", "Evacuate if needed", "Report status"]'
) ON CONFLICT (event_id, user_id, document_name) DO UPDATE SET
    summary = EXCLUDED.summary,
    checklist_items = EXCLUDED.checklist_items,
    updated_at = CURRENT_TIMESTAMP;

-- 3. Test document Q&A with custom prompt
INSERT INTO document_questions (event_id, user_id, question, answer, document_context)
VALUES (
    1, 1,
    'What are the emergency procedures?',
    'TEST PROMPT: Answer emergency questions with specific details - The emergency procedures include: 1) Secure area, 2) Contact services, 3) Evacuate if needed. This is a test response.',
    '{"documents": ["test-integration-doc.pdf"]}'
);

-- 4. Verify integration data
SELECT 'Integration test results:' AS test_section;

-- Check if document summary uses custom prompt format
SELECT 
    CASE 
        WHEN summary LIKE '%TEST PROMPT%' THEN 'PASS: Document summary using custom prompt'
        ELSE 'FAIL: Document summary not using custom prompt'
    END AS summary_integration_test,
    document_name,
    LEFT(summary, 100) || '...' AS summary_preview
FROM document_summaries 
WHERE document_name = 'test-integration-doc.pdf';

-- Check if Q&A uses custom prompt format
SELECT 
    CASE 
        WHEN answer LIKE '%TEST PROMPT%' THEN 'PASS: Q&A using custom prompt'
        ELSE 'FAIL: Q&A not using custom prompt'
    END AS qa_integration_test,
    question,
    LEFT(answer, 100) || '...' AS answer_preview
FROM document_questions 
WHERE question = 'What are the emergency procedures?';

-- Check if checklist items are properly stored
SELECT 
    CASE 
        WHEN checklist_items IS NOT NULL AND JSON_ARRAY_LENGTH(checklist_items) > 0 THEN 'PASS: Checklist items stored'
        ELSE 'FAIL: Checklist items not stored'
    END AS checklist_test,
    checklist_items
FROM document_summaries 
WHERE document_name = 'test-integration-doc.pdf';

-- 5. Test user-specific data isolation
-- Insert data for a different user to test isolation
INSERT INTO document_summaries (event_id, user_id, document_name, summary, checklist_items)
VALUES (
    1, 2,
    'test-integration-doc.pdf',
    'Different user summary - should not be visible to user 1',
    '["Different user checklist"]'
) ON CONFLICT (event_id, user_id, document_name) DO UPDATE SET
    summary = EXCLUDED.summary,
    checklist_items = EXCLUDED.checklist_items;

-- Verify data isolation
SELECT 
    'User data isolation test:' AS test_section,
    user_id,
    LEFT(summary, 50) || '...' AS summary_preview
FROM document_summaries 
WHERE document_name = 'test-integration-doc.pdf'
ORDER BY user_id;

-- Count should be 2 (one for each user)
SELECT 
    CASE 
        WHEN COUNT(*) = 2 THEN 'PASS: User data properly isolated'
        ELSE 'FAIL: User data not isolated'
    END AS isolation_test
FROM document_summaries 
WHERE document_name = 'test-integration-doc.pdf';

-- 6. Performance check - verify indexes exist
SELECT 
    'Performance check:' AS test_section,
    schemaname,
    indexname,
    tablename
FROM pg_indexes 
WHERE tablename IN ('document_summaries', 'document_questions', 'checklist_templates')
ORDER BY tablename, indexname;

-- 7. Final integration summary
SELECT 
    'INTEGRATION TEST SUMMARY' AS final_report,
    (SELECT COUNT(*) FROM company_settings WHERE ai_prompts IS NOT NULL) AS settings_configured,
    (SELECT COUNT(*) FROM document_summaries WHERE summary LIKE '%TEST PROMPT%') AS summaries_using_custom_prompts,
    (SELECT COUNT(*) FROM document_questions WHERE answer LIKE '%TEST PROMPT%') AS qa_using_custom_prompts,
    (SELECT COUNT(DISTINCT user_id) FROM document_summaries WHERE document_name = 'test-integration-doc.pdf') AS user_isolation_working;

COMMIT;

-- Cleanup test data (uncomment to remove test data)
-- DELETE FROM document_questions WHERE question = 'What are the emergency procedures?';
-- DELETE FROM document_summaries WHERE document_name = 'test-integration-doc.pdf';

SELECT 'Settings-AI Integration Test Complete!' AS final_status;
