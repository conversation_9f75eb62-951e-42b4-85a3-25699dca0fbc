# AlertComm1 Backend API Requirements

## 🚀 **Required API Endpoints**

### 1. **Chat Summarization API**

**Endpoint:** `POST /ai/summarize-chat`

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "eventId": "string",
  "chatText": "string (formatted as 'username: message\\nusername: message')"
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "summary": "string (AI-generated summary of the chat conversation)",
  "eventId": "string",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Response (Error - 400/500):**
```json
{
  "success": false,
  "error": "string (error message)",
  "code": "SUMMARIZATION_FAILED"
}
```

**Implementation Notes:**
- Use OpenAI GPT-3.5/4 API to summarize chat messages
- Prompt example: "Summarize this emergency response chat conversation, highlighting key decisions, status updates, and action items:"
- Limit summary to 200-300 words

---

### 2. **AI Action Generation API**

**Endpoint:** `POST /ai/generate-actions`

**Headers:**
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**Request Body:**
```json
{
  "eventId": "string",
  "userRole": "string (commander|lead|staff|responder)",
  "eventType": "string (response|notification_only)",
  "eventTitle": "string",
  "eventInfo": "string",
  "documents": [
    {
      "name": "string",
      "type": "string",
      "size": "number",
      "content": "string (optional - if you can extract text from documents)"
    }
  ]
}
```

**Response (Success - 200):**
```json
{
  "success": true,
  "actions": [
    "string (action item 1)",
    "string (action item 2)",
    "string (action item 3)"
  ],
  "eventId": "string",
  "userRole": "string",
  "generatedAt": "2024-01-15T10:30:00Z"
}
```

**Response (Error - 400/500):**
```json
{
  "success": false,
  "error": "string (error message)",
  "code": "ACTION_GENERATION_FAILED"
}
```

**Implementation Notes:**
- Use OpenAI API to generate role-specific action items
- Consider user role, event type, and document content
- Return 5-10 actionable items
- Prompt example: "Based on this emergency event and the user's role as {role}, generate specific action items they should complete:"

---

### 3. **Chat Messages API (WebSocket + REST)**

**WebSocket Events:**
```javascript
// Listen for chat messages
socket.on('chat-message', (message) => {
  // message format:
  {
    "id": "string",
    "eventId": "string", 
    "userId": "string",
    "username": "string",
    "text": "string",
    "timestamp": "2024-01-15T10:30:00Z"
  }
});

// Send chat message
socket.emit('chat-message', {
  "eventId": "string",
  "userId": "string", 
  "username": "string",
  "text": "string",
  "timestamp": "2024-01-15T10:30:00Z"
});
```

**REST Endpoint:** `GET /events/{eventId}/chat`

**Response:**
```json
{
  "success": true,
  "messages": [
    {
      "id": "string",
      "eventId": "string",
      "userId": "string", 
      "username": "string",
      "text": "string",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## 🗄️ **Database Schema Updates**

### **Events Table Updates**
```sql
ALTER TABLE events ADD COLUMN event_type VARCHAR(20) DEFAULT 'response';
ALTER TABLE events ADD COLUMN location_update_interval INTEGER DEFAULT 60;
ALTER TABLE events ADD COLUMN notification_channels JSON;
ALTER TABLE events ADD COLUMN event_documents JSON;

-- Add check constraint
ALTER TABLE events ADD CONSTRAINT event_type_check 
CHECK (event_type IN ('response', 'notification_only'));
```

### **Templates Table Updates** 
```sql
ALTER TABLE templates ADD COLUMN event_type VARCHAR(20) DEFAULT 'response';
ALTER TABLE templates ADD COLUMN location_update_interval INTEGER DEFAULT 60;
ALTER TABLE templates ADD COLUMN notification_channels JSON;
ALTER TABLE templates ADD COLUMN event_documents JSON;
```

### **Chat Messages Table (New)**
```sql
CREATE TABLE chat_messages (
  id SERIAL PRIMARY KEY,
  event_id INTEGER REFERENCES events(id),
  user_id INTEGER REFERENCES users(id),
  username VARCHAR(255),
  message_text TEXT NOT NULL,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_chat_messages_event_id ON chat_messages(event_id);
CREATE INDEX idx_chat_messages_timestamp ON chat_messages(timestamp);
```

### **Chat Summaries Table (New)**
```sql
CREATE TABLE chat_summaries (
  id SERIAL PRIMARY KEY,
  event_id INTEGER REFERENCES events(id),
  summary_text TEXT NOT NULL,
  generated_by INTEGER REFERENCES users(id),
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### **Action Items Table (New)**
```sql
CREATE TABLE action_items (
  id SERIAL PRIMARY KEY,
  event_id INTEGER REFERENCES events(id),
  user_id INTEGER REFERENCES users(id),
  action_text TEXT NOT NULL,
  is_completed BOOLEAN DEFAULT FALSE,
  user_role VARCHAR(50),
  generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL
);
```

---

## 🔧 **Implementation Priority**

### **Phase 1 (Essential)**
1. Database schema updates for event_type, location_update_interval, notification_channels
2. Basic chat storage and retrieval (without AI)
3. WebSocket chat functionality

### **Phase 2 (AI Features)**  
1. OpenAI API integration for chat summarization
2. OpenAI API integration for action generation
3. Document storage and processing

### **Phase 3 (Enhancements)**
1. Advanced document text extraction (PDF, Word)
2. Action item completion tracking
3. Chat summary history

---

## 🔑 **OpenAI Integration Guide**

### **Setup:**
1. Get OpenAI API key
2. Install OpenAI SDK: `npm install openai`
3. Set environment variable: `OPENAI_API_KEY=your_key_here`

### **Basic Implementation Example (Node.js):**
```javascript
const OpenAI = require('openai');
const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

// Chat Summarization
async function summarizeChat(chatText) {
  const response = await openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [
      {
        role: "system", 
        content: "You are an expert at summarizing emergency response communications. Provide clear, concise summaries highlighting key decisions and action items."
      },
      {
        role: "user",
        content: `Please summarize this emergency response chat:\n\n${chatText}`
      }
    ],
    max_tokens: 300,
    temperature: 0.3
  });
  
  return response.choices[0].message.content;
}

// Action Generation  
async function generateActions(eventInfo, userRole, eventType) {
  const prompt = `Generate 5-8 specific action items for a ${userRole} in a ${eventType} event:
  
Event: ${eventInfo.title}
Description: ${eventInfo.info}
Type: ${eventType}

Provide actionable, role-specific tasks:`;

  const response = await openai.chat.completions.create({
    model: "gpt-3.5-turbo", 
    messages: [
      { role: "system", content: "You are an emergency response coordinator. Generate specific, actionable tasks." },
      { role: "user", content: prompt }
    ],
    max_tokens: 400,
    temperature: 0.4
  });
  
  const content = response.choices[0].message.content;
  return content.split('\n').filter(line => line.trim()).map(line => line.replace(/^\d+\.\s*/, ''));
}
```

---

## 🧪 **Testing the APIs**

### **Test Chat Summarization:**
```bash
curl -X POST http://localhost:3000/ai/summarize-chat \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "eventId": "123",
    "chatText": "John: We need more supplies at the scene\nSarah: ETA 10 minutes\nMike: Roger, standing by"
  }'
```

### **Test Action Generation:**
```bash
curl -X POST http://localhost:3000/ai/generate-actions \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "eventId": "123",
    "userRole": "commander", 
    "eventType": "response",
    "eventTitle": "Structure Fire",
    "eventInfo": "Multi-story building fire with potential casualties"
  }'
```

Copy this entire specification to your backend developer - it contains everything they need to implement the required APIs! 🚀
