import React from 'react';

const StatCard = ({ title, value, color, icon, onClick }) => {
  return (
    <div 
      onClick={onClick}
      style={{
        border: '1px solid #e0e0e0',
        borderRadius: '8px',
        padding: '15px',
        backgroundColor: '#fff',
        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        overflow: 'hidden',
        position: 'relative',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'transform 0.2s, box-shadow 0.2s',
        '&:hover': {
          transform: onClick ? 'translateY(-3px)' : 'none',
          boxShadow: onClick ? '0 4px 8px rgba(0,0,0,0.1)' : '0 2px 4px rgba(0,0,0,0.05)'
        }
      }}
    >
      {color && (
        <div 
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '5px',
            height: '100%',
            backgroundColor: color
          }}
        />
      )}
      <div style={{ marginLeft: color ? '8px' : '0' }}>
        <div style={{ fontSize: 'var(--font-size-sm)', color: '#757575', marginBottom: '8px' }}>
          {title}
        </div>
        <div style={{ fontSize: 'var(--font-size-2xl)', fontWeight: 'bold', color: '#212121' }}>
          {value}
        </div>
      </div>
      {icon && (
        <div style={{ position: 'absolute', top: '10px', right: '10px', opacity: 0.2 }}>
          {icon}
        </div>
      )}
    </div>
  );
};

export default StatCard;